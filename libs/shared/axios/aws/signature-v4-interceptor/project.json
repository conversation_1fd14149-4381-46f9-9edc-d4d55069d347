{"name": "shared-axios-aws-signature-v4-interceptor", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/axios/aws/signature-v4-interceptor/src", "projectType": "library", "tags": ["shared"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/axios/aws/signature-v4-interceptor/jest.config.ts", "passWithNoTests": false}}}}