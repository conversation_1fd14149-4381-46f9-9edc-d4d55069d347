# FlexibilityRequestsApi

All URIs are relative to _http://localhost_

| Method                                                                                          | HTTP request                                                         | Description                                                                              |
| ----------------------------------------------------------------------------------------------- | -------------------------------------------------------------------- | ---------------------------------------------------------------------------------------- |
| [**asyncCreateFlexibilityRequestForProgramme**](#asynccreateflexibilityrequestforprogramme)     | **POST** /programmes/{programmeId}/flexibility-requests/submit-async | Create flexibility request for all charging stations in a given programme asynchronously |
| [**createChargingStationFlexibilityRequest**](#createchargingstationflexibilityrequest)         | **POST** /charging-stations/{ppid}/flexibility-requests              | Create flexibility request                                                               |
| [**createFlexibilityRequestForProgramme**](#createflexibilityrequestforprogramme)               | **POST** /programmes/{programmeId}/flexibility-requests              | Create flexibility request for all charging stations in a given programme                |
| [**deleteFlexibilityRequest**](#deleteflexibilityrequest)                                       | **DELETE** /flexibility-requests/{id}                                | Delete flexibility request                                                               |
| [**deleteFlexibilityRequestsForChargingStation**](#deleteflexibilityrequestsforchargingstation) | **DELETE** /charging-stations/{ppid}/flexibility-requests            | Delete flexibility requests for charging station                                         |
| [**getActiveChargingStationFlexibilityRequests**](#getactivechargingstationflexibilityrequests) | **GET** /charging-stations/{ppid}/flexibility-requests               | Get active flexibility requests                                                          |
| [**getFlexibilityRequestByProviderInfo**](#getflexibilityrequestbyproviderinfo)                 | **GET** /flexibility-requests                                        | Get flexibility request by provider info                                                 |
| [**searchFlexibilityRequests**](#searchflexibilityrequests)                                     | **GET** /flexibility-requests/search                                 | Search for flexibility requests given criteria                                           |
| [**updateFlexibilityRequest**](#updateflexibilityrequest)                                       | **PUT** /flexibility-requests/{id}                                   | Update flexibility request                                                               |

# **asyncCreateFlexibilityRequestForProgramme**

> asyncCreateFlexibilityRequestForProgramme(createFlexRequestDto)

Create a flexibility request for all charging stations in a given programme asynchronously

### Example

```typescript
import { FlexibilityRequestsApi, Configuration, CreateFlexRequestDto } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let programmeId: string; // (default to undefined)
let createFlexRequestDto: CreateFlexRequestDto; //

const { status, data } = await apiInstance.asyncCreateFlexibilityRequestForProgramme(programmeId, createFlexRequestDto);
```

### Parameters

| Name                     | Type                     | Description | Notes                 |
| ------------------------ | ------------------------ | ----------- | --------------------- |
| **createFlexRequestDto** | **CreateFlexRequestDto** |             |                       |
| **programmeId**          | [**string**]             |             | defaults to undefined |

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: Not defined

### HTTP response details

| Status code | Description             | Response headers |
| ----------- | ----------------------- | ---------------- |
| **202**     | Accepted                | -                |
| **403**     | Programme is not active | -                |
| **404**     |                         | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createChargingStationFlexibilityRequest**

> FlexRequestResponse createChargingStationFlexibilityRequest(createFlexRequestDto)

Create a flexibility request for a given charger

### Example

```typescript
import { FlexibilityRequestsApi, Configuration, CreateFlexRequestDto } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let ppid: string; //The PPID of the charging station (default to undefined)
let createFlexRequestDto: CreateFlexRequestDto; //

const { status, data } = await apiInstance.createChargingStationFlexibilityRequest(ppid, createFlexRequestDto);
```

### Parameters

| Name                     | Type                     | Description                      | Notes                 |
| ------------------------ | ------------------------ | -------------------------------- | --------------------- |
| **createFlexRequestDto** | **CreateFlexRequestDto** |                                  |                       |
| **ppid**                 | [**string**]             | The PPID of the charging station | defaults to undefined |

### Return type

**FlexRequestResponse**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

### HTTP response details

| Status code | Description                       | Response headers |
| ----------- | --------------------------------- | ---------------- |
| **201**     |                                   | -                |
| **400**     | Invalid PPID                      | -                |
| **404**     | Unit not found                    | -                |
| **409**     | Conflict found with existing data | -                |
| **422**     | Ends before it starts             | -                |
| **502**     | Unsupported charging station      | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createFlexibilityRequestForProgramme**

> CreateFlexibilityRequestForProgramme200Response createFlexibilityRequestForProgramme(createFlexRequestDto)

Create a flexibility request for all charging stations in a given programme

### Example

```typescript
import { FlexibilityRequestsApi, Configuration, CreateFlexRequestDto } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let programmeId: string; // (default to undefined)
let createFlexRequestDto: CreateFlexRequestDto; //

const { status, data } = await apiInstance.createFlexibilityRequestForProgramme(programmeId, createFlexRequestDto);
```

### Parameters

| Name                     | Type                     | Description | Notes                 |
| ------------------------ | ------------------------ | ----------- | --------------------- |
| **createFlexRequestDto** | **CreateFlexRequestDto** |             |                       |
| **programmeId**          | [**string**]             |             | defaults to undefined |

### Return type

**CreateFlexibilityRequestForProgramme200Response**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

### HTTP response details

| Status code | Description             | Response headers |
| ----------- | ----------------------- | ---------------- |
| **200**     | OK                      | -                |
| **403**     | Programme is not active | -                |
| **404**     |                         | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteFlexibilityRequest**

> deleteFlexibilityRequest()

Delete a flexibility request given its id

### Example

```typescript
import { FlexibilityRequestsApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.deleteFlexibilityRequest(id);
```

### Parameters

| Name   | Type         | Description | Notes                 |
| ------ | ------------ | ----------- | --------------------- |
| **id** | [**string**] |             | defaults to undefined |

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

### HTTP response details

| Status code | Description | Response headers |
| ----------- | ----------- | ---------------- |
| **204**     |             | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteFlexibilityRequestsForChargingStation**

> deleteFlexibilityRequestsForChargingStation()

Delete all flexibility requests for a given charging station

### Example

```typescript
import { FlexibilityRequestsApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let ppid: string; // (default to undefined)

const { status, data } = await apiInstance.deleteFlexibilityRequestsForChargingStation(ppid);
```

### Parameters

| Name     | Type         | Description | Notes                 |
| -------- | ------------ | ----------- | --------------------- |
| **ppid** | [**string**] |             | defaults to undefined |

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

### HTTP response details

| Status code | Description | Response headers |
| ----------- | ----------- | ---------------- |
| **204**     |             | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getActiveChargingStationFlexibilityRequests**

> Array<FlexRequestResponse> getActiveChargingStationFlexibilityRequests()

Get the active flexibility requests for a given charger Results are limited to the first 1000, either ordered by increasing end times Or by decreasing start times if the includePast query param is set to true

### Example

```typescript
import { FlexibilityRequestsApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let ppid: string; // (default to undefined)
let includePast: boolean; //Include past requests (defaults to false) if \"true\" (optional) (default to undefined)
let includeDeleted: boolean; //Include deleted (canalled) requests (defaults to false) if \"true\" (optional) (default to undefined)
let from: string; //Return only requests starting after this date (optional) (default to undefined)
let to: string; //Return only requests starting before this date (optional) (default to undefined)
let cacheControl: string; //Set to \"refresh\" to force a refresh (optional) (default to undefined)

const { status, data } = await apiInstance.getActiveChargingStationFlexibilityRequests(ppid, includePast, includeDeleted, from, to, cacheControl);
```

### Parameters

| Name               | Type          | Description                                                                   | Notes                            |
| ------------------ | ------------- | ----------------------------------------------------------------------------- | -------------------------------- |
| **ppid**           | [**string**]  |                                                                               | defaults to undefined            |
| **includePast**    | [**boolean**] | Include past requests (defaults to false) if \&quot;true\&quot;               | (optional) defaults to undefined |
| **includeDeleted** | [**boolean**] | Include deleted (canalled) requests (defaults to false) if \&quot;true\&quot; | (optional) defaults to undefined |
| **from**           | [**string**]  | Return only requests starting after this date                                 | (optional) defaults to undefined |
| **to**             | [**string**]  | Return only requests starting before this date                                | (optional) defaults to undefined |
| **cacheControl**   | [**string**]  | Set to \&quot;refresh\&quot; to force a refresh                               | (optional) defaults to undefined |

### Return type

**Array<FlexRequestResponse>**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

### HTTP response details

| Status code | Description                                           | Response headers |
| ----------- | ----------------------------------------------------- | ---------------- |
| **200**     | All active flexibility requests for the given charger | -                |
| **404**     | Unit not found                                        | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getFlexibilityRequestByProviderInfo**

> FlexRequestResponse getFlexibilityRequestByProviderInfo()

Get a flexibility request given it\'s external provider info

### Example

```typescript
import { FlexibilityRequestsApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let providerName: string; //The name of the competition provider (default to undefined)
let providerFlexRequestId: string; //The provider\'s id for this flex request (default to undefined)
let ppid: string; // (default to undefined)

const { status, data } = await apiInstance.getFlexibilityRequestByProviderInfo(providerName, providerFlexRequestId, ppid);
```

### Parameters

| Name                      | Type         | Description                                  | Notes                 |
| ------------------------- | ------------ | -------------------------------------------- | --------------------- |
| **providerName**          | [**string**] | The name of the competition provider         | defaults to undefined |
| **providerFlexRequestId** | [**string**] | The provider\&#39;s id for this flex request | defaults to undefined |
| **ppid**                  | [**string**] |                                              | defaults to undefined |

### Return type

**FlexRequestResponse**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

### HTTP response details

| Status code | Description                    | Response headers |
| ----------- | ------------------------------ | ---------------- |
| **200**     | The flexibility request        | -                |
| **404**     | Competition provider not found | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchFlexibilityRequests**

> FlexRequestSearchResponse searchFlexibilityRequests()

Search for flexibility requests given criteria

### Example

```typescript
import { FlexibilityRequestsApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let startAt: string; //The flex request start date/time (default to undefined)

const { status, data } = await apiInstance.searchFlexibilityRequests(startAt);
```

### Parameters

| Name        | Type         | Description                      | Notes                 |
| ----------- | ------------ | -------------------------------- | --------------------- |
| **startAt** | [**string**] | The flex request start date/time | defaults to undefined |

### Return type

**FlexRequestSearchResponse**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

### HTTP response details

| Status code | Description     | Response headers |
| ----------- | --------------- | ---------------- |
| **200**     | Search response | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateFlexibilityRequest**

> FlexRequestResponse updateFlexibilityRequest(updateFlexRequestDto)

Update a flexibility request given its id

### Example

```typescript
import { FlexibilityRequestsApi, Configuration, UpdateFlexRequestDto } from './api';

const configuration = new Configuration();
const apiInstance = new FlexibilityRequestsApi(configuration);

let id: string; //The id of the flexibility request (default to undefined)
let updateFlexRequestDto: UpdateFlexRequestDto; //

const { status, data } = await apiInstance.updateFlexibilityRequest(id, updateFlexRequestDto);
```

### Parameters

| Name                     | Type                     | Description                       | Notes                 |
| ------------------------ | ------------------------ | --------------------------------- | --------------------- |
| **updateFlexRequestDto** | **UpdateFlexRequestDto** |                                   |                       |
| **id**                   | [**string**]             | The id of the flexibility request | defaults to undefined |

### Return type

**FlexRequestResponse**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

### HTTP response details

| Status code | Description                     | Response headers |
| ----------- | ------------------------------- | ---------------- |
| **201**     | The updated flexibility request | -                |
| **404**     | Flexibility request not found   | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
