# FlexRequestProviderDto

## Properties

| Name                      | Type       | Description                                  | Notes                  |
| ------------------------- | ---------- | -------------------------------------------- | ---------------------- |
| **name**                  | **string** | The name of the provider                     | [default to undefined] |
| **externalFlexRequestId** | **string** | The provider\&#39;s id for this flex request | [default to undefined] |

## Example

```typescript
import { FlexRequestProviderDto } from './api';

const instance: FlexRequestProviderDto = {
  name,
  externalFlexRequestId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
