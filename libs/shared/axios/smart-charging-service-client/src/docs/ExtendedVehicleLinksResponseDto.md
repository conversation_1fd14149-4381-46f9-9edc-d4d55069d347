# ExtendedVehicleLinksResponseDto

## Properties

| Name     | Type                                                                                 | Description | Notes                  |
| -------- | ------------------------------------------------------------------------------------ | ----------- | ---------------------- |
| **data** | [**Array&lt;ExtendedVehicleLinkResponseDto&gt;**](ExtendedVehicleLinkResponseDto.md) |             | [default to undefined] |

## Example

```typescript
import { ExtendedVehicleLinksResponseDto } from './api';

const instance: ExtendedVehicleLinksResponseDto = {
  data,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
