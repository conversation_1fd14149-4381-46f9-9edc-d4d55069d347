# VehicleLinkResponseDto

## Properties

| Name                         | Type                                                                  | Description | Notes                  |
| ---------------------------- | --------------------------------------------------------------------- | ----------- | ---------------------- |
| **id**                       | **string**                                                            |             | [default to undefined] |
| **isPluggedInToThisCharger** | **boolean**                                                           |             | [default to undefined] |
| **vehicle**                  | [**VehicleLinkResponseDtoVehicle**](VehicleLinkResponseDtoVehicle.md) |             | [default to undefined] |
| **intents**                  | [**VehicleIntentsResponseDto**](VehicleIntentsResponseDto.md)         |             | [default to undefined] |

## Example

```typescript
import { VehicleLinkResponseDto } from './api';

const instance: VehicleLinkResponseDto = {
  id,
  isPluggedInToThisCharger,
  vehicle,
  intents,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
