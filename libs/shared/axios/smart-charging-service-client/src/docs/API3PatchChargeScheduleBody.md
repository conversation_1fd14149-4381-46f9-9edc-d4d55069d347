# API3PatchChargeScheduleBody

## Properties

| Name           | Type                                                | Description | Notes                             |
| -------------- | --------------------------------------------------- | ----------- | --------------------------------- |
| **start_day**  | **number**                                          |             | [optional] [default to undefined] |
| **start_time** | **object**                                          |             | [optional] [default to undefined] |
| **end_day**    | **number**                                          |             | [optional] [default to undefined] |
| **end_time**   | **object**                                          |             | [optional] [default to undefined] |
| **status**     | [**ChargeScheduleStatus**](ChargeScheduleStatus.md) |             | [optional] [default to undefined] |

## Example

```typescript
import { API3PatchChargeScheduleBody } from './api';

const instance: API3PatchChargeScheduleBody = {
  start_day,
  start_time,
  end_day,
  end_time,
  status,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
