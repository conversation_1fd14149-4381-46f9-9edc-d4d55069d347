import { ConfigModule } from '@nestjs/config';
import { DynamicModule, Module } from '@nestjs/common';
import { StripeAccountService } from './account/account.service';
import { StripeChargeService } from './charge/charge.service';
import { StripeConfig } from './stripe.config';
import { StripeConnectService } from './connect/connect.service';
import { StripeCustomerService } from './customer/customer.service';
import { StripeInvoiceService } from './invoice/invoice.service';
import { StripePaymentIntentService } from './payment/payment.service';
import { StripePricesService } from './prices/prices.service';
import { StripeSetupIntentService } from './setup-intent/intent.service';
import { StripeSubscriptionService } from './subscription/subscription.service';
import { StripeWebhookService } from './stripe-webhook/stripe.webhook.service';
import Stripe from 'stripe';

@Module({})
export class StripeModule {
  static register(apiKey: string, config: StripeConfig): DynamicModule {
    return {
      module: StripeModule,
      imports: [ConfigModule],
      providers: [
        StripeAccountService,
        StripeChargeService,
        StripeCustomerService,
        StripeConnectService,
        StripeInvoiceService,
        StripeSetupIntentService,
        StripeSubscriptionService,
        StripePaymentIntentService,
        StripePricesService,
        StripeWebhookService,
        {
          provide: Stripe,
          useValue: new Stripe(apiKey, config),
        },
      ],
      exports: [
        StripeAccountService,
        StripeChargeService,
        StripeCustomerService,
        StripeConnectService,
        StripeInvoiceService,
        StripeSetupIntentService,
        StripeSubscriptionService,
        StripePaymentIntentService,
        StripePricesService,
        StripeWebhookService,
      ],
    };
  }
}
