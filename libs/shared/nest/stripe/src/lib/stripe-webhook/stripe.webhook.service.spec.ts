import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { StripeWebhookService } from './stripe.webhook.service';
import { Test, TestingModule } from '@nestjs/testing';
import { mockDeep } from 'jest-mock-extended';
import Stripe from 'stripe';
import mockEvent from '../__fixtures__/payment.intent.event';

const mockConstructEvent = jest.fn();

jest.mock('stripe', () =>
  jest.fn().mockImplementation(() => ({
    webhooks: {
      // @ts-expect-error mock
      constructEvent: (body, sig, secret) =>
        mockConstructEvent(body, sig, secret),
    },
  }))
);
describe('StripePaymentIntentService', () => {
  let service: StripeWebhookService;
  const endpointSecret = 'A_SECRET';
  const stripeSignature = 'sadasdasd';
  const logger = mockDeep<Logger>();
  process.env.STRIPE_WEBHOOK_SIGNING_SECRET = endpointSecret;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigService,
        StripeWebhookService,
        {
          provide: Stripe,
          useValue: new Stripe('test', {}),
        },
      ],
    }).compile();

    module.useLogger(logger);

    service = module.get<StripeWebhookService>(StripeWebhookService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should throw an error when no body is passed', () => {
    const body = '';
    expect(() => service.processEvent(body, stripeSignature)).toThrow(
      new Error('body is not defined')
    );
    expect(logger.error).toHaveBeenCalledWith(
      { key: 'body', param: '' },
      'body is not defined',
      StripeWebhookService.name
    );
  });

  it('should throw an error when no signature is passed', () => {
    const body = 'asdasdasasd';
    expect(() => service.processEvent(body, '')).toThrow(
      new Error('stripeSignature is not defined')
    );
    expect(logger.error).toHaveBeenCalledWith(
      { key: 'stripeSignature', param: '' },
      'stripeSignature is not defined',
      StripeWebhookService.name
    );
  });

  it('should throw an error when event failed to process', async () => {
    const error = new Error(
      'Unable to extract timestamp and signatures from header'
    );
    mockConstructEvent.mockImplementation(() => {
      throw error;
    });
    const stripeSigningSecret = 'sadasdasd';
    const body = 'asdasdasasd';
    expect(() => service.processEvent(body, stripeSigningSecret)).toThrow(
      error
    );

    expect(logger.error).toHaveBeenCalledWith(
      { error },
      '⚠️  Webhook signature verification failed.',
      StripeWebhookService.name
    );
  });

  it('should return an event', () => {
    const paymentIntentEvent = mockEvent;
    mockConstructEvent.mockImplementation(() => paymentIntentEvent);
    const event = service.processEvent(
      JSON.stringify(paymentIntentEvent),
      stripeSignature
    );
    expect(event).toEqual(paymentIntentEvent);
  });

  it('should allow the secret to be passed in', () => {
    const paymentIntentEvent = mockEvent;
    mockConstructEvent.mockImplementation(() => paymentIntentEvent);
    const event = service.processEvent(
      JSON.stringify(paymentIntentEvent),
      stripeSignature,
      'B_SECRET'
    );

    expect(event).toEqual(paymentIntentEvent);
    expect(mockConstructEvent).toHaveBeenCalledWith(
      JSON.stringify(paymentIntentEvent),
      stripeSignature,
      'B_SECRET'
    );
  });
});
