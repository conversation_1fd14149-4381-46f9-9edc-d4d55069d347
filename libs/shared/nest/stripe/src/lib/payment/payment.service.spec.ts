import { CapturePaymentIntentResponse } from '../types/capture-payment-response';
import { CapturePaymentRequest } from '../types/capture-payment-request';
import { CreatePaymentRequest } from '../types/create-payment-request';
import { RetrievePaymentResponse } from '../types/retrieve-payment-intent-response';
import { StripeAmountTooLargeException } from './payment.exception';
import { StripePaymentIntentService } from './payment.service';
import { Test, TestingModule } from '@nestjs/testing';
import Stripe from 'stripe';

const TEST_CUSTOMER_ID = 'cus_NggdGfEfND3Bfs';
const CLIENT_SECRET_PAYMENT_INTENT = 'weweffwe';
const EPHEMERAL_KEY = 'xxxxx';
const request: CreatePaymentRequest = {
  amount: 80,
  currency: 'gbp',
};
const paymentIntentId = 'pi_123';

const mockCreateKey = jest.fn();
const mockCreatePaymentIntent = jest.fn();
const mockCapturePaymentIntent = jest.fn();
const mockRetrievePaymentIntent = jest.fn();
const mockCancelPaymentIntent = jest.fn();

jest.mock('stripe', () =>
  jest.fn().mockImplementation(() => ({
    ephemeralKeys: {
      // @ts-expect-error mock
      create: (request, options) => mockCreateKey(request, options),
    },
    paymentIntents: {
      // @ts-expect-error mock
      create: (request) => mockCreatePaymentIntent(request),
      capture: (
        paymentIntentId: string,
        paymentIntentRequest: CapturePaymentRequest
      ) => mockCapturePaymentIntent(paymentIntentId, paymentIntentRequest),
      retrieve: (paymentIntentId: string) =>
        mockRetrievePaymentIntent(paymentIntentId),
      cancel: (
        paymentIntentId: string,
        cancellationReason: Stripe.PaymentIntentCancelParams.CancellationReason
      ) => mockCancelPaymentIntent(paymentIntentId, cancellationReason),
    },
  }))
);
describe('StripePaymentIntentService', () => {
  let service: StripePaymentIntentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StripePaymentIntentService,
        {
          provide: Stripe,
          useValue: new Stripe('test', {}),
        },
      ],
    }).compile();

    service = module.get<StripePaymentIntentService>(
      StripePaymentIntentService
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createGuestPaymentIntent', () => {
    it('should create an intent for the customer', async () => {
      mockCreatePaymentIntent.mockResolvedValue({
        client_secret: CLIENT_SECRET_PAYMENT_INTENT,
      });

      const res = await service.createGuestPaymentIntent(request);

      expect(res).toStrictEqual({
        paymentIntent: CLIENT_SECRET_PAYMENT_INTENT,
      });

      expect(mockCreatePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCreatePaymentIntent).toHaveBeenCalledWith({
        amount: request.amount * 100,
        currency: request.currency,
        metadata: {},
        customer: undefined,
        automatic_payment_methods: {
          enabled: true,
        },
      });
    });

    it('should bubble up any error thrown by Stripe', async () => {
      const error = new Error();

      mockCreatePaymentIntent.mockImplementation(() => {
        throw error;
      });

      await expect(service.createGuestPaymentIntent(request)).rejects.toThrow(
        error
      );

      expect(mockCreatePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCreatePaymentIntent).toHaveBeenCalledWith({
        amount: request.amount * 100,
        currency: request.currency,
        metadata: {},
        customer: undefined,
        automatic_payment_methods: {
          enabled: true,
        },
      });
    });
  });

  describe('createRegisteredUserPaymentIntent', () => {
    it('should create an intent for a customer', async () => {
      mockCreateKey.mockResolvedValueOnce({ secret: EPHEMERAL_KEY });
      mockCreatePaymentIntent.mockResolvedValueOnce({
        client_secret: CLIENT_SECRET_PAYMENT_INTENT,
      });

      expect(
        await service.createRegisteredUserPaymentIntent(
          request,
          TEST_CUSTOMER_ID
        )
      ).toEqual({
        customer: TEST_CUSTOMER_ID,
        ephemeralKey: EPHEMERAL_KEY,
        paymentIntent: CLIENT_SECRET_PAYMENT_INTENT,
      });

      expect(mockCreateKey).toHaveBeenCalledWith(
        {
          customer: TEST_CUSTOMER_ID,
        },
        { apiVersion: '2024-06-20' }
      );

      expect(mockCreatePaymentIntent).toHaveBeenCalledWith({
        amount: request.amount * 100,
        currency: request.currency,
        customer: TEST_CUSTOMER_ID,
        automatic_payment_methods: {
          enabled: true,
        },
        metadata: {},
        payment_method_options: undefined,
      });
    });

    it('should create an intent for a customer with metadata', async () => {
      mockCreateKey.mockResolvedValueOnce({ secret: EPHEMERAL_KEY });
      mockCreatePaymentIntent.mockResolvedValueOnce({
        client_secret: CLIENT_SECRET_PAYMENT_INTENT,
      });

      expect(
        await service.createRegisteredUserPaymentIntent(
          request,
          TEST_CUSTOMER_ID,
          {
            origin: 'billing-api',
            product: 'PAYG Top Up',
          },
          {
            card: {
              request_three_d_secure: 'challenge',
            },
          }
        )
      ).toEqual({
        customer: TEST_CUSTOMER_ID,
        ephemeralKey: EPHEMERAL_KEY,
        paymentIntent: CLIENT_SECRET_PAYMENT_INTENT,
      });

      expect(mockCreateKey).toHaveBeenCalledWith(
        {
          customer: TEST_CUSTOMER_ID,
        },
        { apiVersion: '2024-06-20' }
      );

      expect(mockCreatePaymentIntent).toHaveBeenCalledWith({
        amount: request.amount * 100,
        currency: request.currency,
        customer: TEST_CUSTOMER_ID,
        automatic_payment_methods: {
          enabled: true,
        },
        metadata: {
          origin: 'billing-api',
          product: 'PAYG Top Up',
        },
        payment_method_options: {
          card: {
            request_three_d_secure: 'challenge',
          },
        },
      });
    });

    it.each([
      { amount: 5.01, expected: 501 },
      { amount: 5.09, expected: 509 },
      { amount: 5.1, expected: 510 },
      { amount: 5.11, expected: 511 },
      { amount: 5.99, expected: 599 },
    ])(
      'should correctly convert amount to the lowest currency unit ($amount to $expected)',
      async ({ amount, expected }) => {
        mockCreateKey.mockResolvedValueOnce({ secret: EPHEMERAL_KEY });
        mockCreatePaymentIntent.mockResolvedValueOnce({
          client_secret: CLIENT_SECRET_PAYMENT_INTENT,
        });

        expect(
          await service.createRegisteredUserPaymentIntent(
            { ...request, amount },
            TEST_CUSTOMER_ID
          )
        ).toEqual({
          customer: TEST_CUSTOMER_ID,
          ephemeralKey: EPHEMERAL_KEY,
          paymentIntent: CLIENT_SECRET_PAYMENT_INTENT,
        });

        expect(mockCreateKey).toHaveBeenCalledWith(
          {
            customer: TEST_CUSTOMER_ID,
          },
          { apiVersion: '2024-06-20' }
        );

        expect(mockCreatePaymentIntent).toHaveBeenCalledWith({
          amount: expected,
          currency: request.currency,
          customer: TEST_CUSTOMER_ID,
          automatic_payment_methods: {
            enabled: true,
          },
          metadata: {},
          payment_method_options: undefined,
        });
      }
    );
  });

  describe('capturePaymentIntent', () => {
    it('should capture a payment intent', async () => {
      mockCapturePaymentIntent.mockResolvedValue({
        status: 'succeeded',
        amount_received: 3000,
        currency: 'gbp',
      });

      const res: CapturePaymentIntentResponse =
        await service.capturePaymentIntent({
          paymentIntentId: paymentIntentId,
          amount: 1000,
        });

      expect(res).toStrictEqual({
        paymentIntentStatus: 'succeeded',
        amountReceived: 3000,
        currency: 'gbp',
      });

      expect(mockCapturePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCapturePaymentIntent).toHaveBeenCalledWith(paymentIntentId, {
        amount_to_capture: 1000,
      });
    });

    it('should capture a payment intent with metadata', async () => {
      mockCapturePaymentIntent.mockResolvedValue({
        status: 'succeeded',
        amount_received: 3000,
        currency: 'gbp',
      });

      const res: CapturePaymentIntentResponse =
        await service.capturePaymentIntent({
          paymentIntentId: paymentIntentId,
          amount: 1000,
          metadata: {
            origin: 'billing-api',
          },
        });

      expect(res).toStrictEqual({
        paymentIntentStatus: 'succeeded',
        amountReceived: 3000,
        currency: 'gbp',
      });

      expect(mockCapturePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCapturePaymentIntent).toHaveBeenCalledWith(paymentIntentId, {
        amount_to_capture: 1000,
        metadata: {
          origin: 'billing-api',
        },
      });
    });

    it('should bubble up any error thrown by Stripe', async () => {
      const error = new Error();

      mockCapturePaymentIntent.mockImplementation(() => {
        throw error;
      });

      await expect(
        service.capturePaymentIntent({
          paymentIntentId: paymentIntentId,
          amount: 1000,
        })
      ).rejects.toThrow(error);

      expect(mockCapturePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCapturePaymentIntent).toHaveBeenCalledWith(paymentIntentId, {
        amount_to_capture: 1000,
      });
    });

    it('should throw an amount too large exception', async () => {
      const error = {
        code: 'amount_too_large',
      };

      mockCapturePaymentIntent.mockImplementation(() => {
        throw error;
      });

      await expect(
        service.capturePaymentIntent({
          paymentIntentId: paymentIntentId,
          amount: 1000,
        })
      ).rejects.toThrow(new StripeAmountTooLargeException());

      expect(mockCapturePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCapturePaymentIntent).toHaveBeenCalledWith(paymentIntentId, {
        amount_to_capture: 1000,
      });
    });
  });

  describe('retrievePaymentIntent', () => {
    it('should retrieve a payment intent', async () => {
      mockRetrievePaymentIntent.mockResolvedValue({
        status: 'succeeded',
        amount_capturable: 1000,
      });

      const res: RetrievePaymentResponse = await service.retrievePaymentIntent(
        paymentIntentId
      );

      expect(res).toStrictEqual({
        paymentIntentStatus: 'succeeded',
        amountCapturable: 1000,
      });

      expect(mockRetrievePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockRetrievePaymentIntent).toHaveBeenCalledWith(paymentIntentId);
    });
    it('should bubble up any error thrown by Stripe', async () => {
      const error = new Error();

      mockRetrievePaymentIntent.mockImplementation(() => {
        throw error;
      });

      await expect(
        service.retrievePaymentIntent(paymentIntentId)
      ).rejects.toThrow(error);

      expect(mockRetrievePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockRetrievePaymentIntent).toHaveBeenCalledWith(paymentIntentId);
    });
  });

  describe('cancelPaymentIntent', () => {
    it('should cancel a payment intent successfully', async () => {
      const cancellationReason = 'abandoned';
      mockCancelPaymentIntent.mockResolvedValue({
        status: 'canceled',
      });

      const res = await service.cancelPaymentIntent({
        paymentIntentId: 'pi_123',
        cancellationReason,
      });

      expect(res).toStrictEqual({
        paymentIntentStatus: 'canceled',
      });
    });
    it('should throw an error if there is no payment intent returned', async () => {
      const cancellationReason = 'abandoned';
      mockCancelPaymentIntent.mockResolvedValue(null);

      await expect(
        service.cancelPaymentIntent({
          paymentIntentId: 'pi_123',
          cancellationReason,
        })
      ).rejects.toThrow(
        'Failed to cancel payment intent. No payment intent returned.'
      );
    });
    it('should throw an error if the payment intent status is not canceled', async () => {
      const cancellationReason = 'abandoned';
      mockCancelPaymentIntent.mockResolvedValue({
        status: 'requires_capture',
      });

      await expect(
        service.cancelPaymentIntent({
          paymentIntentId: 'pi_123',
          cancellationReason,
        })
      ).rejects.toThrow(
        'Failed to cancel payment intent. Current status: requires_capture'
      );
    });
    it('should bubble up any error thrown by Stripe', async () => {
      const cancellationReason = 'abandoned';
      const error = new Error();

      mockCancelPaymentIntent.mockImplementation(() => {
        throw error;
      });

      await expect(
        service.cancelPaymentIntent({
          paymentIntentId: 'pi_123',
          cancellationReason,
        })
      ).rejects.toThrow(error);

      expect(mockCancelPaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCancelPaymentIntent).toHaveBeenCalledWith('pi_123', {
        cancellation_reason: cancellationReason,
      });
    });
  });
});
