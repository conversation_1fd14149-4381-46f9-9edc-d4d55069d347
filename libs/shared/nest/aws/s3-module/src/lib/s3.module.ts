import { Module } from '@nestjs/common';
import { S3Client } from '@aws-sdk/client-s3';
import { S3Service } from './s3.service';

@Module({
  controllers: [],
  providers: [
    S3Service,
    {
      provide: S3Client,
      useValue: new S3Client({
        region: 'eu-west-1',
        forcePathStyle: process.env.ENVIRONMENT === 'local',
      }),
    },
  ],
  exports: [S3Service],
})
export class S3Module {}
