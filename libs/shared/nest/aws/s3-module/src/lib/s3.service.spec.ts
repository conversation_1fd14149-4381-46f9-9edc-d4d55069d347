import {
  DeleteObjectCommand,
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { ReadStream, createReadStream, readFileSync } from 'fs';
import { S3Service } from './s3.service';
import { Test, TestingModule } from '@nestjs/testing';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { mockClient } from 'aws-sdk-client-mock';
import { sdkStreamMixin } from '@smithy/util-stream';
import path from 'path';

jest.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: jest.fn().mockResolvedValue('https://example.com'),
}));

describe('S3Service', () => {
  let service: S3Service;
  let imageToRead: ReadStream;
  let imageToUpload: Buffer;
  const imagePath = path.resolve(__dirname, './__fixtures__/cc.png');
  const fileName = 'cc1.png';
  const s3Mock = mockClient(S3Client);
  const bucketName = 'myBucket';

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3Service,
        {
          provide: S3Client,
          useValue: s3Mock,
        },
      ],
    }).compile();

    imageToRead = createReadStream(imagePath);
    imageToUpload = readFileSync(imagePath);

    service = module.get<S3Service>(S3Service);
  });

  beforeEach(() => {
    s3Mock.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('a file should be uploaded successfully', async () => {
    s3Mock.on(PutObjectCommand).resolves({});
    const resp = await service.uploadFile(bucketName, imageToUpload, fileName);

    expect(resp).toBeTruthy();
  });

  it('a file should be downloaded successfully', async () => {
    const sdkStream = sdkStreamMixin(imageToRead);
    s3Mock.on(GetObjectCommand).resolves({ Body: sdkStream });
    const resp = await service.getFile(bucketName, fileName);

    expect(resp).toBeTruthy();
  });

  it('a file should be deleted successfully', async () => {
    s3Mock.on(DeleteObjectCommand).resolves({});
    const resp = await service.deleteFile(bucketName, fileName);

    expect(resp).toBeTruthy();
  });

  it('gets pre-signed url for the given file', async () => {
    const getMock = { Body: sdkStreamMixin(imageToRead) };

    s3Mock.on(GetObjectCommand).resolves(getMock);

    const resp = await service.getSignedUrl(bucketName, fileName, 3600);

    expect(getSignedUrl).toHaveBeenCalledTimes(1);
    expect(getSignedUrl).toHaveBeenCalledWith(
      s3Mock,
      expect.any(GetObjectCommand),
      { expiresIn: 3600 }
    );
    expect(resp).toEqual('https://example.com');
  });
});
