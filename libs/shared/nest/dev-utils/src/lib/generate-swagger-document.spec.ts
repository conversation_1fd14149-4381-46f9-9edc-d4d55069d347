import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import {
  afterAll,
  beforeEach,
  describe,
  expect,
  it,
  jest,
} from '@jest/globals';
import { generateSwaggerDocument } from './generate-swagger-document';
import { writeFile } from 'node:fs/promises';
import YAML from 'yaml';

const ogEnv = { ...process.env };

jest.mock('@nestjs/core');
jest.mock('@nestjs/swagger', () => ({
  ...(jest.requireActual('@nestjs/swagger') as object),
  SwaggerModule: {
    createDocument: jest.fn(),
  },
}));

jest.mock('node:fs/promises');

describe('generateSwaggerDocument', () => {
  beforeEach(() => {
    jest.spyOn(process, 'exit').mockImplementation(() => ({} as never));
    process.env = { ...ogEnv };
  });

  afterAll(() => {
    jest.resetAllMocks();
    process.env = ogEnv;
  });

  it('should not write if filePath is not set is true', async () => {
    process.env.NX_DRY_RUN = 'true';

    await generateSwaggerDocument(class {}, () => new DocumentBuilder());
    expect(writeFile).not.toHaveBeenCalled();
  });

  it('should write if filePath is set', async () => {
    await generateSwaggerDocument(
      class {},
      () => new DocumentBuilder(),
      __dirname
    );
    expect(writeFile).toHaveBeenCalled();
  });

  it('should write files', async () => {
    jest
      .spyOn(SwaggerModule, 'createDocument')
      .mockReturnValue({} as ReturnType<typeof SwaggerModule.createDocument>);

    await generateSwaggerDocument(
      class {},
      () => new DocumentBuilder(),
      'path'
    );
    expect(writeFile).toHaveBeenCalledTimes(1);
    expect(writeFile).toHaveBeenCalledWith(
      'path/openapi3.yaml',
      YAML.stringify({})
    );
  });
});
