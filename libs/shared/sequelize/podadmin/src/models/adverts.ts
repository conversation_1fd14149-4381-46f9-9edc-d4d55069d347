import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { PodLocations, PodLocationsId } from './pod-locations';

export interface AdvertsAttributes {
  id: number;
  image: string;
  url: string;
  createdAt: Date;
  updatedAt: Date;
}

export type AdvertsPk = 'id';
export type AdvertsId = Adverts[AdvertsPk];
export type AdvertsOptionalAttributes = 'id' | 'createdAt' | 'updatedAt';
export type AdvertsCreationAttributes = Optional<
  AdvertsAttributes,
  AdvertsOptionalAttributes
>;

export class Adverts
  extends Model<AdvertsAttributes, AdvertsCreationAttributes>
  implements AdvertsAttributes
{
  id!: number;
  image!: string;
  url!: string;
  createdAt!: Date;
  updatedAt!: Date;

  // Adverts hasMany PodLocations via advertId
  podLocations!: PodLocations[];
  getPodLocations!: Sequelize.HasManyGetAssociationsMixin<PodLocations>;
  setPodLocations!: Sequelize.HasManySetAssociationsMixin<
    PodLocations,
    PodLocationsId
  >;
  addPodLocation!: Sequelize.HasManyAddAssociationMixin<
    PodLocations,
    PodLocationsId
  >;
  addPodLocations!: Sequelize.HasManyAddAssociationsMixin<
    PodLocations,
    PodLocationsId
  >;
  createPodLocation!: Sequelize.HasManyCreateAssociationMixin<PodLocations>;
  removePodLocation!: Sequelize.HasManyRemoveAssociationMixin<
    PodLocations,
    PodLocationsId
  >;
  removePodLocations!: Sequelize.HasManyRemoveAssociationsMixin<
    PodLocations,
    PodLocationsId
  >;
  hasPodLocation!: Sequelize.HasManyHasAssociationMixin<
    PodLocations,
    PodLocationsId
  >;
  hasPodLocations!: Sequelize.HasManyHasAssociationsMixin<
    PodLocations,
    PodLocationsId
  >;
  countPodLocations!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof Adverts {
    return Adverts.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        image: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        url: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'updated_at',
        },
      },
      {
        sequelize,
        tableName: 'adverts',
        timestamps: true,
        paranoid: false,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
        ],
      }
    );
  }
}
