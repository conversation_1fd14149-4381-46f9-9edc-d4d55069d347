import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Users, UsersId } from './users';
import type { VehicleModels, VehicleModelsId } from './vehicle-models';

export interface VehicleModelUserAttributes {
  id: number;
  vehicleModelId: number;
  userId: number;
}

export type VehicleModelUserPk = 'id';
export type VehicleModelUserId = VehicleModelUser[VehicleModelUserPk];
export type VehicleModelUserOptionalAttributes = 'id';
export type VehicleModelUserCreationAttributes = Optional<
  VehicleModelUserAttributes,
  VehicleModelUserOptionalAttributes
>;

export class VehicleModelUser
  extends Model<VehicleModelUserAttributes, VehicleModelUserCreationAttributes>
  implements VehicleModelUserAttributes
{
  id!: number;
  vehicleModelId!: number;
  userId!: number;

  // VehicleModelUser belongsTo Users via userId
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;
  // VehicleModelUser belongsTo VehicleModels via vehicleModelId
  vehicleModel!: VehicleModels;
  getVehicleModel!: Sequelize.BelongsToGetAssociationMixin<VehicleModels>;
  setVehicleModel!: Sequelize.BelongsToSetAssociationMixin<
    VehicleModels,
    VehicleModelsId
  >;
  createVehicleModel!: Sequelize.BelongsToCreateAssociationMixin<VehicleModels>;

  static initModel(sequelize: Sequelize.Sequelize): typeof VehicleModelUser {
    return VehicleModelUser.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        vehicleModelId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'vehicle_models',
            key: 'id',
          },
          field: 'vehicle_model_id',
        },
        userId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
          field: 'user_id',
        },
      },
      {
        sequelize,
        tableName: 'vehicle_model_user',
        timestamps: false,
        paranoid: false,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
          {
            name: 'vehicle_model_user_vehicle_model_id_foreign',
            using: 'BTREE',
            fields: [{ name: 'vehicle_model_id' }],
          },
          {
            name: 'vehicle_model_user_user_id_foreign',
            using: 'BTREE',
            fields: [{ name: 'user_id' }],
          },
        ],
      }
    );
  }
}
