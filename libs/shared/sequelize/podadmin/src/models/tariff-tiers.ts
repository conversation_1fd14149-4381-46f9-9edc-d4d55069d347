import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Tariffs, TariffsId } from './tariffs';

export interface TariffTiersAttributes {
  id: number;
  rate: number;
  beginTime?: string;
  beginDay?: number;
  endTime?: string;
  endDay?: number;
  tariffId: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export type TariffTiersPk = 'id';
export type TariffTiersId = TariffTiers[TariffTiersPk];
export type TariffTiersOptionalAttributes =
  | 'id'
  | 'beginTime'
  | 'beginDay'
  | 'endTime'
  | 'endDay'
  | 'createdAt'
  | 'updatedAt'
  | 'deletedAt';
export type TariffTiersCreationAttributes = Optional<
  TariffTiersAttributes,
  TariffTiersOptionalAttributes
>;

export class TariffTiers
  extends Model<TariffTiersAttributes, TariffTiersCreationAttributes>
  implements TariffTiersAttributes
{
  id!: number;
  rate!: number;
  beginTime?: string;
  beginDay?: number;
  endTime?: string;
  endDay?: number;
  tariffId!: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;

  // TariffTiers belongsTo Tariffs via tariffId
  tariff!: Tariffs;
  getTariff!: Sequelize.BelongsToGetAssociationMixin<Tariffs>;
  setTariff!: Sequelize.BelongsToSetAssociationMixin<Tariffs, TariffsId>;
  createTariff!: Sequelize.BelongsToCreateAssociationMixin<Tariffs>;

  static initModel(sequelize: Sequelize.Sequelize): typeof TariffTiers {
    return TariffTiers.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        rate: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
        },
        beginTime: {
          type: DataTypes.TIME,
          allowNull: true,
          field: 'begin_time',
        },
        beginDay: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
          field: 'begin_day',
        },
        endTime: {
          type: DataTypes.TIME,
          allowNull: true,
          field: 'end_time',
        },
        endDay: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
          field: 'end_day',
        },
        tariffId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'tariffs',
            key: 'id',
          },
          field: 'tariff_id',
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'updated_at',
        },
        deletedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'deleted_at',
        },
      },
      {
        sequelize,
        tableName: 'tariff_tiers',
        timestamps: true,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
          {
            name: 'tariff_tiers_tariff_id_foreign',
            using: 'BTREE',
            fields: [{ name: 'tariff_id' }],
          },
        ],
      }
    );
  }
}
