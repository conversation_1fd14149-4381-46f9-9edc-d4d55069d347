import {
  GroupLocation,
  GroupTypes,
  ParkingCharges,
  ParkingOpeningTimeNotes,
  ParkingOpeningTimes,
  PodConnectors,
  PodDoors,
  PodLocationUnit,
  PodLocations,
  PodModels,
  PodStatuses,
  PodUnitConnector,
  PodUnits,
  TariffTiers,
  Tariffs,
} from '../models/init-models';
import { Groups } from '../models/groups';
import { Includeable } from 'sequelize/types/model';

export const podAddressesShallowIncludeOptions = [
  { model: Groups, as: 'group' },
  {
    model: PodLocations,
    as: 'podLocations',
    include: [
      {
        model: GroupLocation,
        as: 'groupLocations',
        include: [
          {
            model: Groups,
            as: 'group',
            include: [{ model: GroupTypes, as: 'type' }],
          },
        ],
      },
      {
        model: PodUnits,
        as: 'unit',
      },
    ],
  },
] as Includeable[];

export const podAddressesDeepIncludeOptions = [
  { model: Groups, as: 'group' },
  { model: ParkingCharges, as: 'parkingCharges' },
  { model: ParkingOpeningTimes, as: 'parkingOpeningTimes' },
  { model: ParkingOpeningTimeNotes, as: 'parkingOpeningTimeNotes' },
  {
    model: PodLocations,
    as: 'podLocations',
    include: [
      {
        model: PodLocationUnit,
        as: 'podLocationUnits',
      },
      {
        model: GroupLocation,
        as: 'groupLocations',
        include: [
          {
            model: Groups,
            as: 'group',
            include: [{ model: GroupTypes, as: 'type' }],
          },
        ],
      },
      {
        model: PodUnits,
        as: 'unit',
        include: [
          {
            model: PodUnitConnector,
            as: 'podUnitConnectors',
            include: [
              {
                model: PodConnectors,
                as: 'connector',
                include: [{ model: PodDoors, as: 'door' }],
              },
              { model: PodStatuses, as: 'status' },
            ],
          },
          { model: PodModels, as: 'model' },
          { model: PodStatuses, as: 'status' },
        ],
      },
    ],
  },
  {
    model: Tariffs,
    as: 'tariff',
    include: [{ model: TariffTiers, as: 'tariffTiers' }],
  },
] as Includeable[];
