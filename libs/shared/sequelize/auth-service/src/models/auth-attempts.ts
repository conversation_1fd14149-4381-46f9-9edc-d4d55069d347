import * as Sequelize from 'sequelize';
import { DataTypes, Model } from 'sequelize';

export interface AuthAttemptsAttributes {
  email: string;
  failCount: number;
  firstFailedAttemptAt: Date;
}

export type AuthAttemptsPk = 'email';
export type AuthAttemptsId = AuthAttempts[AuthAttemptsPk];
export type AuthAttemptsCreationAttributes = AuthAttemptsAttributes;

export class AuthAttempts
  extends Model<AuthAttemptsAttributes, AuthAttemptsCreationAttributes>
  implements AuthAttemptsAttributes
{
  email!: string;
  failCount!: number;
  firstFailedAttemptAt!: Date;

  static initModel(sequelize: Sequelize.Sequelize): typeof AuthAttempts {
    return AuthAttempts.init(
      {
        email: {
          type: DataTypes.STRING(255),
          allowNull: false,
          primaryKey: true,
        },
        failCount: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          field: 'fail_count',
        },
        firstFailedAttemptAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'first_failed_attempt_at',
        },
      },
      {
        sequelize,
        tableName: 'auth_attempts',
        timestamps: false,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'email' }],
          },
        ],
      }
    );
  }
}
