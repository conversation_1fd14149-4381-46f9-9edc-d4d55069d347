import { Button, ButtonProps } from '@experience/shared/react/design-system';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { useFormStatus } from 'react-dom';

export const FormActionButton = (props: ButtonProps) => {
  const { pending } = useFormStatus();

  return (
    <Button {...props} type="submit" disabled={pending || props.disabled}>
      {props.children}
    </Button>
  );
};

export default FormActionButton;
