import { NavigationLinkProps } from '../navigation-link/navigation-link';
import { render } from '@testing-library/react';
import Sidebar from './sidebar';

const mockRouter = jest.fn();
const mockPathname = jest.fn();

jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    feedbackFormUrl: 'https://www.google.com',
    hasAboutPage: true,
  },
}));

jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter(),
  usePathname: () => mockPathname(),
}));

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: { user: { email: '<EMAIL>' } },
    status: 'authenticated',
  })),
}));

const navigationLinks = [
  {
    name: 'Home',
    href: '/',
    icon: 'HomeIcon',
  },
  {
    name: 'Info',
    href: '/info',
    icon: 'InfoIcon',
  },
] as NavigationLinkProps[];

describe('Sidebar', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <Sidebar navigationLinks={[]} showNavigationBottom={true} />
    );
    expect(baseElement).toBeTruthy();
  });

  it.each(navigationLinks)(
    'should match snapshot when path is %s',
    ({ href }) => {
      mockPathname.mockReturnValue(href);

      const { baseElement } = render(
        <Sidebar
          navigationLinks={navigationLinks}
          showNavigationBottom={true}
        />
      );

      expect(baseElement).toMatchSnapshot();
    }
  );
});
