// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Main should match the snapshot 1`] = `
<body>
  <div>
    <div
      class="md:pl-56 flex flex-col flex-1 md:print:pl-0 min-h-screen"
    >
      <div
        class="sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 print:hidden"
      >
        <button
          class="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-neutral hover:text-neutral/80 focus:outline-hidden focus:ring-2 focus:ring-inset focus:ring-info"
          type="button"
        >
          <span
            class="sr-only"
          >
            Open sidebar
          </span>
          <svg
            aria-hidden="true"
            class="h-6 w-6"
            data-slot="icon"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
      <main
        class="flex-grow"
      >
        <div
          class="py-3"
        >
          <div
            class="px-4 sm:px-6 md:px-8"
          >
            <div>
              Main Content
            </div>
          </div>
        </div>
      </main>
      <div>
        Footer
      </div>
    </div>
  </div>
</body>
`;
