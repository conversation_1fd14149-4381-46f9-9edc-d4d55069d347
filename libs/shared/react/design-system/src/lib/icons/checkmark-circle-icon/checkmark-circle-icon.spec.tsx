import { render } from '@testing-library/react';
import CheckmarkCircleIcon from './checkmark-circle-icon';

describe('CheckmarkCircleIcon', () => {
  it('should render light icon successfully', () => {
    const { baseElement } = render(<CheckmarkCircleIcon.LIGHT />);
    expect(baseElement).toBeTruthy();
  });

  it('should match the light icon snapshot', () => {
    const { baseElement } = render(<CheckmarkCircleIcon.LIGHT />);
    expect(baseElement).toMatchSnapshot();
  });
});
