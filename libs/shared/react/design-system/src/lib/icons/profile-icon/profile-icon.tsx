import IconWrapper from '../../../utils/icon-wrapper/icon-wrapper';
import React from 'react';

export type ProfileIconProps = React.HTMLProps<SVGElement>;

export const ProfileIcon = {
  LIGHT: (props: ProfileIconProps) => (
    <IconWrapper {...props}>
      <g>
        <path d="M15.69,18A8.09,8.09,0,1,1,23.78,9.9,8.1,8.1,0,0,1,15.69,18Zm0-14.17A6.09,6.09,0,1,0,21.78,9.9,6.09,6.09,0,0,0,15.69,3.82Z" />
        <path d="M2.15,29.62a1.13,1.13,0,0,1-.32-.05A1,1,0,0,1,1.2,28.3c1.92-5.66,8-9.62,14.78-9.62s12.68,3.78,14.71,9.4a1,1,0,0,1-1.89.68c-1.74-4.84-6.89-8.08-12.82-8.08S4.74,24.08,3.1,28.94A1,1,0,0,1,2.15,29.62Z" />
      </g>
    </IconWrapper>
  ),
};

export default ProfileIcon;
