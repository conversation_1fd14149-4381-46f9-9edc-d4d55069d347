// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LocationBoltIcon should match the light icon snapshot 1`] = `
<body>
  <div>
    <svg
      class="fill-current h-4 w-4"
      viewBox="0 0 32 32"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g>
        <path
          d="M15.76,21a.71.71,0,0,1-.72-.71V16.49h-2.5a.69.69,0,0,1-.39-.12.72.72,0,0,1-.22-1L16.8,7.84a.69.69,0,0,1,.63-.34.72.72,0,0,1,.5.24.7.7,0,0,1,.19.52l0,3.68h2.67a.77.77,0,0,1,.39.11.74.74,0,0,1,.31.46.69.69,0,0,1-.1.54l-5,7.61A.73.73,0,0,1,15.76,21Zm2-12.54ZM17,8.22v0Zm.27-.08h0Z"
        />
        <path
          d="M16.65,31.4a1,1,0,0,1-.7-.29c-.44-.43-10.8-10.65-10.8-19a11.5,11.5,0,0,1,23,0c0,8.31-10.35,18.59-10.79,19A1,1,0,0,1,16.65,31.4Zm0-28.8a9.51,9.51,0,0,0-9.5,9.49c0,6.35,7.19,14.44,9.49,16.87,2.31-2.44,9.5-10.56,9.5-16.87A9.5,9.5,0,0,0,16.65,2.6Z"
        />
      </g>
    </svg>
  </div>
</body>
`;

exports[`LocationBoltIcon should match the solid icon snapshot 1`] = `
<body>
  <div>
    <svg
      class="fill-current h-4 w-4"
      viewBox="0 0 32 32"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g>
        <polygon
          points="17.1 8.44 17.1 8.44 17.1 8.44 17.1 8.44"
        />
        <path
          d="M16,.6A11.5,11.5,0,0,0,4.51,12.09c0,8.37,10.35,18.59,10.79,19a1,1,0,0,0,.7.29,1,1,0,0,0,.7-.29c.44-.43,10.79-10.71,10.79-19A11.5,11.5,0,0,0,16,.6Zm4.71,12.45-5,7.61a.72.72,0,0,1-1.32-.39V16.49h-2.5a.66.66,0,0,1-.38-.12.71.71,0,0,1-.22-1l4.86-7.54a.7.7,0,0,1,.64-.34.74.74,0,0,1,.5.24.69.69,0,0,1,.18.52l0,3.68H20.1a.8.8,0,0,1,.4.11.74.74,0,0,1,.31.46A.73.73,0,0,1,20.71,13.05Z"
        />
      </g>
    </svg>
  </div>
</body>
`;
