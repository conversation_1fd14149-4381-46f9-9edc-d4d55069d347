import { render } from '@testing-library/react';

import LogoutIcon from './logout-icon';

describe('LogoutIcon', () => {
  it('should render light icon successfully', () => {
    const { baseElement } = render(<LogoutIcon.LIGHT />);
    expect(baseElement).toBeTruthy();
  });

  it('should match the light icon snapshot', () => {
    const { baseElement } = render(<LogoutIcon.LIGHT />);
    expect(baseElement).toMatchSnapshot();
  });
});
