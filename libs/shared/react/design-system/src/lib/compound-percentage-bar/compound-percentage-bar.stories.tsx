import { Meta, StoryFn } from '@storybook/react';
import CompoundPercentageBar from './compound-percentage-bar';

export default {
  component: CompoundPercentageBar,
  title: 'Compound Percentage Bar',
} as Meta<typeof CompoundPercentageBar>;

const defaultArgs = {
  options: [
    { label: 'Foo', percentage: 45, barColour: '#005f41' },
    { label: 'Bar', percentage: 55, barColour: '#c8d72d' },
  ],
  className: '',
};

const Template: StoryFn<typeof CompoundPercentageBar> = (args) => (
  <CompoundPercentageBar {...args} />
);

export const Playground = Template.bind({});

Playground.args = { ...defaultArgs };
