import { ButtonTypes } from '../button/button';
import { fireEvent, render, screen } from '@testing-library/react';
import Modal, { ModalProps } from './modal';
import Paragraph from '../paragraph/paragraph';
import userEvent from '@testing-library/user-event';

const mockSetOpen = jest.fn();
const mockConfirm = jest.fn();
const mockCancel = jest.fn();

describe('Modal', () => {
  const defaultProps: ModalProps = {
    cancelButtonText: 'Cancel',
    confirmButtonText: 'Confirm',
    content: (
      <>
        <Paragraph>I can open and close on demand!</Paragraph>
        <Paragraph>Hello I am a modal</Paragraph>
      </>
    ),
    open: true,
    setOpen: mockSetOpen,
    handleConfirm: mockConfirm,
    title: 'Hello world!',
  };

  it('should render correctly', () => {
    const { baseElement } = render(<Modal {...defaultProps} />);

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<Modal {...defaultProps} />);

    expect(baseElement).toMatchSnapshot();
  });

  it('should call setOpen with false by default if close icon is clicked', () => {
    render(<Modal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Close' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should call handleCancel if close icon is clicked and handleCancel is passed', () => {
    render(<Modal {...defaultProps} handleCancel={mockCancel} />);

    fireEvent.click(screen.getByRole('button', { name: 'Close' }));

    expect(mockCancel).toHaveBeenCalledTimes(1);
    expect(mockSetOpen).toHaveBeenCalledTimes(0);
  });

  it('should call setOpen with false by default if cancel button is clicked', () => {
    render(<Modal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should call handleCancel if cancel button is clicked and handleCancel is passed', () => {
    render(<Modal {...defaultProps} handleCancel={mockCancel} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockCancel).toHaveBeenCalledTimes(1);
    expect(mockSetOpen).toHaveBeenCalledTimes(0);
  });

  it('should call handleConfirm if confirm button is clicked', () => {
    render(<Modal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Confirm' }));

    expect(mockConfirm).toHaveBeenCalledTimes(1);
  });

  it('should not show confirm button if no confirm text set', () => {
    render(<Modal {...defaultProps} confirmButtonText="" />);

    expect(
      screen.queryByRole('button', { name: 'Confirm' })
    ).not.toBeInTheDocument();
  });

  it('should not show cancel button if no cancel text set', () => {
    render(<Modal {...defaultProps} cancelButtonText="" />);

    expect(
      screen.queryByRole('button', { name: 'Cancel' })
    ).not.toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Confirm' })).toHaveClass(
      'w-full'
    );
  });

  it('should disable confirm button if isSubmitting prop is passed', () => {
    render(<Modal {...defaultProps} isSubmitting={true} />);

    expect(screen.getByRole('button', { name: 'Confirm' })).toBeDisabled();
  });

  it('should disable confirm button if isDisabled prop is passed', () => {
    render(<Modal {...defaultProps} isDisabled={true} />);

    expect(screen.getByRole('button', { name: 'Confirm' })).toBeDisabled();
  });

  it('should show a tooptip on hover if confirmButtonTooltipText prop is passed', async () => {
    render(
      <Modal {...defaultProps} confirmButtonTooltipText="This is a tooltip" />
    );

    await userEvent.hover(screen.getAllByRole('tooltip')[0]);
    expect(screen.getByText('This is a tooltip')).toBeInTheDocument();
  });

  it.each([ButtonTypes.PRIMARY, ButtonTypes.NEGATIVE, ButtonTypes.LINK])(
    'should style the confirm button as specified (%s)',
    async (type) => {
      const { baseElement } = render(
        <Modal {...defaultProps} confirmButtonType={type} />
      );

      expect(baseElement).toMatchSnapshot();
    }
  );

  it.each([ButtonTypes.PRIMARY, ButtonTypes.NEGATIVE, ButtonTypes.LINK])(
    'should style the cancel button as specified (%s)',
    async (type) => {
      const { baseElement } = render(
        <Modal {...defaultProps} cancelButtonType={type} />
      );

      expect(baseElement).toMatchSnapshot();
    }
  );

  it('should display a loading spinner when showLoadingSpinnerOnConfirm and isSubmitting are true', () => {
    render(<Modal {...defaultProps} isSubmitting={true} />);

    expect(screen.getByRole('status')).toBeTruthy();
  });

  it('should not display a loading spinner when showLoadingSpinnerOnConfirm is false', () => {
    render(
      <Modal
        {...defaultProps}
        isSubmitting={true}
        showLoadingSpinnerOnConfirm={false}
      />
    );

    expect(screen.queryByRole('status')).toBeNull();
  });
});
