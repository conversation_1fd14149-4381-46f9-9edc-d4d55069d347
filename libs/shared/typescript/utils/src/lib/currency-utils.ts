export const DEFAULT_CURRENCY_OPTIONS: Intl.NumberFormatOptions = {
  style: 'currency',
  currency: 'GBP',
};

export interface CurrencyProps {
  amount?: number;
  locale?: string;
  options?: Intl.NumberFormatOptions;
}

export const formatPenceAsCurrencyString = ({
  amount,
  locale = 'en-GB',
  options = DEFAULT_CURRENCY_OPTIONS,
}: CurrencyProps): string =>
  Intl.NumberFormat(locale, options).format(amount ? amount / 100 : 0);

export const formatPenceAsCurrencyNumber = ({
  amount,
  locale = 'en-GB',
}: Omit<CurrencyProps, 'options'>): number =>
  parseFloat(
    Intl.NumberFormat(locale, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      useGrouping: false,
    }).format(amount ? amount / 100 : 0)
  );

export const formatPoundsAsCurrencyString = ({
  amount,
  locale = 'en-GB',
  options = DEFAULT_CURRENCY_OPTIONS,
}: CurrencyProps): string =>
  Intl.NumberFormat(locale, options).format(amount ?? 0);

export const formatPoundsAsCurrencyNumber = ({
  amount,
  locale = 'en-GB',
}: Omit<CurrencyProps, 'options'>): number =>
  parseFloat(
    Intl.NumberFormat(locale, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      useGrouping: false,
    }).format(amount ?? 0)
  );

interface Currency {
  [key: string]: string;
}

export const Currencies: Currency = {
  en: 'GBP',
  ie: 'EUR',
  no: 'NOK',
  fr: 'EUR',
  es: 'EUR',
};
