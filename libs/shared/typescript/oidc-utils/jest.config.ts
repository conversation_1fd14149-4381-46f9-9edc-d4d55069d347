/* eslint-disable */
export default {
  displayName: 'shared-typescript-oidc-utils',
  preset: '../../../../jest.preset.js',
  globals: {},
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]sx?$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
      },
    ],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  coverageDirectory: '../../../../coverage/libs/shared/typescript/oidc-utils',
  testPathIgnorePatterns: ['index.spec.ts'],
};
