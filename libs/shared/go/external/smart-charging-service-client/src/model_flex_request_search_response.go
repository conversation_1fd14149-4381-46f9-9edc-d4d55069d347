/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the FlexRequestSearchResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &FlexRequestSearchResponse{}

// FlexRequestSearchResponse struct for FlexRequestSearchResponse
type FlexRequestSearchResponse struct {
	// The list of matching flex requests
	Data []FlexRequestResponse `json:"data"`
}

type _FlexRequestSearchResponse FlexRequestSearchResponse

// NewFlexRequestSearchResponse instantiates a new FlexRequestSearchResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewFlexRequestSearchResponse(data []FlexRequestResponse) *FlexRequestSearchResponse {
	this := FlexRequestSearchResponse{}
	this.Data = data
	return &this
}

// NewFlexRequestSearchResponseWithDefaults instantiates a new FlexRequestSearchResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewFlexRequestSearchResponseWithDefaults() *FlexRequestSearchResponse {
	this := FlexRequestSearchResponse{}
	return &this
}

// GetData returns the Data field value
func (o *FlexRequestSearchResponse) GetData() []FlexRequestResponse {
	if o == nil {
		var ret []FlexRequestResponse
		return ret
	}

	return o.Data
}

// GetDataOk returns a tuple with the Data field value
// and a boolean to check if the value has been set.
func (o *FlexRequestSearchResponse) GetDataOk() ([]FlexRequestResponse, bool) {
	if o == nil {
		return nil, false
	}
	return o.Data, true
}

// SetData sets field value
func (o *FlexRequestSearchResponse) SetData(v []FlexRequestResponse) {
	o.Data = v
}

func (o FlexRequestSearchResponse) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o FlexRequestSearchResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["data"] = o.Data
	return toSerialize, nil
}

func (o *FlexRequestSearchResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"data",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varFlexRequestSearchResponse := _FlexRequestSearchResponse{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varFlexRequestSearchResponse)

	if err != nil {
		return err
	}

	*o = FlexRequestSearchResponse(varFlexRequestSearchResponse)

	return err
}

type NullableFlexRequestSearchResponse struct {
	value *FlexRequestSearchResponse
	isSet bool
}

func (v NullableFlexRequestSearchResponse) Get() *FlexRequestSearchResponse {
	return v.value
}

func (v *NullableFlexRequestSearchResponse) Set(val *FlexRequestSearchResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableFlexRequestSearchResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableFlexRequestSearchResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableFlexRequestSearchResponse(val *FlexRequestSearchResponse) *NullableFlexRequestSearchResponse {
	return &NullableFlexRequestSearchResponse{value: val, isSet: true}
}

func (v NullableFlexRequestSearchResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableFlexRequestSearchResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
