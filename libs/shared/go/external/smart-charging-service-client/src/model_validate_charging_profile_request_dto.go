/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"time"
)

// checks if the ValidateChargingProfileRequestDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ValidateChargingProfileRequestDto{}

// ValidateChargingProfileRequestDto struct for ValidateChargingProfileRequestDto
type ValidateChargingProfileRequestDto struct {
	// Transaction id
	TransactionId *string `json:"transactionId,omitempty"`
	// The stack level
	StackLevel float32 `json:"stackLevel"`
	// The charging profile purpose
	ChargingProfilePurpose string `json:"chargingProfilePurpose"`
	// The charging profile kind
	ChargingProfileKind string `json:"chargingProfileKind"`
	// The recurrency kind
	RecurrencyKind *string `json:"recurrencyKind,omitempty"`
	// The valid from date
	ValidFrom *time.Time `json:"validFrom,omitempty"`
	// The valid to date
	ValidTo *time.Time `json:"validTo,omitempty"`
	// The charging schedule
	ChargingSchedule ChargingScheduleDto `json:"chargingSchedule"`
}

type _ValidateChargingProfileRequestDto ValidateChargingProfileRequestDto

// NewValidateChargingProfileRequestDto instantiates a new ValidateChargingProfileRequestDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewValidateChargingProfileRequestDto(stackLevel float32, chargingProfilePurpose string, chargingProfileKind string, chargingSchedule ChargingScheduleDto) *ValidateChargingProfileRequestDto {
	this := ValidateChargingProfileRequestDto{}
	this.StackLevel = stackLevel
	this.ChargingProfilePurpose = chargingProfilePurpose
	this.ChargingProfileKind = chargingProfileKind
	this.ChargingSchedule = chargingSchedule
	return &this
}

// NewValidateChargingProfileRequestDtoWithDefaults instantiates a new ValidateChargingProfileRequestDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewValidateChargingProfileRequestDtoWithDefaults() *ValidateChargingProfileRequestDto {
	this := ValidateChargingProfileRequestDto{}
	return &this
}

// GetTransactionId returns the TransactionId field value if set, zero value otherwise.
func (o *ValidateChargingProfileRequestDto) GetTransactionId() string {
	if o == nil || IsNil(o.TransactionId) {
		var ret string
		return ret
	}
	return *o.TransactionId
}

// GetTransactionIdOk returns a tuple with the TransactionId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileRequestDto) GetTransactionIdOk() (*string, bool) {
	if o == nil || IsNil(o.TransactionId) {
		return nil, false
	}
	return o.TransactionId, true
}

// HasTransactionId returns a boolean if a field has been set.
func (o *ValidateChargingProfileRequestDto) HasTransactionId() bool {
	if o != nil && !IsNil(o.TransactionId) {
		return true
	}

	return false
}

// SetTransactionId gets a reference to the given string and assigns it to the TransactionId field.
func (o *ValidateChargingProfileRequestDto) SetTransactionId(v string) {
	o.TransactionId = &v
}

// GetStackLevel returns the StackLevel field value
func (o *ValidateChargingProfileRequestDto) GetStackLevel() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.StackLevel
}

// GetStackLevelOk returns a tuple with the StackLevel field value
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileRequestDto) GetStackLevelOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.StackLevel, true
}

// SetStackLevel sets field value
func (o *ValidateChargingProfileRequestDto) SetStackLevel(v float32) {
	o.StackLevel = v
}

// GetChargingProfilePurpose returns the ChargingProfilePurpose field value
func (o *ValidateChargingProfileRequestDto) GetChargingProfilePurpose() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.ChargingProfilePurpose
}

// GetChargingProfilePurposeOk returns a tuple with the ChargingProfilePurpose field value
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileRequestDto) GetChargingProfilePurposeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargingProfilePurpose, true
}

// SetChargingProfilePurpose sets field value
func (o *ValidateChargingProfileRequestDto) SetChargingProfilePurpose(v string) {
	o.ChargingProfilePurpose = v
}

// GetChargingProfileKind returns the ChargingProfileKind field value
func (o *ValidateChargingProfileRequestDto) GetChargingProfileKind() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.ChargingProfileKind
}

// GetChargingProfileKindOk returns a tuple with the ChargingProfileKind field value
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileRequestDto) GetChargingProfileKindOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargingProfileKind, true
}

// SetChargingProfileKind sets field value
func (o *ValidateChargingProfileRequestDto) SetChargingProfileKind(v string) {
	o.ChargingProfileKind = v
}

// GetRecurrencyKind returns the RecurrencyKind field value if set, zero value otherwise.
func (o *ValidateChargingProfileRequestDto) GetRecurrencyKind() string {
	if o == nil || IsNil(o.RecurrencyKind) {
		var ret string
		return ret
	}
	return *o.RecurrencyKind
}

// GetRecurrencyKindOk returns a tuple with the RecurrencyKind field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileRequestDto) GetRecurrencyKindOk() (*string, bool) {
	if o == nil || IsNil(o.RecurrencyKind) {
		return nil, false
	}
	return o.RecurrencyKind, true
}

// HasRecurrencyKind returns a boolean if a field has been set.
func (o *ValidateChargingProfileRequestDto) HasRecurrencyKind() bool {
	if o != nil && !IsNil(o.RecurrencyKind) {
		return true
	}

	return false
}

// SetRecurrencyKind gets a reference to the given string and assigns it to the RecurrencyKind field.
func (o *ValidateChargingProfileRequestDto) SetRecurrencyKind(v string) {
	o.RecurrencyKind = &v
}

// GetValidFrom returns the ValidFrom field value if set, zero value otherwise.
func (o *ValidateChargingProfileRequestDto) GetValidFrom() time.Time {
	if o == nil || IsNil(o.ValidFrom) {
		var ret time.Time
		return ret
	}
	return *o.ValidFrom
}

// GetValidFromOk returns a tuple with the ValidFrom field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileRequestDto) GetValidFromOk() (*time.Time, bool) {
	if o == nil || IsNil(o.ValidFrom) {
		return nil, false
	}
	return o.ValidFrom, true
}

// HasValidFrom returns a boolean if a field has been set.
func (o *ValidateChargingProfileRequestDto) HasValidFrom() bool {
	if o != nil && !IsNil(o.ValidFrom) {
		return true
	}

	return false
}

// SetValidFrom gets a reference to the given time.Time and assigns it to the ValidFrom field.
func (o *ValidateChargingProfileRequestDto) SetValidFrom(v time.Time) {
	o.ValidFrom = &v
}

// GetValidTo returns the ValidTo field value if set, zero value otherwise.
func (o *ValidateChargingProfileRequestDto) GetValidTo() time.Time {
	if o == nil || IsNil(o.ValidTo) {
		var ret time.Time
		return ret
	}
	return *o.ValidTo
}

// GetValidToOk returns a tuple with the ValidTo field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileRequestDto) GetValidToOk() (*time.Time, bool) {
	if o == nil || IsNil(o.ValidTo) {
		return nil, false
	}
	return o.ValidTo, true
}

// HasValidTo returns a boolean if a field has been set.
func (o *ValidateChargingProfileRequestDto) HasValidTo() bool {
	if o != nil && !IsNil(o.ValidTo) {
		return true
	}

	return false
}

// SetValidTo gets a reference to the given time.Time and assigns it to the ValidTo field.
func (o *ValidateChargingProfileRequestDto) SetValidTo(v time.Time) {
	o.ValidTo = &v
}

// GetChargingSchedule returns the ChargingSchedule field value
func (o *ValidateChargingProfileRequestDto) GetChargingSchedule() ChargingScheduleDto {
	if o == nil {
		var ret ChargingScheduleDto
		return ret
	}

	return o.ChargingSchedule
}

// GetChargingScheduleOk returns a tuple with the ChargingSchedule field value
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileRequestDto) GetChargingScheduleOk() (*ChargingScheduleDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargingSchedule, true
}

// SetChargingSchedule sets field value
func (o *ValidateChargingProfileRequestDto) SetChargingSchedule(v ChargingScheduleDto) {
	o.ChargingSchedule = v
}

func (o ValidateChargingProfileRequestDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ValidateChargingProfileRequestDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.TransactionId) {
		toSerialize["transactionId"] = o.TransactionId
	}
	toSerialize["stackLevel"] = o.StackLevel
	toSerialize["chargingProfilePurpose"] = o.ChargingProfilePurpose
	toSerialize["chargingProfileKind"] = o.ChargingProfileKind
	if !IsNil(o.RecurrencyKind) {
		toSerialize["recurrencyKind"] = o.RecurrencyKind
	}
	if !IsNil(o.ValidFrom) {
		toSerialize["validFrom"] = o.ValidFrom
	}
	if !IsNil(o.ValidTo) {
		toSerialize["validTo"] = o.ValidTo
	}
	toSerialize["chargingSchedule"] = o.ChargingSchedule
	return toSerialize, nil
}

func (o *ValidateChargingProfileRequestDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"stackLevel",
		"chargingProfilePurpose",
		"chargingProfileKind",
		"chargingSchedule",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varValidateChargingProfileRequestDto := _ValidateChargingProfileRequestDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varValidateChargingProfileRequestDto)

	if err != nil {
		return err
	}

	*o = ValidateChargingProfileRequestDto(varValidateChargingProfileRequestDto)

	return err
}

type NullableValidateChargingProfileRequestDto struct {
	value *ValidateChargingProfileRequestDto
	isSet bool
}

func (v NullableValidateChargingProfileRequestDto) Get() *ValidateChargingProfileRequestDto {
	return v.value
}

func (v *NullableValidateChargingProfileRequestDto) Set(val *ValidateChargingProfileRequestDto) {
	v.value = val
	v.isSet = true
}

func (v NullableValidateChargingProfileRequestDto) IsSet() bool {
	return v.isSet
}

func (v *NullableValidateChargingProfileRequestDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableValidateChargingProfileRequestDto(val *ValidateChargingProfileRequestDto) *NullableValidateChargingProfileRequestDto {
	return &NullableValidateChargingProfileRequestDto{value: val, isSet: true}
}

func (v NullableValidateChargingProfileRequestDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableValidateChargingProfileRequestDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
