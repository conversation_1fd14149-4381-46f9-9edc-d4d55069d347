/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"time"
)

// checks if the ChargeOverrideResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargeOverrideResponse{}

// ChargeOverrideResponse struct for ChargeOverrideResponse
type ChargeOverrideResponse struct {
	// The ID of the charge override
	Id string `json:"id"`
	// The date and time the charge override was requested
	RequestedAt time.Time `json:"requestedAt"`
	// The date and time the charge override was received by the api
	ReceivedAt time.Time `json:"receivedAt"`
	// The date and time the charge override should end
	EndAt NullableTime `json:"endAt"`
	// The evse associated with the charge override
	Evse EvseResponse `json:"evse"`
	// The charging station associated with the charge override
	ChargingStation ChargingStationResponseDto `json:"chargingStation"`
	// The date and time the charge override was deleted
	DeletedAt NullableTime `json:"deletedAt"`
}

type _ChargeOverrideResponse ChargeOverrideResponse

// NewChargeOverrideResponse instantiates a new ChargeOverrideResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargeOverrideResponse(id string, requestedAt time.Time, receivedAt time.Time, endAt NullableTime, evse EvseResponse, chargingStation ChargingStationResponseDto, deletedAt NullableTime) *ChargeOverrideResponse {
	this := ChargeOverrideResponse{}
	this.Id = id
	this.RequestedAt = requestedAt
	this.ReceivedAt = receivedAt
	this.EndAt = endAt
	this.Evse = evse
	this.ChargingStation = chargingStation
	this.DeletedAt = deletedAt
	return &this
}

// NewChargeOverrideResponseWithDefaults instantiates a new ChargeOverrideResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargeOverrideResponseWithDefaults() *ChargeOverrideResponse {
	this := ChargeOverrideResponse{}
	return &this
}

// GetId returns the Id field value
func (o *ChargeOverrideResponse) GetId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Id
}

// GetIdOk returns a tuple with the Id field value
// and a boolean to check if the value has been set.
func (o *ChargeOverrideResponse) GetIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Id, true
}

// SetId sets field value
func (o *ChargeOverrideResponse) SetId(v string) {
	o.Id = v
}

// GetRequestedAt returns the RequestedAt field value
func (o *ChargeOverrideResponse) GetRequestedAt() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.RequestedAt
}

// GetRequestedAtOk returns a tuple with the RequestedAt field value
// and a boolean to check if the value has been set.
func (o *ChargeOverrideResponse) GetRequestedAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.RequestedAt, true
}

// SetRequestedAt sets field value
func (o *ChargeOverrideResponse) SetRequestedAt(v time.Time) {
	o.RequestedAt = v
}

// GetReceivedAt returns the ReceivedAt field value
func (o *ChargeOverrideResponse) GetReceivedAt() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.ReceivedAt
}

// GetReceivedAtOk returns a tuple with the ReceivedAt field value
// and a boolean to check if the value has been set.
func (o *ChargeOverrideResponse) GetReceivedAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ReceivedAt, true
}

// SetReceivedAt sets field value
func (o *ChargeOverrideResponse) SetReceivedAt(v time.Time) {
	o.ReceivedAt = v
}

// GetEndAt returns the EndAt field value
// If the value is explicit nil, the zero value for time.Time will be returned
func (o *ChargeOverrideResponse) GetEndAt() time.Time {
	if o == nil || o.EndAt.Get() == nil {
		var ret time.Time
		return ret
	}

	return *o.EndAt.Get()
}

// GetEndAtOk returns a tuple with the EndAt field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ChargeOverrideResponse) GetEndAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return o.EndAt.Get(), o.EndAt.IsSet()
}

// SetEndAt sets field value
func (o *ChargeOverrideResponse) SetEndAt(v time.Time) {
	o.EndAt.Set(&v)
}

// GetEvse returns the Evse field value
func (o *ChargeOverrideResponse) GetEvse() EvseResponse {
	if o == nil {
		var ret EvseResponse
		return ret
	}

	return o.Evse
}

// GetEvseOk returns a tuple with the Evse field value
// and a boolean to check if the value has been set.
func (o *ChargeOverrideResponse) GetEvseOk() (*EvseResponse, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Evse, true
}

// SetEvse sets field value
func (o *ChargeOverrideResponse) SetEvse(v EvseResponse) {
	o.Evse = v
}

// GetChargingStation returns the ChargingStation field value
func (o *ChargeOverrideResponse) GetChargingStation() ChargingStationResponseDto {
	if o == nil {
		var ret ChargingStationResponseDto
		return ret
	}

	return o.ChargingStation
}

// GetChargingStationOk returns a tuple with the ChargingStation field value
// and a boolean to check if the value has been set.
func (o *ChargeOverrideResponse) GetChargingStationOk() (*ChargingStationResponseDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargingStation, true
}

// SetChargingStation sets field value
func (o *ChargeOverrideResponse) SetChargingStation(v ChargingStationResponseDto) {
	o.ChargingStation = v
}

// GetDeletedAt returns the DeletedAt field value
// If the value is explicit nil, the zero value for time.Time will be returned
func (o *ChargeOverrideResponse) GetDeletedAt() time.Time {
	if o == nil || o.DeletedAt.Get() == nil {
		var ret time.Time
		return ret
	}

	return *o.DeletedAt.Get()
}

// GetDeletedAtOk returns a tuple with the DeletedAt field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ChargeOverrideResponse) GetDeletedAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return o.DeletedAt.Get(), o.DeletedAt.IsSet()
}

// SetDeletedAt sets field value
func (o *ChargeOverrideResponse) SetDeletedAt(v time.Time) {
	o.DeletedAt.Set(&v)
}

func (o ChargeOverrideResponse) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargeOverrideResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["id"] = o.Id
	toSerialize["requestedAt"] = o.RequestedAt
	toSerialize["receivedAt"] = o.ReceivedAt
	toSerialize["endAt"] = o.EndAt.Get()
	toSerialize["evse"] = o.Evse
	toSerialize["chargingStation"] = o.ChargingStation
	toSerialize["deletedAt"] = o.DeletedAt.Get()
	return toSerialize, nil
}

func (o *ChargeOverrideResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"id",
		"requestedAt",
		"receivedAt",
		"endAt",
		"evse",
		"chargingStation",
		"deletedAt",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargeOverrideResponse := _ChargeOverrideResponse{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varChargeOverrideResponse)

	if err != nil {
		return err
	}

	*o = ChargeOverrideResponse(varChargeOverrideResponse)

	return err
}

type NullableChargeOverrideResponse struct {
	value *ChargeOverrideResponse
	isSet bool
}

func (v NullableChargeOverrideResponse) Get() *ChargeOverrideResponse {
	return v.value
}

func (v *NullableChargeOverrideResponse) Set(val *ChargeOverrideResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableChargeOverrideResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableChargeOverrideResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargeOverrideResponse(val *ChargeOverrideResponse) *NullableChargeOverrideResponse {
	return &NullableChargeOverrideResponse{value: val, isSet: true}
}

func (v NullableChargeOverrideResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargeOverrideResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
