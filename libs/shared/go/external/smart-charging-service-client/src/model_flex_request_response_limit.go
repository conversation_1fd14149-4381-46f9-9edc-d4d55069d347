/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
)

// checks if the FlexRequestResponseLimit type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &FlexRequestResponseLimit{}

// FlexRequestResponseLimit The limit of the flexibility request
type FlexRequestResponseLimit struct {
	Unit  *string  `json:"unit,omitempty"`
	Value *float32 `json:"value,omitempty"`
}

// NewFlexRequestResponseLimit instantiates a new FlexRequestResponseLimit object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewFlexRequestResponseLimit() *FlexRequestResponseLimit {
	this := FlexRequestResponseLimit{}
	return &this
}

// NewFlexRequestResponseLimitWithDefaults instantiates a new FlexRequestResponseLimit object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewFlexRequestResponseLimitWithDefaults() *FlexRequestResponseLimit {
	this := FlexRequestResponseLimit{}
	return &this
}

// GetUnit returns the Unit field value if set, zero value otherwise.
func (o *FlexRequestResponseLimit) GetUnit() string {
	if o == nil || IsNil(o.Unit) {
		var ret string
		return ret
	}
	return *o.Unit
}

// GetUnitOk returns a tuple with the Unit field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *FlexRequestResponseLimit) GetUnitOk() (*string, bool) {
	if o == nil || IsNil(o.Unit) {
		return nil, false
	}
	return o.Unit, true
}

// HasUnit returns a boolean if a field has been set.
func (o *FlexRequestResponseLimit) HasUnit() bool {
	if o != nil && !IsNil(o.Unit) {
		return true
	}

	return false
}

// SetUnit gets a reference to the given string and assigns it to the Unit field.
func (o *FlexRequestResponseLimit) SetUnit(v string) {
	o.Unit = &v
}

// GetValue returns the Value field value if set, zero value otherwise.
func (o *FlexRequestResponseLimit) GetValue() float32 {
	if o == nil || IsNil(o.Value) {
		var ret float32
		return ret
	}
	return *o.Value
}

// GetValueOk returns a tuple with the Value field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *FlexRequestResponseLimit) GetValueOk() (*float32, bool) {
	if o == nil || IsNil(o.Value) {
		return nil, false
	}
	return o.Value, true
}

// HasValue returns a boolean if a field has been set.
func (o *FlexRequestResponseLimit) HasValue() bool {
	if o != nil && !IsNil(o.Value) {
		return true
	}

	return false
}

// SetValue gets a reference to the given float32 and assigns it to the Value field.
func (o *FlexRequestResponseLimit) SetValue(v float32) {
	o.Value = &v
}

func (o FlexRequestResponseLimit) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o FlexRequestResponseLimit) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Unit) {
		toSerialize["unit"] = o.Unit
	}
	if !IsNil(o.Value) {
		toSerialize["value"] = o.Value
	}
	return toSerialize, nil
}

type NullableFlexRequestResponseLimit struct {
	value *FlexRequestResponseLimit
	isSet bool
}

func (v NullableFlexRequestResponseLimit) Get() *FlexRequestResponseLimit {
	return v.value
}

func (v *NullableFlexRequestResponseLimit) Set(val *FlexRequestResponseLimit) {
	v.value = val
	v.isSet = true
}

func (v NullableFlexRequestResponseLimit) IsSet() bool {
	return v.isSet
}

func (v *NullableFlexRequestResponseLimit) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableFlexRequestResponseLimit(val *FlexRequestResponseLimit) *NullableFlexRequestResponseLimit {
	return &NullableFlexRequestResponseLimit{value: val, isSet: true}
}

func (v NullableFlexRequestResponseLimit) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableFlexRequestResponseLimit) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
