// Code generated by MockGen. DO NOT EDIT.
// Source: libs/shared/go/event-store/event_transformer.go
//
// Generated by this command:
//
//	mockgen -destination libs/shared/go/event-store/test/mock/event_transformer.go -source libs/shared/go/event-store/event_transformer.go
//
// Package mock_eventstore is a generated GoMock package.
package mock_eventstore

import (
	json "encoding/json"
	eventstore "experience/libs/shared/go/event-store"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockEventTransformer is a mock of EventTransformer interface.
type MockEventTransformer struct {
	ctrl     *gomock.Controller
	recorder *MockEventTransformerMockRecorder
}

// MockEventTransformerMockRecorder is the mock recorder for MockEventTransformer.
type MockEventTransformerMockRecorder struct {
	mock *MockEventTransformer
}

// NewMockEventTransformer creates a new mock instance.
func NewMockEventTransformer(ctrl *gomock.Controller) *MockEventTransformer {
	mock := &MockEventTransformer{ctrl: ctrl}
	mock.recorder = &MockEventTransformerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventTransformer) EXPECT() *MockEventTransformerMockRecorder {
	return m.recorder
}

// Transform mocks base method.
func (m *MockEventTransformer) Transform(messageBody *string) (eventstore.Event, json.RawMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transform", messageBody)
	ret0, _ := ret[0].(eventstore.Event)
	ret1, _ := ret[1].(json.RawMessage)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Transform indicates an expected call of Transform.
func (mr *MockEventTransformerMockRecorder) Transform(messageBody any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transform", reflect.TypeOf((*MockEventTransformer)(nil).Transform), messageBody)
}
