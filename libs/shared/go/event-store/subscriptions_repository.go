package eventstore

import (
	"context"
	"database/sql"
	"experience/libs/shared/go/event-store/sqlc"
	"fmt"
	"log"

	"github.com/jmoiron/sqlx"
)

func NewEventSubscription(logger *log.Logger) Subscription {
	return &subscriptionImpl{
		logger: logger,
	}
}

type subscriptionImpl struct {
	dbWrite *sqlx.DB
	queries *sqlc.Queries
	logger  *log.Logger
}

func (s subscriptionImpl) ReadCheckpointAndLockSubscription(ctx context.Context, tx *sql.Tx, subscriptionName string) (EventTransaction, error) {
	lastTransaction, err := s.queries.WithTx(tx).ReadCheckpointAndLockSubscription(ctx, subscriptionName)

	if err != nil {
		return EventTransaction{}, fmt.Errorf("unable to retrieve last transaction ID and event ID: %w", err)
	}

	return EventTransaction{
		TransactionID: lastTransaction.LastTransactionID,
		ID:            lastTransaction.LastEventID,
	}, nil
}

func (s subscriptionImpl) ReadEventsAfterCheckpoint(ctx context.Context, tx *sql.Tx, transactionID uint64, eventID int64) ([]Record, error) {
	events, err := s.queries.WithTx(tx).ReadEventsAfterCheckpoint(ctx, sqlc.ReadEventsAfterCheckpointParams{TransactionID: transactionID, ID: eventID})

	if err != nil {
		return nil, fmt.Errorf("failed to retrieve new events for: %d, %w", eventID, err)
	}

	records := make([]Record, len(events))

	for i, event := range events {
		records[i] = Record{
			EventID:       event.ID,
			TransactionID: event.TransactionID,
			Version:       event.Version,
			Data:          event.Data,
		}
	}
	return records, nil
}

func (s subscriptionImpl) UpdateEventSubscription(ctx context.Context, tx *sql.Tx, lastTransactionID uint64, eventID int64, subscriptionName string) (EventTransaction, error) {
	transaction, err := s.queries.WithTx(tx).UpdateEventSubscription(ctx, sqlc.UpdateEventSubscriptionParams{LastTransactionID: lastTransactionID, LastEventID: eventID, SubscriptionName: subscriptionName})

	if err != nil {
		return EventTransaction{}, err
	}

	return EventTransaction{
		TransactionID: transaction.LastTransactionID,
		ID:            transaction.LastEventID,
	}, nil
}
