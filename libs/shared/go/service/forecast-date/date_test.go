package forecastdate

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestForecastFrom(t *testing.T) {
	tests := []struct {
		name string
		arg  time.Time
		want time.Time
	}{
		{
			name: "At minute 1 forecast starts at minute 0",
			arg:  time.Date(2023, 1, 1, 1, 1, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 1, 0, 0, 0, time.UTC),
		},
		{
			name: "At minute 15 forecast starts at minute 0",
			arg:  time.Date(2023, 1, 1, 1, 15, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 1, 0, 0, 0, time.UTC),
		},
		{
			name: "At minute 30 forecast starts at minute 0",
			arg:  time.Date(2023, 1, 1, 1, 30, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 1, 0, 0, 0, time.UTC),
		},
		{
			name: "At minute 31 forecast starts at minute 30",
			arg:  time.Date(2023, 1, 1, 1, 31, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 1, 30, 0, 0, time.UTC),
		},
		{
			name: "At minute 45 forecast starts at minute 30",
			arg:  time.Date(2023, 1, 1, 1, 45, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 1, 30, 0, 0, time.UTC),
		},
		{
			name: "At minute 59 forecast starts at minute 30",
			arg:  time.Date(2023, 1, 1, 1, 59, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 1, 30, 0, 0, time.UTC),
		},
		{
			name: "At minute 0 forecast starts at minute 30 of the previous hour",
			arg:  time.Date(2023, 1, 1, 1, 0, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 0, 30, 0, 0, time.UTC),
		},
		{
			name: "At minute 0 of hour 0 forecast starts at minute 30 of the last hour of the previous day",
			arg:  time.Date(2023, 1, 2, 0, 0, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 23, 30, 0, 0, time.UTC),
		},
		{
			name: "At minute 0 of the first day of the month forecast starts at minute 30 of the last hour of the last day of the previous month",
			arg:  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			want: time.Date(2023, 1, 31, 23, 30, 0, 0, time.UTC),
		},
		{
			name: "At minute 0 of the first day of the year forecast starts at minute 30 of the last hour of the last day of the previous year",
			arg:  time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			want: time.Date(2022, 12, 31, 23, 30, 0, 0, time.UTC),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			require.Equal(t, tt.want, From(tt.arg))
		})
	}
}

func TestGetPreviousScheduled(t *testing.T) {
	tests := []struct {
		name string
		date time.Time
		want time.Time
	}{
		{
			name: "Previous scheduled date is 30 minutes to the past",
			date: time.Date(2023, 1, 1, 18, 0, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 17, 0, 0, 0, time.UTC),
		},
		{
			name: "Previous scheduled date is 30 minutes to the past according to schedule also for non-normalised dates",
			date: time.Date(2023, 1, 1, 18, 15, 0, 0, time.UTC),
			want: time.Date(2023, 1, 1, 17, 30, 0, 0, time.UTC),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			require.Equal(t, tt.want, GetPreviousScheduled(tt.date))
		})
	}
}
