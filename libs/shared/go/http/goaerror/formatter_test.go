package goaerror_test

import (
	"context"
	"errors"
	"experience/libs/shared/go/http/goaerror"
	"testing"

	"github.com/stretchr/testify/assert"
	goa "goa.design/goa/v3/pkg"
)

type TestInvalidError struct {
}

func (t TestInvalidError) Error() string {
	return "invalid"
}

type TestNotFoundError struct {
}

func (t TestNotFoundError) Error() string {
	return "not found"
}

func TestFormatError(t *testing.T) {
	tests := []struct {
		name string
		err  error
		want int
	}{
		{
			name: "return 500 when error is not a goa service error",
			err:  errors.New("we don't support this error type"),
			want: 500,
		},
		{
			name: "return 404 for specialised NotFound goa error",
			err:  &TestNotFoundError{},
			want: 404,
		},
		{
			name: "return 400 for specialised Invalid goa error",
			err:  &TestInvalidError{},
			want: 400,
		},
		{
			name: "returns 400 for specialised BadRequest goa error",
			err:  goa.NewServiceError(errors.New("bad request"), "bad_request", false, false, false),
			want: 400,
		},
		{
			name: "returns goa service error with status",
			err:  goa.NewServiceError(errors.New("goa service error"), "goa service error", false, false, false),
			want: 400,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			formattedError := goaerror.FormatError(context.Background(), tt.err)
			assert.Equal(t, tt.want, formattedError.StatusCode())
		})
	}
}
