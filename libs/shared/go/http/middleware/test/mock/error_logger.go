// Code generated by MockGen. DO NOT EDIT.
// Source: libs/shared/go/http/middleware/error_logger.go
//
// Generated by this command:
//
//	mockgen -destination libs/shared/go/http/middleware/test/mock/error_logger.go -source libs/shared/go/http/middleware/error_logger.go
//
// Package mock_middleware is a generated GoMock package.
package mock_middleware

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	log "goa.design/clue/log"
)

// MockGoaClueLog is a mock of GoaClueLog interface.
type MockGoaClueLog struct {
	ctrl     *gomock.Controller
	recorder *MockGoaClueLogMockRecorder
}

// MockGoaClueLogMockRecorder is the mock recorder for MockGoaClueLog.
type MockGoaClueLogMockRecorder struct {
	mock *MockGoaClueLog
}

// NewMockGoaClueLog creates a new mock instance.
func NewMockGoaClueLog(ctrl *gomock.Controller) *MockGoaClueLog {
	mock := &MockGoaClueLog{ctrl: ctrl}
	mock.recorder = &MockGoaClueLogMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGoaClueLog) EXPECT() *MockGoaClueLogMockRecorder {
	return m.recorder
}

// Error mocks base method.
func (m *MockGoaClueLog) Error(ctx context.Context, err error, keyvals ...log.Fielder) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, err}
	for _, a := range keyvals {
		varargs = append(varargs, a)
	}
	m.ctrl.Call(m, "Error", varargs...)
}

// Error indicates an expected call of Error.
func (mr *MockGoaClueLogMockRecorder) Error(ctx, err any, keyvals ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, err}, keyvals...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Error", reflect.TypeOf((*MockGoaClueLog)(nil).Error), varargs...)
}
