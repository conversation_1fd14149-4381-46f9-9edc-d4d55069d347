import { ConfigModule } from '@nestjs/config';
import { PushEvseUpdatesProducer } from './push-evse-updates.producer';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import { Test, TestingModule } from '@nestjs/testing';

jest.mock('@experience/shared/nest/aws/sqs-module');

describe('Push evse updates producer', () => {
  let producer: PushEvseUpdatesProducer;
  let sqsClientService: SqsClientService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              PUSH_EVSE_UPDATES_QUEUE_URL:
                'http://localhost/000000/push-evse-updates',
            }),
          ],
        }),
      ],
      providers: [PushEvseUpdatesProducer, SqsClientService],
    }).compile();

    producer = module.get<PushEvseUpdatesProducer>(PushEvseUpdatesProducer);
    sqsClientService = module.get<SqsClientService>(SqsClientService);
  });

  it('should be defined', () => {
    expect(producer).toBeDefined();
    expect(sqsClientService).toBeDefined();
  });

  it('should queue push evse update request', async () => {
    const message = {
      evseId: 'GB*POD*E*PG12345E1',
    };

    const mockSendMessage = jest.spyOn(sqsClientService, 'sendMessage');

    await producer.queuePushEvseUpdatesRequest(message);

    expect(mockSendMessage).toHaveBeenCalledWith(
      'http://localhost/000000/push-evse-updates',
      {
        messageBody: JSON.stringify(message),
      }
    );
  });
});
