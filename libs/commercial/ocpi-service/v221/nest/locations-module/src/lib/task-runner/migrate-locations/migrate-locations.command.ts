import { Command, CommandRunner } from 'nest-commander';
import {
  OCPIPrismaClient,
  Prisma,
} from '@experience/commercial/ocpi-service/v221/prisma/client';
import ConnectorMigrator from '../../migrators/connector/connector-migrator';
import ConnectorTariffMigrator from '../../migrators/connector/connector-tariff-migrator';
import EvseMigrator from '../../migrators/evse/evse-migrator';
import LocationMigrator from '../../migrators/location/location-migrator';
import RemovedEvseMigrator from '../../migrators/evse/removed-evse-migrator';

@Command({ name: 'migrate-locations' })
export class MigrateLocationsCommand extends CommandRunner {
  constructor(
    private readonly connectorMigrator: ConnectorMigrator,
    private readonly connectorTariffMigrator: ConnectorTariffMigrator,
    private readonly database: OCPIPrismaClient,
    private readonly evseMigrator: EvseMigrator,
    private readonly locationMigrator: LocationMigrator,
    private readonly removedEvseMigrator: RemovedEvseMigrator
  ) {
    super();
  }

  async run(): Promise<void> {
    const [since] = await Promise.all([
      this.database.location
        .findFirst({
          orderBy: { lastUpdatedInPodadmin: Prisma.SortOrder.desc },
        })
        .then((result) =>
          result ? result.lastUpdatedInPodadmin : new Date(0)
        ),
    ]);

    await this.locationMigrator.migrate(since);
    await this.evseMigrator.migrate(since);
    await this.connectorMigrator.migrate(since);
    await this.connectorTariffMigrator.migrate(since);

    await this.removedEvseMigrator.migrate();
  }
}

export default MigrateLocationsCommand;
