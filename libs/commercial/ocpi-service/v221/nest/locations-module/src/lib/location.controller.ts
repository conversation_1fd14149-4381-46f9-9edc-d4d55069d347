import {
  ConnectorDto,
  EvseDto,
  ExceptionHandlerInterceptor,
  LocationDto,
  OcpiResponse,
  OcpiVersionNumber,
  buildSuccessResponse,
} from '@experience/commercial/ocpi-service/v221/shared';
import {
  Controller,
  Get,
  Param,
  Req,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { LocationService } from './location.service';
import {
  Page,
  Pagination,
  PaginationParams,
  setPaginationResponseHeaders,
} from '@experience/shared/nest/utils';
import { Request, Response } from 'express';

@Controller({
  path: 'locations',
  version: OcpiVersionNumber.enum['2.2.1'],
})
@UseInterceptors(ExceptionHandlerInterceptor)
export class LocationController {
  constructor(private service: LocationService) {}

  @Get()
  async findAll(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
    @Pagination() paginationParams: PaginationParams
  ): Promise<OcpiResponse<LocationDto[]>> {
    return this.service
      .findAll(paginationParams)
      .then((page: Page<LocationDto>) => {
        setPaginationResponseHeaders(request, response, page);
        return page.elements;
      })
      .then((locations) => buildSuccessResponse(locations));
  }

  @Get(':locationId')
  async findLocation(
    @Param('locationId') locationId: string
  ): Promise<OcpiResponse<LocationDto>> {
    return this.service
      .findLocation(locationId)
      .then((location) => buildSuccessResponse(location));
  }

  @Get(':locationId/:evseUid')
  async findEvse(
    @Param('locationId') locationId: string,
    @Param('evseUid') evseUid: string
  ): Promise<OcpiResponse<EvseDto>> {
    return this.service
      .findEvse(locationId, evseUid)
      .then((evse) => buildSuccessResponse(evse));
  }

  @Get(':locationId/:evseUid/:connectorId')
  async findConnector(
    @Param('locationId') locationId: string,
    @Param('evseUid') evseUid: string,
    @Param('connectorId') connectorId: string
  ): Promise<OcpiResponse<ConnectorDto>> {
    return this.service
      .findConnector(locationId, evseUid, connectorId)
      .then((connector) => buildSuccessResponse(connector));
  }
}
