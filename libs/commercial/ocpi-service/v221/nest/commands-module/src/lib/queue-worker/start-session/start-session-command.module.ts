import { ConfigModule, ConfigService } from '@nestjs/config';
import { CredentialsModule } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import {
  ChargeAuthorisationApi as DataPlatformChargeAuthorisationApi,
  Configuration as DataPlatformConfiguration,
} from '@experience/shared/axios/data-platform-api-client';
import { LocationsModule } from '@experience/commercial/ocpi-service/v221/nest/locations-module';
import { Module } from '@nestjs/common';
import {
  QueueConsumerService,
  SqsConsumerModule,
} from '@experience/shared/nest/aws/sqs-module';
import { SessionsModule } from '@experience/commercial/ocpi-service/v221/nest/sessions-module';
import { SqsConsumerModuleOptions } from '@experience/shared/nest/aws/sqs-module';
import {
  StartSessionCommandConsumer,
  StartSessionCommandQueueConsumerService,
} from './start-session-command.consumer';
import axios from 'axios';

@Module({
  imports: [
    ConfigModule,
    CredentialsModule,
    LocationsModule,
    SessionsModule,
    SqsConsumerModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService): SqsConsumerModuleOptions => ({
        disabled: !config.get<string>('START_SESSION_COMMANDS_QUEUE_URL'),
        queueUrl: config.get<string>('START_SESSION_COMMANDS_QUEUE_URL'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    {
      provide: StartSessionCommandQueueConsumerService,
      useExisting: QueueConsumerService,
    },
    {
      inject: [ConfigService],
      provide: DataPlatformChargeAuthorisationApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('DATA_PLATFORM_API_TIMEOUT', 10000),
        });
        return new DataPlatformChargeAuthorisationApi(
          new DataPlatformConfiguration(),
          configService.get('DATA_PLATFORM_API_BASE_URL'),
          client
        );
      },
    },
    StartSessionCommandConsumer,
  ],
})
export class StartSessionCommandModule {}
