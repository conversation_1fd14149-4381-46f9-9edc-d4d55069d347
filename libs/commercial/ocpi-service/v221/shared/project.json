{"name": "commercial-ocpi-service-v221-shared", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/ocpi-service/v221/shared/src", "projectType": "library", "tags": ["commercial", "ocpi-service"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commercial/ocpi-service/v221/shared/jest.config.ts"}}}}