import 'whatwg-fetch';
import {
  AUTH_ACCOUNT_DISABLED_ERROR,
  AUTH_CONFIRM_NEW_PASSWORD_REQUIRED_ERROR,
  AUTH_INVALID_RESET_PASSWORD_CODE,
  AUTH_NEW_PASSWORD_REQUIRED_ERROR,
  AUTH_PASSWORDS_MISMATCH_ERROR,
  AUTH_PWNED_PASSWORD_ERROR,
  AUTH_WEAK_PASSWORD_ERROR,
} from '@experience/commercial/site-admin/domain/auth';
import { COMMON_INTERNAL_SERVER_ERROR } from '@experience/shared/typescript/validation';
import { ErrorBoundary } from '@sentry/nextjs';
import { confirmPasswordReset, verifyPasswordResetCode } from 'firebase/auth';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { pwnedPassword } from 'hibp';
import PasswordResetForm from './password-reset-form';

jest.mock('@experience/shared/next/firebase', () => ({
  getAuth: jest.fn(),
}));
jest.mock('firebase/auth');
jest.mock('hibp', () => ({ pwnedPassword: jest.fn() }));

const defaultProps = {
  oobCode: '0ilGllMg3iTk9pkGhVULmNDlpDJk-ApalGqHaHaOdFQAAAGJ',
};

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

const mockVerifyPasswordResetCode = jest.mocked(verifyPasswordResetCode);
const mockConfirmPasswordReset = jest.mocked(confirmPasswordReset);
const mockPwnedPassword = jest.mocked(pwnedPassword);

describe('PasswordResetForm', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<PasswordResetForm {...defaultProps} />);
    expect(baseElement).toBeDefined();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<PasswordResetForm {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should verify request, set password and show success component if valid form is submitted', async () => {
    mockPwnedPassword.mockResolvedValueOnce(0);

    render(<PasswordResetForm {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('New password'), {
      target: { value: 'password' },
    });
    fireEvent.change(screen.getByLabelText('Confirm new password'), {
      target: { value: 'password' },
    });
    fireEvent.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(
        screen.getByText(
          'Your password has been successfully changed. You can now log in with your new password.'
        )
      ).toBeInTheDocument();
      expect(mockPwnedPassword).toHaveBeenCalledTimes(1);
      expect(mockVerifyPasswordResetCode).toHaveBeenCalledTimes(1);
      expect(mockConfirmPasswordReset).toHaveBeenCalledTimes(1);
    });
  });

  it('should show validation error if new password field is empty', async () => {
    render(<PasswordResetForm {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Confirm new password'), {
      target: { value: 'password' },
    });
    fireEvent.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(
        screen.getByText(AUTH_NEW_PASSWORD_REQUIRED_ERROR)
      ).toBeInTheDocument();
      expect(mockPwnedPassword).not.toHaveBeenCalled();
      expect(mockVerifyPasswordResetCode).not.toHaveBeenCalled();
      expect(mockConfirmPasswordReset).not.toHaveBeenCalled();
    });
  });

  it('should show validation error if confirm new password field is empty', async () => {
    render(<PasswordResetForm {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('New password'), {
      target: { value: 'password' },
    });
    fireEvent.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(
        screen.getByText(AUTH_CONFIRM_NEW_PASSWORD_REQUIRED_ERROR)
      ).toBeInTheDocument();
      expect(mockPwnedPassword).not.toHaveBeenCalled();
      expect(mockVerifyPasswordResetCode).not.toHaveBeenCalled();
      expect(mockConfirmPasswordReset).not.toHaveBeenCalled();
    });
  });

  it('should show validation error if passwords do not match', async () => {
    render(<PasswordResetForm {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('New password'), {
      target: { value: 'password' },
    });
    fireEvent.change(screen.getByLabelText('Confirm new password'), {
      target: { value: 'password2' },
    });
    fireEvent.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(
        screen.getByText(AUTH_PASSWORDS_MISMATCH_ERROR)
      ).toBeInTheDocument();
      expect(mockPwnedPassword).not.toHaveBeenCalled();
      expect(mockVerifyPasswordResetCode).not.toHaveBeenCalled();
      expect(mockConfirmPasswordReset).not.toHaveBeenCalled();
    });
  });

  it.each([
    ['auth/password-does-not-meet-requirements', AUTH_WEAK_PASSWORD_ERROR],
    ['auth/weak-password', AUTH_WEAK_PASSWORD_ERROR],
    ['auth/network-request-failed', COMMON_INTERNAL_SERVER_ERROR],
    ['auth/expired-action-code', AUTH_INVALID_RESET_PASSWORD_CODE],
    ['auth/invalid-action-code', AUTH_INVALID_RESET_PASSWORD_CODE],
    ['auth/user-disabled', AUTH_ACCOUNT_DISABLED_ERROR],
  ])('should handle firebase authentication error: %s', async (code, error) => {
    mockPwnedPassword.mockResolvedValueOnce(0);
    mockConfirmPasswordReset.mockRejectedValueOnce({
      code: code,
    });

    render(<PasswordResetForm {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('New password'), {
      target: { value: 'password' },
    });
    fireEvent.change(screen.getByLabelText('Confirm new password'), {
      target: { value: 'password' },
    });
    fireEvent.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(screen.getByText(error)).toBeInTheDocument();
      expect(mockPwnedPassword).toHaveBeenCalledTimes(1);
    });
  });

  it('should show pwned password error if the given password has been exposed', async () => {
    mockPwnedPassword.mockResolvedValueOnce(1);

    render(<PasswordResetForm {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('New password'), {
      target: { value: 'password' },
    });
    fireEvent.change(screen.getByLabelText('Confirm new password'), {
      target: { value: 'password' },
    });
    fireEvent.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(screen.getByText(AUTH_PWNED_PASSWORD_ERROR)).toBeInTheDocument();
      expect(mockPwnedPassword).toHaveBeenCalledTimes(1);
    });
  });

  it('should handle unexpected error when checking if password has been pwned', async () => {
    mockPwnedPassword.mockRejectedValueOnce(new Error());

    render(<PasswordResetForm {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('New password'), {
      target: { value: 'password' },
    });
    fireEvent.change(screen.getByLabelText('Confirm new password'), {
      target: { value: 'password' },
    });
    fireEvent.click(screen.getByText('Confirm'));

    await waitFor(() => {
      expect(
        screen.getByText(
          'Your password has been successfully changed. You can now log in with your new password.'
        )
      ).toBeInTheDocument();
      expect(mockPwnedPassword).toHaveBeenCalledTimes(1);
      expect(mockVerifyPasswordResetCode).toHaveBeenCalledTimes(1);
      expect(mockConfirmPasswordReset).toHaveBeenCalledTimes(1);
    });
  });

  it('should handle unexpected error when verifying password reset code', async () => {
    mockPwnedPassword.mockResolvedValueOnce(0);
    mockVerifyPasswordResetCode.mockRejectedValueOnce({
      code: 'auth/internal-error',
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <PasswordResetForm {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.change(screen.getByLabelText('New password'), {
      target: { value: 'password' },
    });
    fireEvent.change(screen.getByLabelText('Confirm new password'), {
      target: { value: 'password' },
    });

    try {
      fireEvent.click(screen.getByText('Confirm'));
    } catch (error) {
      await waitFor(() => {
        expect(
          screen.getByRole('heading', { level: 1, name: 'Error detected' })
        ).toBeInTheDocument();
        expect(mockPwnedPassword).toHaveBeenCalledTimes(1);
        expect(mockVerifyPasswordResetCode).toHaveBeenCalledTimes(1);
        expect(mockConfirmPasswordReset).not.toHaveBeenCalled();
      });
    }
  });

  it('should handle unexpected error when confirming password reset', async () => {
    mockPwnedPassword.mockResolvedValueOnce(0);
    mockConfirmPasswordReset.mockRejectedValueOnce({
      code: 'auth/internal-error',
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <PasswordResetForm {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.change(screen.getByLabelText('New password'), {
      target: { value: 'password' },
    });
    fireEvent.change(screen.getByLabelText('Confirm new password'), {
      target: { value: 'password' },
    });

    try {
      fireEvent.click(screen.getByText('Confirm'));
    } catch (error) {
      await waitFor(() => {
        expect(
          screen.getByRole('heading', { level: 1, name: 'Error detected' })
        ).toBeInTheDocument();
        expect(mockPwnedPassword).toHaveBeenCalledTimes(1);
        expect(mockVerifyPasswordResetCode).toHaveBeenCalledTimes(1);
        expect(mockConfirmPasswordReset).toHaveBeenCalledTimes(1);
      });
    }
  });
});
