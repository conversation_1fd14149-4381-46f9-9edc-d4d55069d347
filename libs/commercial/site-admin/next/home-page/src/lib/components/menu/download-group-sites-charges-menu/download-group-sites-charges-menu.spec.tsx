import { render, screen } from '@testing-library/react';
import DownloadGroupSitesChargesMenu from './download-group-sites-charges-menu';
import userEvent from '@testing-library/user-event';

describe('DownloadGroupSitesChargesMenu', () => {
  const mockOpen = jest.fn();

  it('should render correctly', () => {
    const { baseElement } = render(<DownloadGroupSitesChargesMenu />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<DownloadGroupSitesChargesMenu />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should download a csv with the group sites charges for the current month', async () => {
    jest.spyOn(window, 'open').mockImplementation(mockOpen);

    render(<DownloadGroupSitesChargesMenu />);

    await userEvent.click(screen.getByRole('button', { name: 'Download CSV' }));
    await userEvent.click(
      screen.getByRole('menuitem', { name: 'Current month' })
    );

    expect(mockOpen).toHaveBeenCalledWith(
      `/api/user/group/charges/csv`,
      '_blank'
    );
  });
});
