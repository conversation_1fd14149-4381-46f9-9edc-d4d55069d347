import { Tariff } from '@experience/commercial/site-admin/typescript/domain-model';
import { useParams } from 'next/navigation';
import { useSWRWithErrorHandling } from '@experience/shared/react/hooks';
import TariffSummary from '../../components/tariff-summary/tariff-summary';

export const TariffPage = () => {
  const { id } = useParams();
  const { data, fallback } = useSWRWithErrorHandling<Tariff>(
    `/api/tariffs/${id}`
  );

  if (!data) return fallback;

  return <TariffSummary tariff={data} />;
};

export default TariffPage;
