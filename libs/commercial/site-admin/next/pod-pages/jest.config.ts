/* eslint-disable */
export default {
  displayName: 'commercial-site-admin-next-pod-pages',
  preset: '../../../../../jest.preset.js',
  transform: {
    '^(?!.*\\.(js|jsx|ts|tsx|css|json)$)': '@nx/react/plugins/jest',
    '^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@nx/react/babel'] }],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  coverageDirectory:
    '../../../../../coverage/libs/commercial/site-admin/next/pod-pages',
  setupFilesAfterEnv: ['@testing-library/jest-dom'],
};
