import { COMMON_REQUIRED_ERROR } from '@experience/shared/typescript/validation';
import { ErrorBoundary } from '@sentry/react';
import {
  POD_NOT_FOUND_ERROR,
  PodErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import AssignPodModal from './assign-pod-modal';
import axios from 'axios';

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockRouter = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter(),
}));

const mockSetOpen = jest.fn();

const defaultProps = {
  advertId: 1,
  open: true,
  setOpen: mockSetOpen,
};

describe('AssignPodModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(<AssignPodModal {...defaultProps} />);

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<AssignPodModal {...defaultProps} />);

    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal if cancel button is clicked', () => {
    render(<AssignPodModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should call API and when form is submitted if input is valid', async () => {
    render(<AssignPodModal {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: 'Assign charger' });

    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'Hank-Neil' },
    });
    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosPost).toHaveBeenCalledWith('/api/adverts/1/pods', {
        name: 'Hank-Neil',
      });
      expect(mockMutate).toHaveBeenCalledWith('/api/adverts/1');
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should show validation message on form submission if the charger name input field is empty', async () => {
    render(<AssignPodModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Assign charger' }));

    expect(await screen.findByText(COMMON_REQUIRED_ERROR)).toBeInTheDocument();
  });

  it('should show validation message on form submission if the charger does not exist', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        data: {
          error: PodErrorCodes.POD_NOT_FOUND_ERROR,
          message: POD_NOT_FOUND_ERROR,
          statusCode: 400,
        },
        status: 400,
      },
    });

    render(<AssignPodModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'Jake-Mary' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Assign charger' }));

    expect(await screen.findByText(POD_NOT_FOUND_ERROR)).toBeInTheDocument();
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <AssignPodModal {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'Jake-Mary' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Assign charger' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should autofocus the first input', () => {
    render(<AssignPodModal {...defaultProps} />);
    expect(screen.getByLabelText('Name')).toHaveFocus();
  });
});
