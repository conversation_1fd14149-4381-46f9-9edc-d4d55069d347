import { Button } from '@experience/shared/react/design-system';
import { useState } from 'react';
import AssignPodModal from '../../modals/assign-pod-modal/assign-pod-modal';

export interface AssignPodMenuProps {
  advertId: number;
}

export const AssignPodMenu = ({ advertId }: AssignPodMenuProps) => {
  const [openAssignPod, setOpenAssignPod] = useState(false);

  return (
    <>
      <Button onClick={() => setOpenAssignPod(true)} className="ml-20">
        Assign charger
      </Button>
      <AssignPodModal
        advertId={advertId}
        open={openAssignPod}
        setOpen={setOpenAssignPod}
      />
    </>
  );
};

export default AssignPodMenu;
