import { DriverExpensesTable } from '../../../components/driver-expenses/table';
import { DriverExpensesTableActions } from '../../../components/driver-expenses/table-actions';
import { ErrorPage } from '@experience/shared/react/error-pages';
import { LoadingOverlay } from '@experience/shared/react/design-system';
import { PageHeader } from '@experience/shared/react/headings';
import { TotalStats } from '../../../components/driver-expenses/total-stats';
import { useParams } from 'next/navigation';
import useSWR from 'swr';

export const DriverExpensesPage = () => {
  const { id } = useParams();

  const {
    data: { driver, submittedCharges, totalCost, totalUsage } = {},
    error,
  } = useSWR(`/api/expenses/drivers/${id}`);

  if (error) return <ErrorPage />;
  if (!driver || !submittedCharges) return <LoadingOverlay />;

  return (
    <>
      <div className="flex flex-col space-y-4 pb-4">
        <PageHeader
          heading={driver.fullName}
          subHeading="Expenses submitted by this driver."
          action={
            <DriverExpensesTableActions
              data={submittedCharges}
              driver={driver}
            />
          }
        />
        <TotalStats totalCost={totalCost} totalUsage={totalUsage} />
      </div>
      <DriverExpensesTable data={submittedCharges} />
    </>
  );
};

export default DriverExpensesPage;
