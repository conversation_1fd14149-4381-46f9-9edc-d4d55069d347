import { Group } from '@experience/commercial/site-admin/typescript/domain-model';
import { LoadingOverlay } from '@experience/shared/react/design-system';
import { useSession } from 'next-auth/react';
import React, { createContext, useContext } from 'react';
import useSWR from 'swr';

interface GroupsProviderProps {
  children: React.ReactNode;
}

export const GroupsContext = createContext<Group[] | undefined>(undefined);

export const GroupsProvider = ({ children }: GroupsProviderProps) => {
  const { status } = useSession();
  const { data: groups, isLoading } = useSWR<Group[]>(
    status === 'authenticated' ? '/api/user/groups' : null
  );

  if (isLoading && !groups) {
    return <LoadingOverlay />;
  }

  return (
    <GroupsContext.Provider value={groups}>{children}</GroupsContext.Provider>
  );
};

export default GroupsProvider;

export const useGroups = (): Group[] | undefined => useContext(GroupsContext);
