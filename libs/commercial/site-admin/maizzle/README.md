# Site Admin Maizzle

This library uses the Maizzle framework to compile Tailwind styled emails using templates.

This library was generated with [Nx](https://nx.dev).

## Build emails:

```
nx run commercial-site-admin-maizzle:build
```

This outputs the compiled emails to assets/site-admin-api/email-templates, as defined in the config.js

## Serve

```
nx run commercial-site-admin-maizzle:serve
```

This starts a dev server on port 3000, so compiled email templates can be viewed

### Documentation

Maizzle documentation is available at https://maizzle.com

#### Adding New Languages

1. Create a new `config.[language].js` file and update the directory to match.
2. Update `project.json` with a build step for your language.
