{"name": "commercial-site-admin-nest-support-module", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/site-admin/nest/support-module/src", "projectType": "library", "tags": ["commercial", "site-admin"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commercial/site-admin/nest/support-module/jest.config.ts", "passWithNoTests": false}, "dependsOn": [{"projects": ["commercial-site-admin-prisma-rfid-client"], "target": "generate-sources:prisma"}]}}}