import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  SerializeOptions,
  ValidationPipe,
} from '@nestjs/common';
import {
  Driver,
  ExpensableCharge,
  ExpensableChargeDriverSummary,
  SubmittedCharge,
  TotalCost,
  TotalUsage,
} from '@experience/shared/axios/data-platform-api-client';
import { ExpenseService } from './expense.service';
import { FleetUsageResponse } from '@experience/shared/axios/data-platform-api-client';
import { ProcessExpensesRequest } from './expense.requests';

@Controller('expenses')
@SerializeOptions({ exposeUnsetFields: false })
export class ExpenseController {
  constructor(private expenseService: ExpenseService) {}

  @Get('/new')
  async findNewByGroup(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Query('year', ParseIntPipe) year: number,
    @Query('month', ParseIntPipe) month: number
  ): Promise<ExpensableCharge[]> {
    return this.expenseService.findNewByGroup(groupUid, year, month);
  }

  @Get('/processed')
  async findProcessedByGroup(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Query('year', ParseIntPipe) year: number,
    @Query('month', ParseIntPipe) month: number
  ): Promise<ExpensableCharge[]> {
    return this.expenseService.findProcessedByGroup(groupUid, year, month);
  }

  @Get('/new/grouped')
  async findNewByGroupGroupedByDriver(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Query('year', ParseIntPipe) year: number,
    @Query('month', ParseIntPipe) month: number
  ): Promise<ExpensableChargeDriverSummary[]> {
    return this.expenseService.findNewByGroupGroupedByDriver(
      groupUid,
      year,
      month
    );
  }

  @Get('/drivers/:driverId')
  async findAllByGroupUidAndDriverId(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Param('driverId', ParseIntPipe) driverId: number
  ): Promise<{
    driver?: Driver;
    submittedCharges?: SubmittedCharge[];
    totalCost?: TotalCost;
    totalUsage?: TotalUsage;
  }> {
    return this.expenseService.findAllByGroupUidAndDriverId(groupUid, driverId);
  }

  @Post('process')
  async markExpensesAsProcessed(
    @Query('adminId', ParseIntPipe) adminId: number,
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Body(ValidationPipe) request: ProcessExpensesRequest
  ): Promise<void> {
    return this.expenseService.processManyForGroupUidAndUser(
      groupUid,
      adminId,
      request
    );
  }

  @Get('stats/monthly')
  async getMonthlyUsageForOrganisation(
    @Query('groupUid', ParseUUIDPipe) groupUid: string
  ): Promise<FleetUsageResponse> {
    return this.expenseService.getMonthlyUsageForOrganisation(groupUid);
  }
}
