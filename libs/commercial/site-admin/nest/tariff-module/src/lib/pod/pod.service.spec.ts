import {
  Pod,
  TEST_ASSIGN_TARIFF_PODS_REQUEST,
  TEST_GROUP,
  TEST_POD,
  TEST_POD_WITH_DIFFERENT_ID_AND_NAME,
  TEST_POD_WITH_DIFFERENT_PPID,
  TEST_TARIFF,
  TEST_TARIFF_DRIVER_ENERGY_SCHEDULE,
  TEST_TARIFF_DRIVER_FIXED_SCHEDULE,
  TEST_TARIFF_WITH_ENERGY_SCHEDULE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { PodLocations } from '@experience/shared/sequelize/podadmin';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { PodService } from '@experience/commercial/site-admin/nest/pod-module';
import { TariffIncompatiblePodException } from './pod.exception';
import { TariffPodService } from './pod.service';
import { TariffService } from '../tariff.service';
import { Test, TestingModule } from '@nestjs/testing';

jest.mock('@experience/commercial/site-admin/nest/pod-module');
jest.mock('../tariff.service');

describe('TariffPodService', () => {
  let tariffPodService: TariffPodService;
  let podService: PodService;
  let tariffService: TariffService;
  let repository: typeof PodLocations;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TariffPodService,
        {
          provide: PodService,
          useClass: PodService,
        },
        {
          provide: TariffService,
          useClass: TariffService,
        },
        {
          provide: 'POD_LOCATIONS_REPOSITORY',
          useValue: PodLocations,
        },
      ],
    }).compile();

    tariffPodService = module.get<TariffPodService>(TariffPodService);
    tariffService = module.get<TariffService>(TariffService);
    podService = module.get<PodService>(PodService);
    repository = module.get<typeof PodLocations>('POD_LOCATIONS_REPOSITORY');
  });

  it('should be defined', () => {
    expect(tariffPodService).toBeDefined();
  });

  it('should assign tariff based on pod id and group id', async () => {
    const mockUpdate = jest.spyOn(repository, 'update').mockResolvedValue([1]);
    jest
      .spyOn(tariffService, 'findByGroupIdAndTariffId')
      .mockResolvedValueOnce(TEST_TARIFF_WITH_ENERGY_SCHEDULE);
    jest
      .spyOn(podService, 'findByGroupId')
      .mockResolvedValueOnce([TEST_POD, TEST_POD_WITH_DIFFERENT_ID_AND_NAME]);

    await tariffPodService.updateByGroupIdAndTariffId(
      TEST_GROUP.id,
      TEST_TARIFF_WITH_ENERGY_SCHEDULE.id,
      TEST_ASSIGN_TARIFF_PODS_REQUEST
    );

    expect(mockUpdate).toHaveBeenCalledTimes(2);
    expect(mockUpdate.mock.calls).toEqual([
      [
        { revenueProfileId: null },
        { where: { revenueProfileId: TEST_TARIFF.id } },
      ],
      [
        { revenueProfileId: TEST_TARIFF.id, paygEnabled: 1 },
        { where: { id: TEST_POD.id } },
      ],
    ]);
  });

  it('should assign tariff to multiple pods based on pod id and group id', async () => {
    const mockUpdate = jest.spyOn(repository, 'update').mockResolvedValue([1]);
    jest
      .spyOn(tariffService, 'findByGroupIdAndTariffId')
      .mockResolvedValueOnce(TEST_TARIFF_WITH_ENERGY_SCHEDULE);
    jest
      .spyOn(podService, 'findByGroupId')
      .mockResolvedValueOnce([TEST_POD, TEST_POD_WITH_DIFFERENT_ID_AND_NAME]);

    await tariffPodService.updateByGroupIdAndTariffId(
      TEST_GROUP.id,
      TEST_TARIFF_WITH_ENERGY_SCHEDULE.id,
      { podIds: [TEST_POD.id, TEST_POD_WITH_DIFFERENT_ID_AND_NAME.id] }
    );

    expect(mockUpdate).toHaveBeenCalledTimes(3);
    expect(mockUpdate.mock.calls).toEqual([
      [
        { revenueProfileId: null },
        { where: { revenueProfileId: TEST_TARIFF.id } },
      ],
      [
        { revenueProfileId: TEST_TARIFF.id, paygEnabled: 1 },
        { where: { id: TEST_POD.id } },
      ],
      [
        { revenueProfileId: TEST_TARIFF.id, paygEnabled: 1 },
        { where: { id: TEST_POD_WITH_DIFFERENT_ID_AND_NAME.id } },
      ],
    ]);
  });

  it('should only call update once if no pods to assign', async () => {
    const mockUpdate = jest.spyOn(repository, 'update').mockResolvedValue([1]);

    jest
      .spyOn(tariffService, 'findByGroupIdAndTariffId')
      .mockResolvedValueOnce(TEST_TARIFF);
    jest
      .spyOn(podService, 'findByGroupId')
      .mockResolvedValueOnce([TEST_POD, TEST_POD_WITH_DIFFERENT_ID_AND_NAME]);

    await tariffPodService.updateByGroupIdAndTariffId(
      TEST_GROUP.id,
      TEST_TARIFF.id,
      { podIds: [] }
    );

    expect(mockUpdate).toHaveBeenCalledTimes(1);
    expect(mockUpdate.mock.calls).toEqual([
      [
        { revenueProfileId: null },
        { where: { revenueProfileId: TEST_TARIFF.id } },
      ],
    ]);
  });

  it('should not enable payg if pod supports ocpp', async () => {
    const mockUpdate = jest.spyOn(repository, 'update').mockResolvedValue([1]);
    jest
      .spyOn(tariffService, 'findByGroupIdAndTariffId')
      .mockResolvedValueOnce(TEST_TARIFF_WITH_ENERGY_SCHEDULE);
    jest
      .spyOn(podService, 'findByGroupId')
      .mockResolvedValueOnce([{ ...TEST_POD, supportsOcpp: true }]);

    await tariffPodService.updateByGroupIdAndTariffId(
      TEST_GROUP.id,
      TEST_TARIFF_WITH_ENERGY_SCHEDULE.id,
      TEST_ASSIGN_TARIFF_PODS_REQUEST
    );

    expect(mockUpdate).toHaveBeenCalledTimes(2);
    expect(mockUpdate.mock.calls).toEqual([
      [
        { revenueProfileId: null },
        { where: { revenueProfileId: TEST_TARIFF.id } },
      ],
      [
        { revenueProfileId: TEST_TARIFF.id, paygEnabled: undefined },
        { where: { id: TEST_POD.id } },
      ],
    ]);
  });

  it('should work the same with ppids', async () => {
    const mockUpdate = jest.spyOn(repository, 'update').mockResolvedValue([1]);

    jest
      .spyOn(podService, 'findByGroupId')
      .mockResolvedValueOnce([TEST_POD, TEST_POD_WITH_DIFFERENT_PPID]);
    jest
      .spyOn(tariffService, 'findByGroupIdAndTariffId')
      .mockResolvedValueOnce(TEST_TARIFF_WITH_ENERGY_SCHEDULE);

    await tariffPodService.updateByGroupIdAndTariffId(
      TEST_GROUP.id,
      TEST_TARIFF_WITH_ENERGY_SCHEDULE.id,
      { ...TEST_ASSIGN_TARIFF_PODS_REQUEST, podIds: ['BAR'] }
    );

    expect(mockUpdate).toHaveBeenCalledTimes(2);
    expect(mockUpdate.mock.calls).toEqual([
      [
        { revenueProfileId: null },
        { where: { revenueProfileId: TEST_TARIFF.id } },
      ],
      [
        { revenueProfileId: TEST_TARIFF.id, paygEnabled: 1 },
        { where: { id: TEST_POD.id } },
      ],
    ]);
  });

  it('should throw an exception if tariff is using energy pricing model when assigning to a pod which does not support energy pricing', async () => {
    jest
      .spyOn(podService, 'findByGroupId')
      .mockResolvedValueOnce([{ ...TEST_POD, supportsPerKwh: false } as Pod]);
    jest
      .spyOn(tariffService, 'findByGroupIdAndTariffId')
      .mockResolvedValueOnce({
        ...TEST_TARIFF,
        schedule: { drivers: [TEST_TARIFF_DRIVER_ENERGY_SCHEDULE] },
      });

    await expect(
      tariffPodService.updateByGroupIdAndTariffId(
        TEST_GROUP.id,
        TEST_TARIFF.id,
        TEST_ASSIGN_TARIFF_PODS_REQUEST
      )
    ).rejects.toThrow(TariffIncompatiblePodException);
  });

  it('should throw an exception if tariff is using non-energy pricing model when assigning to a public pod', async () => {
    jest
      .spyOn(podService, 'findByGroupId')
      .mockResolvedValueOnce([{ ...TEST_POD, isPublic: true } as Pod]);
    jest
      .spyOn(tariffService, 'findByGroupIdAndTariffId')
      .mockResolvedValueOnce({
        ...TEST_TARIFF,
        schedule: { drivers: [TEST_TARIFF_DRIVER_FIXED_SCHEDULE] },
      });

    await expect(
      tariffPodService.updateByGroupIdAndTariffId(
        TEST_GROUP.id,
        TEST_TARIFF.id,
        TEST_ASSIGN_TARIFF_PODS_REQUEST
      )
    ).rejects.toThrow(TariffIncompatiblePodException);
  });

  it('should not throw an exception if pod is incompatible with tariff but is not being assigned', async () => {
    const mockUpdate = jest.spyOn(repository, 'update').mockResolvedValue([1]);

    jest
      .spyOn(podService, 'findByGroupId')
      .mockResolvedValueOnce([{ ...TEST_POD, supportsPerKwh: false } as Pod]);
    jest
      .spyOn(tariffService, 'findByGroupIdAndTariffId')
      .mockResolvedValueOnce({
        ...TEST_TARIFF,
        schedule: { drivers: [TEST_TARIFF_DRIVER_ENERGY_SCHEDULE] },
      });

    await tariffPodService.updateByGroupIdAndTariffId(
      TEST_GROUP.id,
      TEST_TARIFF.id,
      {
        podIds: [],
      }
    );

    expect(mockUpdate).toHaveBeenCalledTimes(1);
    expect(mockUpdate.mock.calls).toEqual([
      [
        { revenueProfileId: null },
        { where: { revenueProfileId: TEST_TARIFF.id } },
      ],
    ]);
  });
});
