import { ChargeService } from './charge.service';
import {
  ChargeStatisticsApi,
  ChargingStatisticsApi as ChargingStatisticsApiLegacy,
  Configuration,
  DriverChargesApi,
  UserChargesApi,
} from '@experience/shared/axios/data-platform-api-client';
import { ChargeSummaryService } from './charge-summary.service';
import {
  Charges,
  ClaimedCharges,
  PodadminSequelizeModule,
} from '@experience/shared/sequelize/podadmin';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ConfirmChargeService } from './confirm-charge/confirm-charge.service';
import {
  Configuration as DataPlatformConfiguration,
  ProjectionChargesApi as DataPlatformProjectionChargesApi,
} from '@experience/shared/axios/data-platform-api-client';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { DriverModule } from '@experience/commercial/site-admin/nest/driver-module';
import { Module, forwardRef } from '@nestjs/common';
import axios from 'axios';

@Module({
  imports: [
    ConfigModule,
    PodadminSequelizeModule,
    forwardRef(() => DriverModule),
  ],
  providers: [
    ChargeService,
    ChargeSummaryService,
    ConfirmChargeService,
    {
      provide: 'CHARGES_REPOSITORY',
      useValue: Charges,
    },
    {
      provide: 'CLAIMED_CHARGES_REPOSITORY',
      useValue: ClaimedCharges,
    },
    {
      inject: [ConfigService],
      provide: DriverChargesApi,
      useFactory: async (configService: ConfigService) =>
        new DriverChargesApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
    {
      inject: [ConfigService],
      provide: ChargingStatisticsApiLegacy,
      useFactory: async (configService: ConfigService) =>
        new ChargingStatisticsApiLegacy(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
    {
      inject: [ConfigService],
      provide: ChargeStatisticsApi,
      useFactory: async (configService: ConfigService) =>
        new ChargeStatisticsApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
    {
      inject: [ConfigService],
      provide: DataPlatformProjectionChargesApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('DATA_PLATFORM_API_TIMEOUT', 10000),
        });
        return new DataPlatformProjectionChargesApi(
          new DataPlatformConfiguration(),
          configService.get('DATA_PLATFORM_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: UserChargesApi,
      useFactory: async (configService: ConfigService) =>
        new UserChargesApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
  ],
  exports: [ChargeService, ChargeSummaryService, ConfirmChargeService],
})
export class ChargeModule {}
