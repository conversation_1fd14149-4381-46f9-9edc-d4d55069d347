import { TEST_CREATE_DOMAIN_REQUEST } from '@experience/commercial/site-admin/typescript/domain-model';
import axios from 'axios';

export const describeDomainModule = (baseUrl: string) => {
  describe('domain module', () => {
    describe('domain controller', () => {
      it('should find a list of domains', async () => {
        const response = await axios.get(
          `${baseUrl}/domains?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });

      it('should add a new domain', async () => {
        const response = await axios.post(
          `${baseUrl}/domains?authId=66154039-e231-40f5-8daa-6bb68e6b1890`,
          TEST_CREATE_DOMAIN_REQUEST,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        expect(response.status).toEqual(201);
        expect(response.data).toMatchSnapshot({
          activatedOn: expect.stringMatching(
            /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2}(?:\.\d*)?)((-(\d{2}):(\d{2})|Z)?)$/
          ),
          id: expect.any(Number),
        });
      });

      it('should edit an existing domain', async () => {
        const updateResponse = await axios.put(
          `${baseUrl}/domains/23?authId=66154039-e231-40f5-8daa-6bb68e6b1890`,
          { domainName: 'testing2.com' },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        expect(updateResponse.status).toEqual(204);

        const response = await axios.get(
          `${baseUrl}/domains?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );

        expect(response.status).toEqual(200);
        expect({ data: response.data }).toMatchSnapshot({
          data: [
            {
              activatedOn: expect.stringMatching(
                /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2}(?:\.\d*)?)((-(\d{2}):(\d{2})|Z)?)$/
              ),
              id: expect.any(Number),
            },
            {
              activatedOn: expect.stringMatching(
                /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2}(?:\.\d*)?)((-(\d{2}):(\d{2})|Z)?)$/
              ),
              id: expect.any(Number),
            },
          ],
        });
      });

      it('should delete an existing domain', async () => {
        const deleteResponse = await axios.delete(
          `${baseUrl}/domains/23?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );

        expect(deleteResponse.status).toEqual(204);

        const response = await axios.get(
          `${baseUrl}/domains?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );

        expect(response.status).toEqual(200);
        expect({ data: response.data }).toMatchSnapshot({
          data: [
            {
              activatedOn: expect.stringMatching(
                /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2}(?:\.\d*)?)((-(\d{2}):(\d{2})|Z)?)$/
              ),
              id: expect.any(Number),
            },
          ],
        });
      });
    });
  });
};
