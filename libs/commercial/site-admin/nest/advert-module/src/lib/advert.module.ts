import {
  AdminMiddleware,
  AdminModule,
} from '@experience/commercial/site-admin/nest/admin-module';
import { AdvertController } from './advert.controller';
import { AdvertService } from './advert.service';
import {
  Adverts,
  PodLocations,
  PodadminSequelizeModule,
} from '@experience/shared/sequelize/podadmin';
import { MiddlewareConsumer, Module } from '@nestjs/common';

@Module({
  imports: [AdminModule, PodadminSequelizeModule],
  controllers: [AdvertController],
  providers: [
    AdvertService,
    {
      provide: 'ADVERTS_REPOSITORY',
      useValue: Adverts,
    },
    {
      provide: 'POD_LOCATIONS_REPOSITORY',
      useValue: PodLocations,
    },
  ],
})
export class AdvertModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminMiddleware).forRoutes(AdvertController);
  }
}
