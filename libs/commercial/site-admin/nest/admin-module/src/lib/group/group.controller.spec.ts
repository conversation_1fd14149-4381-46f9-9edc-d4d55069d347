import { CacheModule } from '@nestjs/cache-manager';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { ChargeService } from '@experience/commercial/site-admin/nest/charge-module';
import { GroupController } from './group.controller';
import { GroupService } from './group.service';
import { INestApplication } from '@nestjs/common';
import {
  TEST_GROUP,
  TEST_GROUP_WITH_STATS,
  TEST_PROJECTION_CHARGES,
  TEST_USER,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { Test, TestingModule } from '@nestjs/testing';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import MockDate from 'mockdate';
import request from 'supertest';

jest.mock('./group.service');
jest.mock('@experience/commercial/site-admin/nest/charge-module');

describe('GroupController', () => {
  let app: INestApplication;
  let chargeService: ChargeService;
  let groupService: GroupService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register()],
      controllers: [GroupController],
      providers: [
        { provide: GroupService, useClass: GroupService },
        { provide: ChargeService, useClass: ChargeService },
      ],
    }).compile();

    groupService = module.get<GroupService>(GroupService);
    chargeService = module.get<ChargeService>(ChargeService);

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();

    MockDate.set(new Date(2023, 2, 20));
  });

  afterEach(() => {
    MockDate.reset();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should find by group id', async () => {
    const mockService = jest
      .spyOn(groupService, 'findByGroupIdWithStats')
      .mockResolvedValueOnce(TEST_GROUP_WITH_STATS);

    await request(app.getHttpServer())
      .get(`/user/group?groupId=${TEST_USER.groupId}`)

      .expect(200)
      .expect({
        ...TEST_GROUP_WITH_STATS,
        activatedOn: TEST_GROUP_WITH_STATS.activatedOn?.toISOString(),
      });

    expect(mockService).toHaveBeenCalledWith(TEST_USER.groupId);
  });

  it.each([null, undefined, 'one'])(
    'should throw a validation error when auth id is %s when finding by group id',
    (groupId) =>
      request(app.getHttpServer())
        .get(`/user/group?groupId=${groupId}`)
        .expect(400)
        .expect({
          statusCode: 400,
          message: 'Validation failed (numeric string is expected)',
          error: 'Bad Request',
        })
  );

  it.each([null, undefined, 'one'])(
    'should throw a validation error when auth id is %s when finding all by group id',
    (groupId) =>
      request(app.getHttpServer())
        .get(`/user/groups?groupId=${groupId}`)
        .expect(400)
        .expect({
          statusCode: 400,
          message: 'Validation failed (numeric string is expected)',
          error: 'Bad Request',
        })
  );

  it('should find all by group id', async () => {
    jest
      .spyOn(groupService, 'findAllByGroupId')
      .mockResolvedValueOnce([TEST_GROUP]);

    return await request(app.getHttpServer())
      .get(`/user/groups?groupId=${TEST_USER.groupId}`)
      .expect(200)
      .expect([TEST_GROUP]);
  });

  it('should generate csv file of group charges for the current month', async () => {
    jest.spyOn(groupService, 'findByGroupId').mockResolvedValueOnce(TEST_GROUP);

    jest
      .spyOn(chargeService, 'findChargeDataFromProjectionsEndpoint')
      .mockResolvedValueOnce(TEST_PROJECTION_CHARGES);

    await request(app.getHttpServer())
      .get(
        `/user/group/charges/csv?groupId=${TEST_USER.groupId}&groupUid=${TEST_USER.groupUid}`
      )
      .expect(200);

    expect(
      chargeService.findChargeDataFromProjectionsEndpoint
    ).toHaveBeenCalledWith({
      groupId: TEST_GROUP.id,
      groupUid: TEST_GROUP.uid,
      sortBy: 'siteNamePodNamePluggedIn',
    });
  });

  it('should generate csv file of group charges for a specific month', async () => {
    jest.spyOn(groupService, 'findByGroupId').mockResolvedValueOnce(TEST_GROUP);

    jest
      .spyOn(chargeService, 'findChargeDataFromProjectionsEndpoint')
      .mockResolvedValueOnce(TEST_PROJECTION_CHARGES);

    await request(app.getHttpServer())
      .get(
        `/user/group/charges/csv?groupId=${TEST_USER.groupId}&groupUid=${TEST_USER.groupUid}&date=2023-01-01`
      )
      .expect(200);

    expect(
      chargeService.findChargeDataFromProjectionsEndpoint
    ).toHaveBeenCalledWith({
      date: '2023-01-01',
      groupId: TEST_GROUP.id,
      groupUid: TEST_GROUP.uid,
      sortBy: 'siteNamePodNamePluggedIn',
    });
  });
});
