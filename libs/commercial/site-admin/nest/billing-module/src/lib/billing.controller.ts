import { BillingInterceptor } from './billing.interceptor';
import { BillingService } from './billing.service';
import {
  Controller,
  Get,
  ParseUUIDPipe,
  Query,
  UseInterceptors,
} from '@nestjs/common';

@UseInterceptors(BillingInterceptor)
@Controller('billing')
export class BillingController {
  constructor(private billingService: BillingService) {}

  @Get()
  async findBillingInformationByGroupUid(
    @Query('groupUid', ParseUUIDPipe) groupUid: string
  ) {
    return this.billingService.findBillingInformationByGroupUid(groupUid);
  }
}
