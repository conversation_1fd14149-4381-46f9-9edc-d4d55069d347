import axios from 'axios';

export const describeBillingModule = (baseUrl: string) => {
  describe('billing module', () => {
    describe('billing controller', () => {
      it('should find billing information', async () => {
        const response = await axios.get(
          `${baseUrl}/billing?authId=973b5195-c2b8-4de0-9eab-381618e3ff74`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });
    });

    describe('billing statements controller', () => {
      it('should find billing statement pdf', async () => {
        const response = await axios.get(
          `${baseUrl}/billing/statements/4287245c-6a15-4891-afe7-7fcb7dd9fa7f/pdf?authId=973b5195-c2b8-4de0-9eab-381618e3ff74`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });
    });

    describe('billing invoices controller', () => {
      it('should find billing invoice pdf', async () => {
        const response = await axios.get(
          `${baseUrl}/billing/invoices/384fe8c9-c0b8-4a1a-a881-1ca2222d2be5/pdf?authId=973b5195-c2b8-4de0-9eab-381618e3ff74`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });
    });
  });
};
