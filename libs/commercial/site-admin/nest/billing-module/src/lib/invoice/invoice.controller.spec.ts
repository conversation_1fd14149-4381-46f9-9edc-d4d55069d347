import { BillingInvoiceController } from './invoice.controller';
import { BillingInvoiceService } from './invoice.service';
import { INestApplication } from '@nestjs/common';
import { TEST_BILLING_INFORMATION } from '@experience/commercial/site-admin/typescript/domain-model';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

jest.mock('./invoice.service');

describe('billing invoice controller', () => {
  let app: INestApplication;
  let billingInvoiceService: BillingInvoiceService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BillingInvoiceController],
      providers: [BillingInvoiceService],
    }).compile();

    billingInvoiceService = module.get<BillingInvoiceService>(
      BillingInvoiceService
    );

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should get an invoice PDF', async () => {
    const mockGetInvoicePdf = jest
      .spyOn(billingInvoiceService, 'getInvoicePdf')
      .mockResolvedValueOnce({
        filename: 'invoice.pdf',
        buffer: Buffer.from('pdf data'),
      });

    const [statement] = TEST_BILLING_INFORMATION.statements;
    const { invoiceId } = statement;

    await request(app.getHttpServer())
      .get(`/billing/invoices/${invoiceId}/pdf`)
      .expect(200)
      .expect('Content-Type', 'application/pdf')
      .expect('Content-Disposition', 'attachment; filename=invoice.pdf');

    expect(mockGetInvoicePdf).toHaveBeenCalledWith(invoiceId);
  });
});
