import { check } from 'k6';
import http from 'k6/http';

export const options = {
  thresholds: {
    checks: ['rate==1'],
  },
};

export default () => {
  /* global __ENV */
  const { AUTH_ID, BASE_URL } = __ENV;

  check(http.get(`${BASE_URL}/user?authId=${AUTH_ID}`), {
    'GET /user returns status 200': (r) => r.status === 200,
  });

  check(http.get(`${BASE_URL}/user/group?authId=${AUTH_ID}`), {
    'GET /user/group returns status 200': (r) => r.status === 200,
  });
};
