# WorkItem

## Properties

| Name                  | Type                          | Description | Notes                             |
| --------------------- | ----------------------------- | ----------- | --------------------------------- |
| **automated**         | **boolean**                   |             | [default to undefined]            |
| **groupUid**          | **string**                    |             | [default to undefined]            |
| **groupName**         | **string**                    |             | [default to undefined]            |
| **id**                | **string**                    |             | [default to undefined]            |
| **month**             | **string**                    |             | [default to undefined]            |
| **previousStatement** | [**Statement**](Statement.md) |             | [optional] [default to undefined] |
| **siteId**            | **string**                    |             | [default to undefined]            |
| **siteName**          | **string**                    |             | [default to undefined]            |
| **statement**         | [**Statement**](Statement.md) |             | [optional] [default to undefined] |
| **status**            | **string**                    |             | [default to undefined]            |
| **userId**            | **string**                    |             | [optional] [default to undefined] |
| **userName**          | **string**                    |             | [optional] [default to undefined] |

## Example

```typescript
import { WorkItem } from './api';

const instance: WorkItem = {
  automated,
  groupUid,
  groupName,
  id,
  month,
  previousStatement,
  siteId,
  siteName,
  statement,
  status,
  userId,
  userName,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
