import { InvoiceDto } from '../invoice.dto';
import { Statement } from '../statement.dto';
import { TEST_ADJUSTED_FEE } from './test-adjusted-fee';
import { TEST_GROUP } from './test-group';
import { TEST_SITE } from './test-site';
import { TEST_WORK_ITEM } from './test-work-items';
import { WorkItem } from '../work-item.dto';
import { WorkItemStatus } from '../../enums/workitem-status.enum';

export const getTestStatement = (
  invoice?: Partial<InvoiceDto>,
  workItem?: WorkItem,
  overrides?: Partial<Statement>
): Statement => ({
  id: '0a69aa8f-fa98-4488-a113-4ea8aca29e5d',
  invoice: invoice,
  adjustedFees: [TEST_ADJUSTED_FEE],
  automaticPayout: false,
  emails: '-',
  energy: {
    claimedEnergyDelivered: 60.7,
    energyDelivered: 40.2,
    paidEnergyDelivered: 56.1,
  },
  fees: {
    gross: 27,
    net: 56.9,
    vat: 40.2,
  },
  groupUid: '036c0720-f908-45fc-ae7c-031a47c2e278',
  numberOfCharges: 3,
  payoutStatus: 'PENDING',
  reference: 'ROSDH012023',
  revenue: {
    gross: 73.7,
    net: 97.5,
    vat: 34.7,
  },
  siteAddress: '28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK',
  workItem: workItem,
  ...overrides,
});

export const TEST_STATEMENT: Statement = getTestStatement(
  undefined,
  TEST_WORK_ITEM
);

export const TEST_STATEMENT_WITH_STRIPE_INVOICE: Statement = getTestStatement(
  {
    id: '32748f77-dec7-41eb-8b28-255a54e4397d',
    invoiceNumber: 'AF0000001',
    quoteNumber: 'AF001',
    invoiceDate: '2024-01-23',
    group: TEST_GROUP,
    site: TEST_SITE,
    statement: TEST_STATEMENT,
    stripeInvoiceNumber: 'ADF-4567',
    stripeInvoiceStatus: 'open',
  },
  TEST_WORK_ITEM,
  {
    payoutStatus: 'PENDING',
  }
);

export const TEST_STATEMENTS: Statement[] = [
  {
    id: '9bd247e0-92b9-11ee-b9d1-0242ac120002',
    invoice: {
      id: '5ab6cbf4-9902-4942-a052-b6a62bcdbaac',
      invoiceNumber: 'AF0000202',
      stripeInvoiceNumber: 'ADF-1001',
      stripeInvoiceStatus: 'open',
    },
    adjustedFees: [
      {
        fee: 80,
        ppid: 'PP-643463',
      },
    ],
    automaticPayout: false,
    emails: '-',
    energy: {
      claimedEnergyDelivered: 42.7,
      energyDelivered: 23.5,
      paidEnergyDelivered: 38.2,
    },
    fees: {
      gross: 7,
      net: 38.3,
      vat: 22.2,
    },
    groupUid: '725fead1-a43b-468b-a500-a279d8a47f95',
    numberOfCharges: 2,
    payoutStatus: 'PENDING',
    reference: 'PP0CH012023',
    revenue: {
      gross: 53.8,
      net: 73.5,
      vat: 14.4,
    },
    siteAddress: '135 Lucious Track',
    workItem: {
      automated: false,
      groupName: 'Group 1',
      groupUid: '57d2cab0-92b9-11ee-b9d1-0242ac120002',
      id: '9bd247e0-92b9-11ee-b9d1-0242ac120002',
      month: '2023-01-13',
      siteId: '62198536-92b9-11ee-b9d1-0242ac120002',
      siteName: 'Site 1',
      status: WorkItemStatus.SENT,
      userName: 'Bob Dylan',
      userId: '746daf89-abea-4640-9c67-0d89a5c57b64',
    },
  },
  {
    id: '369b1302-92b9-11ee-b9d1-0242ac120002',
    invoice: {
      id: '9be0da46-1462-42b5-85b1-d43cf64f339e',
      invoiceNumber: 'AF0000203',
      stripeInvoiceNumber: 'ADF-1002',
      stripeInvoiceStatus: 'overdue',
    },
    adjustedFees: [
      {
        fee: 112,
        ppid: 'PP-643463',
      },
    ],
    automaticPayout: false,
    emails: '-',
    energy: {
      claimedEnergyDelivered: 59.9,
      energyDelivered: 41.6,
      paidEnergyDelivered: 54.3,
    },
    fees: {
      gross: 21.1,
      net: 51.2,
      vat: 38.5,
    },
    groupUid: '725fead1-a43b-468b-a500-a279d8a47f95',
    numberOfCharges: 2,
    payoutStatus: 'TRANSFERRED',
    reference: 'PP0CH022023',
    revenue: {
      gross: 66.8,
      net: 92.5,
      vat: 31.4,
    },
    siteAddress: '135 Lucious Track',
    workItem: {
      automated: false,
      groupName: 'Group 1',
      groupUid: '57d2cab0-92b9-11ee-b9d1-0242ac120002',
      id: '369b1302-92b9-11ee-b9d1-0242ac120002',
      month: '2023-02-13',
      siteId: '62198536-92b9-11ee-b9d1-0242ac120002',
      siteName: 'Site 1',
      status: WorkItemStatus.SENT,
      userName: 'Bob Dylan',
      userId: '746daf89-abea-4640-9c67-0d89a5c57b64',
    },
  },
  {
    id: 'f7c7552c-92b9-11ee-b9d1-0242ac120002',
    invoice: {
      id: 'ff407cc1-6898-4559-a220-ae66acb3f391',
      invoiceNumber: 'AF0000204',
      stripeInvoiceNumber: 'ADF-1003',
      stripeInvoiceStatus: 'paid',
    },
    adjustedFees: [
      {
        fee: 141,
        ppid: 'PP-885381',
      },
    ],
    automaticPayout: false,
    emails: '-',
    energy: {
      claimedEnergyDelivered: 72.1,
      energyDelivered: 59.6,
      paidEnergyDelivered: 74.1,
    },
    fees: {
      gross: 31.1,
      net: 61.2,
      vat: 48.5,
    },
    numberOfCharges: 2,
    payoutStatus: 'PAID_OUT',
    reference: 'PP0CH022023',
    revenue: {
      gross: 56.8,
      net: 82.9,
      vat: 24.3,
    },
    groupUid: '725fead1-a43b-468b-a500-a279d8a47f95',
    siteAddress: '28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK',
    workItem: {
      automated: false,
      groupName: 'Pod Point',
      groupUid: 'fefd9c70-92b9-11ee-b9d1-0242ac120002',
      id: 'f7c7552c-92b9-11ee-b9d1-0242ac120002',
      month: '2023-02-13',
      siteId: '02973742-92ba-11ee-b9d1-0242ac120002',
      siteName: 'Banner Street',
      status: WorkItemStatus.SENT,
      userName: 'Bob Dylan',
      userId: '746daf89-abea-4640-9c67-0d89a5c57b64',
    },
  },
  {
    id: 'f7c7552c-92b9-11ee-b9d1-0242ac121234',
    invoice: {
      id: 'ff407cc1-6898-4559-a220-ae66acb3f111',
      invoiceNumber: 'AF0000205',
      stripeInvoiceNumber: 'ADF-1004',
      stripeInvoiceStatus: 'paid',
    },
    automaticPayout: false,
    adjustedFees: [
      {
        fee: 141,
        ppid: 'PP-885381',
      },
    ],
    emails: '-',
    energy: {
      claimedEnergyDelivered: 72.1,
      energyDelivered: 59.6,
      paidEnergyDelivered: 74.1,
    },
    fees: {
      gross: 31.1,
      net: 61.2,
      vat: 48.5,
    },
    groupUid: '725fead1-a43b-468b-a500-a279d8a47f95',
    numberOfCharges: 2,
    payoutStatus: 'WARNING',
    reference: 'PP0CH022023',
    revenue: {
      gross: 56.8,
      net: 82.9,
      vat: 24.3,
    },
    siteAddress: '28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK',
    workItem: {
      automated: false,
      groupName: 'Tesco',
      groupUid: 'fefd9c70-92b9-11ee-b9d1-0242ac120002',
      id: 'f7c7552c-92b9-11ee-b9d1-0242ac120002',
      month: '2023-02-13',
      siteId: '02973742-92ba-11ee-b9d1-0242ac120002',
      siteName: 'Banner Street',
      status: WorkItemStatus.SENT,
      userName: 'Bob Dylan',
      userId: '746daf89-abea-4640-9c67-0d89a5c57b64',
    },
  },
];
