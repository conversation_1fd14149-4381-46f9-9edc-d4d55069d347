import { AddGroupTable } from './add-group-table';
import { TEST_GROUP } from '@experience/commercial/site-admin/typescript/domain-model';
import { TEST_GROUP as TEST_GROUP_CONFIG } from '@experience/commercial/statement-service/shared';
import { render, screen, waitFor } from '@testing-library/react';

const mockAddGroup = jest.fn();

describe('AddGroupTable', () => {
  const defaultProps = {
    groups: [TEST_GROUP],
    existingGroupIds: [],
    handleAddGroup: mockAddGroup,
  };

  it('should render successfully', () => {
    const { baseElement } = render(<AddGroupTable {...defaultProps} />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<AddGroupTable {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should call add group function when the add button is clicked', async () => {
    render(<AddGroupTable {...defaultProps} />);
    screen.getByRole('button', { name: 'Add' }).click();
    await waitFor(() => {
      expect(mockAddGroup).toHaveBeenCalledWith(TEST_GROUP);
    });
  });

  it('should not show an add link for groups that are already present', () => {
    render(
      <AddGroupTable
        groups={[{ ...TEST_GROUP, uid: TEST_GROUP_CONFIG.groupId }]}
        existingGroupIds={[TEST_GROUP_CONFIG.groupId]}
        handleAddGroup={mockAddGroup}
      />
    );
    expect(screen.queryByText('Add')).not.toBeInTheDocument();
  });
});
