import {
  TEST_CHARGER,
  TEST_UPDATE_CHARGER_REQUEST,
} from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { revalidatePath } from 'next/cache';
import { updateChargerConfig } from './action';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

jest.mock('next/cache');
const mockRevalidatePath = jest.mocked(revalidatePath);

describe('Update charger config', () => {
  it('it should update a charger config', async () => {
    mockRequestHandler.mockResolvedValueOnce({ statusCode: 200 });

    const body = JSON.stringify(TEST_UPDATE_CHARGER_REQUEST);

    await updateChargerConfig(TEST_CHARGER.siteId, body);

    expect(appRequestHandler).toHaveBeenCalledWith(
      `http://localhost:5102/sites/${TEST_CHARGER.siteId}/chargers`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'PUT',
        body,
      }
    );
    expect(mockRevalidatePath).toHaveBeenCalledWith(
      '/groups/[groupId]/sites/[siteId]/chargers',
      'page'
    );
  });
});
