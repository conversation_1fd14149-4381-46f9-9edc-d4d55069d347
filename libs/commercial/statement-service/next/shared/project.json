{"name": "commercial-statement-service-next-shared", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/statement-service/next/shared/src", "projectType": "library", "tags": ["commercial", "statement-service"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commercial/statement-service/next/shared/jest.config.ts"}}}}