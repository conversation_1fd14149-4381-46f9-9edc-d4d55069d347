import {
  ButtonProps,
  ButtonTypes,
  MenuButton,
} from '@experience/shared/react/design-system';
import { Statement } from '@experience/commercial/statement-service/shared';
import { useRouter } from 'next/navigation';

interface ViewMenuProps extends Partial<ButtonProps> {
  statement: Statement;
}

export const ViewMenu = ({ statement, ...props }: ViewMenuProps) => {
  const router = useRouter();

  const handleViewInvoice = (statement: Statement): void => {
    const { invoice } = statement;
    const { stripeInvoiceId, id: invoiceId } = invoice ?? {};

    invoice?.stripeInvoiceId
      ? window.open(
          `https://dashboard.stripe.com/invoices/${stripeInvoiceId}`,
          '_blank'
        )
      : router.push(`/statements/invoices/${invoiceId}`);
  };

  return (
    <MenuButton
      buttonType={ButtonTypes.PRIMARY_OUTLINE}
      title="View..."
      {...props}
    >
      <MenuButton.Item
        onClick={() => router.push(`/statements/${statement.id}`)}
      >
        View statement
      </MenuButton.Item>
      <MenuButton.Item onClick={() => handleViewInvoice(statement)}>
        View fee invoice
      </MenuButton.Item>
      <MenuButton.Item
        onClick={() =>
          router.push(`/statements/${statement.id}/statement-fees`)
        }
      >
        View fees
      </MenuButton.Item>
    </MenuButton>
  );
};

export default ViewMenu;
