import { <PERSON>, <PERSON><PERSON>, Post, RawBodyRequest, Req } from '@nestjs/common';
import { WebhookService } from './webhook.service';

@Controller('stripe/webhooks')
export class WebhookController {
  constructor(private webhookService: WebhookService) {}

  // Note: This endpoint has a http_listener_rule on the ALB
  // @see https://github.com/Pod-Point/terraform/blob/51e7841d4b7b99b8c8758a749b6e3d3e02f0007c/modules/applications/experience/commercial/statement-service/alb.tf#L75
  @Post()
  async webhook(
    @Req() request: RawBodyRequest<Request>,
    @Headers('stripe-signature') stripeSignature: string
  ): Promise<void> {
    await this.webhookService.processEvent(request.rawBody, stripeSignature);
  }

  // Note: This endpoint has a http_listener_rule on the ALB
  // @see https://github.com/Pod-Point/terraform/blob/51e7841d4b7b99b8c8758a749b6e3d3e02f0007c/modules/applications/experience/commercial/statement-service/alb.tf#L75
  @Post('/connect')
  async connectWebhook(
    @Req() request: RawBodyRequest<Request>,
    @Headers('stripe-signature') stripeSignature: string
  ): Promise<void> {
    await this.webhookService.processConnectEvent(
      request.rawBody,
      stripeSignature
    );
  }
}
