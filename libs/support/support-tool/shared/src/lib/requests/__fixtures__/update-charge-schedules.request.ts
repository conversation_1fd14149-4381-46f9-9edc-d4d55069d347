import { UpdateChargeSchedulesRequest } from '../update-charge-schedules.request';

export const TEST_UPDATE_CHARGE_SCHEDULES_FORM_DATA = {
  ppid: 'PPID-12345',
  'Monday-active': 'on',
  'Monday-hours': '4',
  'Monday-minutes': '0',
  'Monday-startTime': '10:00:00',
  'Tuesday-active': 'on',
  'Tuesday-hours': '3',
  'Tuesday-minutes': '15',
  'Tuesday-startTime': '20:35:00',
  'Wednesday-active': 'on',
  'Wednesday-hours': '5',
  'Wednesday-minutes': '0',
  'Wednesday-startTime': '00:00:00',
  'Thursday-active': 'on',
  'Thursday-hours': '1',
  'Thursday-minutes': '0',
  'Thursday-startTime': '16:10:00',
  'Friday-active': 'on',
  'Friday-hours': '5',
  'Friday-minutes': '0',
  'Friday-startTime': '00:00:00',
  'Saturday-active': 'off',
  'Saturday-hours': '5',
  'Saturday-minutes': '0',
  'Saturday-startTime': '00:00:00',
  'Sunday-active': 'off',
  'Sunday-hours': '5',
  'Sunday-minutes': '0',
  'Sunday-startTime': '00:00:00',
};

export const TEST_UPDATE_CHARGE_SCHEDULES_REQUEST: UpdateChargeSchedulesRequest =
  {
    days: [
      {
        Monday: {
          active: true,
          hours: 4,
          minutes: 0,
          startTime: '10:00:00',
        },
      },
      {
        Tuesday: {
          active: true,
          hours: 3,
          minutes: 15,
          startTime: '20:35:00',
        },
      },
      {
        Wednesday: {
          active: true,
          hours: 5,
          minutes: 0,
          startTime: '00:00:00',
        },
      },
      {
        Thursday: {
          active: true,
          hours: 1,
          minutes: 0,
          startTime: '16:10:00',
        },
      },
      {
        Friday: {
          active: true,
          hours: 5,
          minutes: 0,
          startTime: '00:00:00',
        },
      },
      {
        Saturday: {
          active: false,
          hours: 5,
          minutes: 0,
          startTime: '00:00:00',
        },
      },
      {
        Sunday: {
          active: false,
          hours: 5,
          minutes: 0,
          startTime: '00:00:00',
        },
      },
    ],
  };
