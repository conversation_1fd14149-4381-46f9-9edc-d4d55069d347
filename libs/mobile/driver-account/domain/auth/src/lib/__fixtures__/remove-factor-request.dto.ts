import { CountryCode } from 'libphonenumber-js';
import { RemoveFactorRequest } from '../remove-factor-request.dto';

export const TEST_REMOVE_FACTOR_REQUEST: RemoveFactorRequest = {
  email: '<EMAIL>',
  phoneNumber: '07544056306',
  countryCode: 'GB' as CountryCode,
};

export const TEST_REMOVE_FACTOR_REQUEST_NOT_FOUND: RemoveFactorRequest = {
  email: '<EMAIL>',
  phoneNumber: '+4407544056305',
  countryCode: 'GB' as CountryCode,
};

export const TEST_REMOVE_FACTOR_REQUEST_NOT_VALID_PHONE: RemoveFactorRequest = {
  email: '<EMAIL>',
  phoneNumber: '+3307544056305',
  countryCode: 'GB' as CountryCode,
};
