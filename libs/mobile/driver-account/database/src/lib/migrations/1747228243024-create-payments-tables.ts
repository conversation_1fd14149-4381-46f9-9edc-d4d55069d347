import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreatePaymentsTables1747228243024 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Recipients

    await queryRunner.createTable(
      new Table({
        name: 'recipients',
        schema: 'payments',
        database: 'driver_account',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            default: 'uuid_generate_v4()',
            isPrimary: true,
            primaryKeyConstraintName: 'payments_recipients_pk',
          },
          {
            name: 'auth_id',
            type: 'varchar',
          },
          {
            name: 'stripe_account_id',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'deleted_at',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
      })
    );

    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON payments.recipients TO driver_account_api;

        CREATE INDEX payments_recipients_auth_id_idx ON payments.recipients USING btree (auth_id);
        CREATE INDEX payments_recipients_stripe_account_id_idx ON payments.recipients USING btree (stripe_account_id);
      `);

    // Bank Accounts

    await queryRunner.createTable(
      new Table({
        name: 'bank_accounts',
        schema: 'payments',
        database: 'driver_account',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            default: 'uuid_generate_v4()',
            isPrimary: true,
            primaryKeyConstraintName: 'payments_bank_accounts_pk',
          },
          {
            name: 'name',
            type: 'varchar',
          },
          {
            name: 'last4',
            type: 'varchar',
          },
          {
            name: 'recipient_id',
            type: 'uuid',
          },
          {
            name: 'stripe_bank_account_id',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'deleted_at',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            name: 'fk_recipients_bank_accounts',
            columnNames: ['recipient_id'],
            referencedTableName: 'recipients',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      })
    );

    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON payments.bank_accounts TO driver_account_api;

        CREATE INDEX payments_bank_accounts_recipient_id_idx ON payments.bank_accounts USING btree (recipient_id);
        CREATE INDEX payments_bank_accounts_stripe_bank_account_id_idx ON payments.bank_accounts USING btree (stripe_bank_account_id);
      `);

    // Transactions

    await queryRunner.createTable(
      new Table({
        schema: 'payments',
        database: 'driver_account',
        name: 'transactions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            default: 'uuid_generate_v4()',
            isPrimary: true,
            primaryKeyConstraintName: 'payments_transactions_pk',
          },
          {
            name: 'recipient_id',
            type: 'uuid',
          },
          {
            name: 'stripe_transaction_id',
            type: 'varchar',
          },
          {
            name: 'amount',
            type: 'DECIMAL(19,2)',
          },
          {
            name: 'currency',
            type: 'varchar',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'deleted_at',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            name: 'fk_recipients_transactions',
            columnNames: ['recipient_id'],
            referencedTableName: 'recipients',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      })
    );

    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON payments.transactions TO driver_account_api;

        CREATE INDEX payments_transactions_recipient_id_idx ON payments.transactions USING btree (recipient_id);
        CREATE INDEX payments_transactions_stripe_transaction_id_idx ON payments.transactions USING btree (stripe_transaction_id);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('payments.recipients', true, true, true);
    await queryRunner.dropIndex(
      'payments.recipients',
      'payments_recipients_auth_id_idx'
    );
    await queryRunner.dropIndex(
      'payments.recipients',
      'payments_recipients_stripe_account_id_idx'
    );
    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON payments.recipients FROM driver_account_api;
      `);

    await queryRunner.dropTable('payments.bank_accounts', true, true, true);
    await queryRunner.dropIndex(
      'payments.bank_accounts',
      'payments_bank_accounts_recipient_id_idx'
    );
    await queryRunner.dropIndex(
      'payments.bank_accounts',
      'payments_bank_accounts_stripe_bank_account_id_idx'
    );
    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON payments.bank_accounts FROM driver_account_api;
      `);

    await queryRunner.dropTable('payments.transactions', true, true, true);
    await queryRunner.dropIndex(
      'payments.transactions',
      'payments_transactions_recipient_id_idx'
    );
    await queryRunner.dropIndex(
      'payments.transactions',
      'payments_transactions_stripe_transaction_id_idx'
    );
    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON payments.transactions FROM driver_account_api;
      `);
  }
}
