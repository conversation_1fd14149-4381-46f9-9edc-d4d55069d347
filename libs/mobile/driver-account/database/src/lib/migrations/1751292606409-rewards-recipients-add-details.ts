import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class RewardsRecipientsAddDetails1751292606409
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'payments.recipients',
      new TableColumn({
        name: 'details',
        type: 'jsonb',
        default: "'{}'::jsonb",
        isNullable: false,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('payments.recipients', 'details');
  }
}
