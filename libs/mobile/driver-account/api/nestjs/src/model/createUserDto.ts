/**
 * Driver Account API
 * Driver account API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ConsentDto } from './consentDto';
import { PreferencesDto } from './preferencesDto';

export interface CreateUserDto {
  /**
   * First name
   */
  first_name: string;
  /**
   * Last name
   */
  last_name: string;
  /**
   * Locale
   */
  locale: string;
  /**
   * Email
   */
  email: string;
  /**
   * password
   */
  password?: string;
  consent?: ConsentDto;
  preferences?: PreferencesDto;
}
