/**
 * Driver Account API
 * Driver account API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface OAuthTokenRefreshTokenResponseDto {
  /**
   * The access token to be used as `${token_type} ${access_token}` in the Authorization header
   */
  access_token: string;
  /**
   * The type of token returned
   */
  token_type: string;
  /**
   * The number of seconds until the token expires (currently one hour)
   */
  expires_in: number;
}
