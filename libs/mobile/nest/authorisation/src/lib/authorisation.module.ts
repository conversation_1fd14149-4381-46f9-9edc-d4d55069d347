import {
  Api3TokenModule,
  Api3TokenService,
} from '@experience/mobile/nest/api3-token';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';
import { FlagsModule } from '@experience/shared/nest/remote-config';
import { Module } from '@nestjs/common';
import { TokenValidator } from './token.validator';
import { TokenValidatorWithRemoteConfig } from './token.validator.rc';
import { UserMFAEnabledGuard } from './guards/user-mfa-enabled.guard';

@Module({
  imports: [Api3TokenModule, ConfigModule, CacheModule.register(), FlagsModule],
  providers: [
    Api3TokenService,
    TokenValidator,
    TokenValidatorWithRemoteConfig,
    UserMFAEnabledGuard,
  ],
  exports: [
    Api3TokenService,
    TokenValidator,
    TokenValidatorWithRemoteConfig,
    UserMFAEnabledGuard,
  ],
})
export class AuthorisationModule {}
