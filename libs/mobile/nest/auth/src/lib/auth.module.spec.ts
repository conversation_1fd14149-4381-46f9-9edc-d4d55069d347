import axios from 'axios';

export const describeAuthModule = (baseUrl: string) => {
  describe('auth module', () => {
    describe('auth/email-verification controller', () => {
      it('should generate an email for activations', async () => {
        const response = await axios.post(
          `${baseUrl}/auth/email-verification`,
          { email: '<EMAIL>' }
        );

        expect(response.status).toEqual(202);
        expect(response.data).toEqual('');
      });

      it('should generate an email for activations and generate a continue url', async () => {
        const response = await axios.post(
          `${baseUrl}/auth/email-verification`,
          {
            email: '<EMAIL>',
            email_verification_continue_url: 'http://mydomain.com',
          }
        );
        expect(response.status).toEqual(202);
        expect(response.data).toEqual('');
      });
    });

    describe('auth/reset-email controller', () => {
      it('should generate an email for resetting a password', async () => {
        const response = await axios.post(`${baseUrl}/auth/password-reset`, {
          email: '<EMAIL>',
        });
        expect(response.status).toEqual(202);
        expect(response.data).toEqual('');
      });

      it('should generate an email for resetting a password and generate a continue url', async () => {
        const response = await axios.post(`${baseUrl}/auth/password-reset`, {
          email: '<EMAIL>',
          reset_password_continue_url: 'http://mydomain.com',
        });
        expect(response.status).toEqual(202);
        expect(response.data).toEqual('');
      });
    });

    describe('auth/verify-and-change-email controller', () => {
      it('should generate an email for verifying and changing an email', async () => {
        const response = await axios.post(
          `${baseUrl}/auth/verify-and-change-email`,
          {
            email: '<EMAIL>',
            newEmail: '<EMAIL>',
          }
        );
        expect(response.status).toEqual(202);
        expect(response.data).toEqual('');
      });
    });

    describe('delete auth/factor', () => {
      it('should remove a factor ', async () => {
        const response = await axios.delete(`${baseUrl}/auth/factor`, {
          data: {
            email: '<EMAIL>',
            phoneNumber: '07544056306',
            countryCode: 'GB',
          },
        });
        expect(response.status).toEqual(204);
        expect(response.data).toEqual('');
      });
    });
  });

  describe('post auth/password-reset-alert', () => {
    it('should return a 400 if a email is not supplied to a password reset alert', async () => {
      await expect(
        axios.post(`${baseUrl}/auth/password-reset-alert`)
      ).rejects.toThrow('Request failed with status code 400');
    });

    it('should return a 200 when sending a password reset alert', async () => {
      const response = await axios.post(
        `${baseUrl}/auth/password-reset-alert`,
        {
          email: '<EMAIL>',
        }
      );

      expect(response.status).toEqual(200);
    });
  });

  describe('post auth/sign-in-with-email', () => {
    it('should return a 400 if a email is not supplied to a sign in with email', async () => {
      await expect(
        axios.post(`${baseUrl}/auth/sign-in-with-email`, {
          continue_url: 'https://some-url.com',
        })
      ).rejects.toThrow('Request failed with status code 400');
    });

    it('should return a 400 if a continue url is not supplied to a sign in with email', async () => {
      await expect(
        axios.post(`${baseUrl}/auth/sign-in-with-email`, {
          email: '<EMAIL>',
        })
      ).rejects.toThrow('Request failed with status code 400');
    });

    it('should return a 202 when sending a password reset alert', async () => {
      const response = await axios.post(`${baseUrl}/auth/sign-in-with-email`, {
        email: '<EMAIL>',
        continue_url: 'https://some-url.com',
      });

      expect(response.status).toEqual(202);
    });
  });

  describe('get auth/telephone-codes', () => {
    it('should return the telephone codes', async () => {
      const response = await axios.get(`${baseUrl}/auth/telephone-codes`);

      expect(response.status).toEqual(200);
    });
  });
};
