import { ConfigService } from '@nestjs/config';
import { EmailTheme } from '../constants';
import { EventBridgeService } from '@experience/shared/nest/aws/eventbridge-module';
import { I18nService } from 'nestjs-i18n';
import { Injectable, Logger } from '@nestjs/common';
import {
  SimpleEmailService,
  getTemplateStrings,
} from '@experience/shared/nest/aws/ses-module';
import {
  TrackLoginRequest,
  UserInfoResponseDto,
} from '@experience/mobile/driver-account/domain/user';
import { UAParser } from 'ua-parser-js';
import {
  UserSignedInEvent,
  buildUserSignedInEvent,
} from '@experience/mobile/events';
import { getEmailThemeBaseUrl, getEmailThemeSenderString } from '../auth.utils';
import { getLanguageFromCode } from '@experience/shared/nest/utils';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';

interface EmailLoginParameters {
  theme: EmailTheme;
  emailAddress: string;
  subject?: string;
  firstName?: string;
  ipAddress?: string;
  timestamp?: string;
  device?: string;
}

@Injectable()
export class TrackLoginService {
  private logger = new Logger(TrackLoginService.name);

  constructor(
    private readonly config: ConfigService,
    private readonly simpleEmailService: SimpleEmailService,
    private readonly i18n: I18nService,
    private readonly eventBridgeService: EventBridgeService
  ) {}

  async sendEmail(
    user: UserInfoResponseDto,
    trackLoginRequest: TrackLoginRequest,
    theme: EmailTheme
  ) {
    const uaParser = new UAParser(trackLoginRequest.userAgent);
    const { vendor, model } = uaParser.getDevice();
    const deviceInfo = vendor
      ? `${vendor} ${model}`
      : uaParser.getBrowser().name
      ? `${uaParser.getBrowser().name}`
      : 'Unknown';

    this.logger.log(
      { authId: user.uid, theme: theme },
      `User ${user.uid} logged in with ${deviceInfo} from ${trackLoginRequest.ipAddress}`
    );

    await this.sendTrackLoginEmail(
      {
        emailAddress: user.email,
        firstName: user.first_name,
        ipAddress: trackLoginRequest.ipAddress,
        device: deviceInfo,
        timestamp: trackLoginRequest.timestamp,
        theme: theme,
      },
      user.locale
    );
    await this.submitUserSignedInEvent(user, trackLoginRequest);
  }

  private async sendTrackLoginEmail(
    params: EmailLoginParameters,
    language: string
  ): Promise<void> {
    const locale = getLanguageFromCode(language);

    const { emailAddress } = params;
    const { htmlTemplateString, plainTextTemplateString } = getTemplateStrings(
      `./assets/mobile-account/email-templates/${locale}/${params.theme}/track-login`
    );
    const identityUrl = getEmailThemeBaseUrl(params.theme);
    const subject = this.i18n.t('auth-emails.login.subject', { lang: locale });

    params.subject = subject;

    await this.simpleEmailService.sendEmail({
      bodyHtml: injectParametersIntoTemplateString(htmlTemplateString, {
        ...params,
        imageUrl: identityUrl,
      }),
      bodyText: injectParametersIntoTemplateString(plainTextTemplateString, {
        ...params,
      }),
      subject,
      to: emailAddress,
      attachment: [],
      cc: [],
      bcc: [],
      sender: getEmailThemeSenderString(params.theme),
    });
  }

  private async submitUserSignedInEvent(
    user: UserInfoResponseDto,
    trackLoginRequest: TrackLoginRequest
  ) {
    try {
      const { userAgent, ipAddress } = trackLoginRequest;
      await this.eventBridgeService.putEvent<UserSignedInEvent>({
        eventBus: this.config.get<string>('EXPERIENCE_EVENT_BUS_ARN', ''),
        source: 'driver-account-api',
        detailType: 'User.SignedIn',
        detail: buildUserSignedInEvent(
          'Driver',
          '/experience/mobile/driver-account-api',
          {
            user: {
              id: user.uid,
              email: user.email,
              firstName: user.first_name,
              lastName: user.last_name,
            },
            context: {
              ipAddress: ipAddress,
              userAgent: userAgent,
            },
          }
        ),
      });
    } catch (error) {
      this.logger.error(
        { authId: user.uid, error },
        'failed to submit user deleted event'
      );
    }
  }
}
