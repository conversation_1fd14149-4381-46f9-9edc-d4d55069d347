import { COUNTRY_CODE_DATA } from './telephone-codes.const';
import { ConfigService } from '@nestjs/config';
import { ConfigurationNotFound } from './telephone-codes.error';
import { CountryCodeResponse } from './telephone-codes.types';
import { I18nService } from 'nestjs-i18n';
import { Injectable, Logger } from '@nestjs/common';
import { getAuth } from '@experience/shared/firebase/admin';
import { getLanguageFromCode } from '@experience/shared/nest/utils';

const PRIORITY_COUNTRY_CODES = ['GB', 'IE', 'FR', 'ES', 'NO'];

@Injectable()
export class TelephoneCodesService {
  private readonly logger = new Logger(TelephoneCodesService.name);
  constructor(
    private readonly config: ConfigService,
    private readonly i18n: I18nService
  ) {}

  async getTelephoneCodes(language = 'en'): Promise<CountryCodeResponse[]> {
    this.logger.log({ language }, 'retrieving telephone codes');
    const lang = getLanguageFromCode(language);
    const config = await getAuth().projectConfigManager().getProjectConfig();
    const allowedRegions =
      config?.smsRegionConfig?.allowlistOnly?.allowedRegions;

    if (!allowedRegions || allowedRegions.length === 0) {
      this.logger.error('There are no allowed regions in firebase');
      throw new ConfigurationNotFound();
    }

    this.logger.log({ allowedRegions }, 'retrieved supported regions');

    const priorityCountries = this.buildCountryCodeArray(
      PRIORITY_COUNTRY_CODES,
      lang
    ).filter((country) => allowedRegions.includes(country.code));

    const supportedCountries = this.buildCountryCodeArray(
      allowedRegions,
      lang
    ).sort((a, b) => a.name.localeCompare(b.name));

    return [...priorityCountries, ...supportedCountries];
  }

  private buildCountryCodeArray(
    codes: string[],
    lang: string
  ): CountryCodeResponse[] {
    const result: CountryCodeResponse[] = [];

    codes.forEach((code) => {
      const country = COUNTRY_CODE_DATA[code];

      if (!country) {
        this.logger.warn(
          { country },
          'country code not found in country telephone data source'
        );

        return;
      }

      country.name = this.i18n.t(`telephone-codes.${country.code}`, {
        lang,
      });

      result.push({ ...country, enabled: true });
    });

    return result;
  }
}
