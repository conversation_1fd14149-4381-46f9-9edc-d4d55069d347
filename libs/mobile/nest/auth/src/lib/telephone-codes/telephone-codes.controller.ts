import {
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Controller, Get, Headers, UseInterceptors } from '@nestjs/common';
import { CountryCodeResponse } from './telephone-codes.types';
import { TelephoneCodesInterceptor } from './telephone-codes.interceptor';
import { TelephoneCodesService } from './telephone-codes.service';

@ApiTags('Auth')
@Controller({
  path: 'auth',
  version: '1',
})
@UseInterceptors(TelephoneCodesInterceptor)
export class TelephoneCodesController {
  constructor(private telephoneCodesService: TelephoneCodesService) {}

  @Get('telephone-codes')
  @ApiOperation({
    summary: 'retrieve available telephone codes',
    description:
      'Retrieves available telephone codes specified by the Firebase configuration',
  })
  @ApiOkResponse({
    description: 'Successfully retrieved country codes',
  })
  @ApiNotFoundResponse({
    description: 'Firebase configuration could not be found',
  })
  async getTelephoneCodes(
    @Headers('Accept-Language') language = 'en'
  ): Promise<CountryCodeResponse[]> {
    return this.telephoneCodesService.getTelephoneCodes(language);
  }
}
