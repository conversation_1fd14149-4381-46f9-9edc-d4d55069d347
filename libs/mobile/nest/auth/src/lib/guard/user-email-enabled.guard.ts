import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';

import { getAuth } from '@experience/shared/firebase/admin';

@Injectable()
export class UserEmailEnabledGuard implements CanActivate {
  private logger = new Logger('HTTP');

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    try {
      const authUser = await getAuth().getUserByEmail(request.body.email);

      if (authUser.disabled) {
        this.logger.error(`User with email ${request.body.email} is disabled`);

        throw new NotFoundException(
          `User with email ${request.body.email} is disabled`
        );
      }

      return true;
    } catch (error) {
      this.logger.error(`User with email ${request.body.email} not found`);

      throw new NotFoundException(
        `User with email ${request.body.email} not found`
      );
    }
  }
}
