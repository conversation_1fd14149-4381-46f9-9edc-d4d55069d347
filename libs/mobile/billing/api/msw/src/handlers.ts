/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [await getHealthControllerCheck200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/payments/setup-intent/:uid`, async () => {
    const resultArray = [
      [await getPaymentControllerSetupIntent201Response(), { status: 201 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/payments/create-payment-intent`, async () => {
    const resultArray = [
      [
        await getPaymentControllerCreateGuestPaymentIntent201Response(),
        { status: 201 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/payments/create-payment-intent/:uid`, async () => {
    const resultArray = [
      [
        await getPaymentControllerCreateRegisteredUserPaymentIntent201Response(),
        { status: 201 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/webhook`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/version`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.patch(`${baseURL}/customer/:authId`, async () => {
    const resultArray = [
      [await getCustomerControllerUpdateCustomer200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}

export function getPaymentControllerSetupIntent201Response() {
  return {
    customer: faker.lorem.words(),
    setupIntent: faker.lorem.words(),
    ephemeralKey: faker.lorem.words(),
  };
}

export function getPaymentControllerCreateGuestPaymentIntent201Response() {
  return {
    paymentIntent: faker.lorem.words(),
  };
}

export function getPaymentControllerCreateRegisteredUserPaymentIntent201Response() {
  return {
    paymentIntent: faker.lorem.words(),
    customer: faker.lorem.words(),
    ephemeralKey: faker.lorem.words(),
  };
}

export function getCustomerControllerUpdateCustomer200Response() {
  return {
    email: faker.internet.email(),
  };
}
