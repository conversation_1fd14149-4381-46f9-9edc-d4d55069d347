# openapi.api.ExampleApi

## Load the API package

```dart
import 'package:openapi/api.dart';
```

All URIs are relative to _http://localhost_

| Method                                                             | HTTP request | Description              |
| ------------------------------------------------------------------ | ------------ | ------------------------ |
| [**helloControllerGetData**](ExampleApi.md#hellocontrollergetdata) | **GET** /    | welcome message endpoint |

# **helloControllerGetData**

> helloControllerGetData()

welcome message endpoint

### Example

```dart
import 'package:openapi/api.dart';

final api_instance = ExampleApi();

try {
    api_instance.helloControllerGetData();
} catch (e) {
    print('Exception when calling ExampleApi->helloControllerGetData: $e\n');
}
```

### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
