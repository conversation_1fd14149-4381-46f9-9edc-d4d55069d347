# openapi.model.UpdatePcbSwapDto

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name          | Type       | Description                                                                          | Notes |
| ------------- | ---------- | ------------------------------------------------------------------------------------ | ----- |
| **emailedAt** | **String** | If present, the time at which the charger's owner was notified of the completed swap |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
