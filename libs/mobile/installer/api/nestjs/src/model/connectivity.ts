/**
 * Installer API
 * Installer Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface Connectivity {
  wifiConnectedState: string;
  signalStrength: number;
  networkState: Connectivity.NetworkStateEnum;
  networkInterface?: Connectivity.NetworkInterfaceEnum;
}
export namespace Connectivity {
  export type NetworkStateEnum = 'disconnected' | 'internet' | 'pod point';
  export const NetworkStateEnum = {
    Disconnected: 'disconnected' as NetworkStateEnum,
    Internet: 'internet' as NetworkStateEnum,
    PodPoint: 'pod point' as NetworkStateEnum,
  };
  export type NetworkInterfaceEnum = 'disconnected' | 'wifi' | 'ethernet';
  export const NetworkInterfaceEnum = {
    Disconnected: 'disconnected' as NetworkInterfaceEnum,
    Wifi: 'wifi' as NetworkInterfaceEnum,
    Ethernet: 'ethernet' as NetworkInterfaceEnum,
  };
}
