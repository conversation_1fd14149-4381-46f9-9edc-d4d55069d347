module.exports = {
  content: ['./**/*.html'],
  theme: {
    screens: {
      xs: { max: '425px' },
      sm: { max: '600px' },
    },
    extend: {
      colors: {
        cool: {
          50: '#f4f6fa',
          100: '#e6e9f3',
          200: '#d2d8eb',
          300: '#b3bedd',
          400: '#8e9dcc',
          500: '#7381be',
          600: '#6169af',
          700: '#555aa0',
          800: '#4a4b83',
          900: '#36385b',
          950: '#292a42',
        },
        error: {
          50: '#fef2f2',
          100: '#fde3e3',
          200: '#fdcccb',
          300: '#faa8a7',
          400: '#f57674',
          500: '#eb4d4b',
          600: '#d72d2b',
          700: '#b52220',
          800: '#96201e',
          900: '#7d201f',
          950: '#440c0b',
        },
        info: {
          50: '#f2fbfa',
          100: '#d2f5f0',
          200: '#a5eae2',
          300: '#70d8cf',
          400: '#44bfb8',
          500: '#29a39e',
          600: '#1e8381',
          700: '#1c6968',
          800: '#1b5454',
          900: '#1b4646',
          950: '#0a2729',
        },
        neutral: {
          50: '#f2f2f2',
          100: '#e3e3e4',
          200: '#c6c7c9',
          300: '#a2a3a6',
          400: '#7f7f82',
          500: '#646568',
          600: '#4f4f52',
          700: '#414144',
          800: '#373738',
          900: '#303031',
          950: '#19191a',
        },
        primary: {
          50: '#f5faeb',
          100: '#e9f3d4',
          200: '#d3e8ae',
          300: '#b6d87e',
          400: '#8fc043',
          500: '#7bab37',
          600: '#5f8828',
          700: '#496823',
          800: '#3c5420',
          900: '#34481f',
          950: '#1a270c',
        },
        steel: {
          50: '#f5f7f9',
          100: '#e9ebf0',
          200: '#d8dde5',
          300: '#bdc5d3',
          400: '#9ba7bb',
          500: '#8691ab',
          600: '#747d9c',
          700: '#686e8d',
          800: '#585d75',
          900: '#494d5f',
          950: '#2f313c',
        },
        warning: {
          50: '#fefce8',
          100: '#fffac2',
          200: '#fff288',
          300: '#ffe343',
          400: '#ffce10',
          500: '#efb403',
          600: '#d18d00',
          700: '#a46304',
          800: '#874d0c',
          900: '#733f10',
          950: '#432005',
        },
      },
      spacing: {
        screen: '100vw',
        full: '100%',
        0: '0',
        0.5: '2px',
        1: '4px',
        1.5: '6px',
        2: '8px',
        2.5: '10px',
        3: '12px',
        3.5: '14px',
        4: '16px',
        4.5: '18px',
        5: '20px',
        5.5: '22px',
        6: '24px',
        6.5: '26px',
        7: '28px',
        7.5: '30px',
        8: '32px',
        8.5: '34px',
        9: '36px',
        9.5: '38px',
        10: '40px',
        11: '44px',
        12: '48px',
        14: '56px',
        16: '64px',
        20: '80px',
        24: '96px',
        28: '112px',
        32: '128px',
        36: '144px',
        40: '160px',
        44: '176px',
        48: '192px',
        52: '208px',
        56: '224px',
        60: '240px',
        64: '256px',
        72: '288px',
        80: '320px',
        96: '384px',
        97.5: '390px',
        120: '480px',
        150: '600px',
        160: '640px',
        175: '700px',
        '1/2': '50%',
        '1/3': '33.333333%',
        '2/3': '66.666667%',
        '1/4': '25%',
        '2/4': '50%',
        '3/4': '75%',
        '1/5': '20%',
        '2/5': '40%',
        '3/5': '60%',
        '4/5': '80%',
        '1/6': '16.666667%',
        '2/6': '33.333333%',
        '3/6': '50%',
        '4/6': '66.666667%',
        '5/6': '83.333333%',
        '1/12': '8.333333%',
        '2/12': '16.666667%',
        '3/12': '25%',
        '4/12': '33.333333%',
        '5/12': '41.666667%',
        '6/12': '50%',
        '7/12': '58.333333%',
        '8/12': '66.666667%',
        '9/12': '75%',
        '10/12': '83.333333%',
        '11/12': '91.666667%',
      },
      borderRadius: {
        none: '0px',
        sm: '2px',
        DEFAULT: '4px',
        md: '6px',
        lg: '8px',
        xl: '12px',
        '2xl': '16px',
        '3xl': '24px',
      },
      boxShadow: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        DEFAULT:
          '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)',
      },
      fontFamily: {
        sans: [
          'Quicksand',
          'ui-sans-serif',
          'system-ui',
          '-apple-system',
          '"Segoe UI"',
          'sans-serif',
        ],
        serif: [
          'ui-serif',
          'Georgia',
          'Cambria',
          '"Times New Roman"',
          'Times',
          'serif',
        ],
        mono: ['ui-monospace', 'Menlo', 'Consolas', 'monospace'],
      },
      fontSize: {
        0: '0',
        xxs: '11px',
        xs: '12px',
        '2xs': '13px',
        sm: '14px',
        '2sm': '15px',
        base: '16px',
        lg: '18px',
        xl: '20px',
        '2xl': '24px',
        '3xl': '30px',
        '4xl': '36px',
        '5xl': '48px',
        '6xl': '60px',
        '7xl': '72px',
        '8xl': '96px',
        '9xl': '128px',
      },
      letterSpacing: (theme) => ({
        ...theme('spacing'),
      }),
      lineHeight: (theme) => ({
        ...theme('spacing'),
      }),
      maxWidth: (theme) => ({
        ...theme('spacing'),
        xs: '160px',
        sm: '192px',
        md: '224px',
        lg: '256px',
        xl: '288px',
        '2xl': '336px',
        '3xl': '384px',
        '4xl': '448px',
        '5xl': '512px',
        '6xl': '576px',
        '7xl': '640px',
      }),
      minHeight: (theme) => ({
        ...theme('spacing'),
      }),
      minWidth: (theme) => ({
        ...theme('spacing'),
      }),
    },
  },
  corePlugins: {
    preflight: false,
    backgroundOpacity: false,
    borderOpacity: false,
    boxShadow: false,
    divideOpacity: false,
    placeholderOpacity: false,
    textOpacity: false,
  },
  plugins: [
    import('@tailwindcss/forms'),
    import('tailwindcss-box-shadow'),
    import('tailwindcss-email-variants'),
    import('tailwindcss-mso'),
  ],
};
