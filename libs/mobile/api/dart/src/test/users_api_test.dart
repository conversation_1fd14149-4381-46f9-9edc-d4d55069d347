//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';


/// tests for UsersApi
void main() {
  // final instance = UsersApi();

  group('tests for UsersApi', () {
    // Gets the current user's info
    //
    // Get the user info for the authenticated user
    //
    //Future<ExtendedUserInfoResponseDto> usersControllerGetUser() async
    test('test usersControllerGetUser', () async {
      // TODO
    });

    // Generates a link for connecting to Enode
    //
    // Generates and returns a link for connecting a user's account to Enode
    //
    //Future<GetLinkSessionResponse> usersControllerLinkEnode(GetLinkSessionRequest getLinkSessionRequest) async
    test('test usersControllerLinkEnode', () async {
      // TODO
    });

    // Generates an email of when and from which ip the user logged in
    //
    // Generates an email of when and from which ip the user logged in
    //
    //Future usersControllerTrackLogin(TrackLoginRequest trackLoginRequest) async
    test('test usersControllerTrackLogin', () async {
      // TODO
    });

  });
}
