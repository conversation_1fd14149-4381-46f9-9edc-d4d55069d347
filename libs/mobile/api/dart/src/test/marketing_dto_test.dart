//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for MarketingDto
void main() {
  // final instance = MarketingDto();

  group('test MarketingDto', () {
    // num isConsentGiven
    test('to test the property `isConsentGiven`', () async {
      // TODO
    });

    // String type
    test('to test the property `type`', () async {
      // TODO
    });

    // String copy
    test('to test the property `copy`', () async {
      // TODO
    });

    // String origin
    test('to test the property `origin`', () async {
      // TODO
    });


  });

}
