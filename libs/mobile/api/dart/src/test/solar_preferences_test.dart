//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for SolarPreferences
void main() {
  // final instance = SolarPreferences();

  group('test SolarPreferences', () {
    // String powerGeneration
    test('to test the property `powerGeneration`', () async {
      // TODO
    });

    // String solarMatching
    test('to test the property `solarMatching`', () async {
      // TODO
    });

    // Deprecated: please use solarMaxGridImport instead
    // num solarThreshold
    test('to test the property `solarThreshold`', () async {
      // TODO
    });

    // The maximum amount of power in kWh to import from the grid. Replaces solarThreshold which has been deprecated
    // num solarMaxGridImport
    test('to test the property `solarMaxGridImport`', () async {
      // TODO
    });


  });

}
