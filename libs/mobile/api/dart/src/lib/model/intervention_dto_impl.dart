//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class InterventionDtoImpl {
  /// Returns a new [InterventionDtoImpl] instance.
  InterventionDtoImpl({
    required this.all,
    this.chargeState = const [],
    this.information = const [],
  });

  /// The endpoint to extract all interventions
  String all;

  /// The individual interventions for charge state
  List<String> chargeState;

  /// The individual interventions for vehicle information
  List<String> information;

  @override
  bool operator ==(Object other) => identical(this, other) || other is InterventionDtoImpl &&
    other.all == all &&
    _deepEquality.equals(other.chargeState, chargeState) &&
    _deepEquality.equals(other.information, information);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (all.hashCode) +
    (chargeState.hashCode) +
    (information.hashCode);

  @override
  String toString() => 'InterventionDtoImpl[all=$all, chargeState=$chargeState, information=$information]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'all'] = this.all;
      json[r'chargeState'] = this.chargeState;
      json[r'information'] = this.information;
    return json;
  }

  /// Returns a new [InterventionDtoImpl] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static InterventionDtoImpl? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "InterventionDtoImpl[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "InterventionDtoImpl[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return InterventionDtoImpl(
        all: mapValueOfType<String>(json, r'all')!,
        chargeState: json[r'chargeState'] is Iterable
            ? (json[r'chargeState'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        information: json[r'information'] is Iterable
            ? (json[r'information'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<InterventionDtoImpl> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <InterventionDtoImpl>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = InterventionDtoImpl.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, InterventionDtoImpl> mapFromJson(dynamic json) {
    final map = <String, InterventionDtoImpl>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = InterventionDtoImpl.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of InterventionDtoImpl-objects as value to a dart map
  static Map<String, List<InterventionDtoImpl>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<InterventionDtoImpl>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = InterventionDtoImpl.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'all',
    'chargeState',
    'information',
  };
}

