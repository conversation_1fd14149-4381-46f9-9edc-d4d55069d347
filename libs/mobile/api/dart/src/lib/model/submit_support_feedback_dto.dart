//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SubmitSupportFeedbackDTO {
  /// Returns a new [SubmitSupportFeedbackDTO] instance.
  SubmitSupportFeedbackDTO({
    required this.region,
    required this.email,
    required this.description,
    this.chargerName,
    this.siteName,
    this.siteAddress,
  });

  /// The region the support case is relevant to
  SubmitSupportFeedbackDTORegionEnum region;

  /// The email of the user submitting the feedback
  String email;

  /// The free-text entered by the user
  String description;

  /// The name of the charger, if relevant
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? chargerName;

  /// The name of the site at which the charger is located, if relevant
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? siteName;

  /// The address of the site at which the charger is located, if relevant
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? siteAddress;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SubmitSupportFeedbackDTO &&
    other.region == region &&
    other.email == email &&
    other.description == description &&
    other.chargerName == chargerName &&
    other.siteName == siteName &&
    other.siteAddress == siteAddress;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (region.hashCode) +
    (email.hashCode) +
    (description.hashCode) +
    (chargerName == null ? 0 : chargerName!.hashCode) +
    (siteName == null ? 0 : siteName!.hashCode) +
    (siteAddress == null ? 0 : siteAddress!.hashCode);

  @override
  String toString() => 'SubmitSupportFeedbackDTO[region=$region, email=$email, description=$description, chargerName=$chargerName, siteName=$siteName, siteAddress=$siteAddress]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'region'] = this.region;
      json[r'email'] = this.email;
      json[r'description'] = this.description;
    if (this.chargerName != null) {
      json[r'chargerName'] = this.chargerName;
    } else {
      json[r'chargerName'] = null;
    }
    if (this.siteName != null) {
      json[r'siteName'] = this.siteName;
    } else {
      json[r'siteName'] = null;
    }
    if (this.siteAddress != null) {
      json[r'siteAddress'] = this.siteAddress;
    } else {
      json[r'siteAddress'] = null;
    }
    return json;
  }

  /// Returns a new [SubmitSupportFeedbackDTO] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SubmitSupportFeedbackDTO? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SubmitSupportFeedbackDTO[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SubmitSupportFeedbackDTO[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SubmitSupportFeedbackDTO(
        region: SubmitSupportFeedbackDTORegionEnum.fromJson(json[r'region'])!,
        email: mapValueOfType<String>(json, r'email')!,
        description: mapValueOfType<String>(json, r'description')!,
        chargerName: mapValueOfType<String>(json, r'chargerName'),
        siteName: mapValueOfType<String>(json, r'siteName'),
        siteAddress: mapValueOfType<String>(json, r'siteAddress'),
      );
    }
    return null;
  }

  static List<SubmitSupportFeedbackDTO> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SubmitSupportFeedbackDTO>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SubmitSupportFeedbackDTO.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SubmitSupportFeedbackDTO> mapFromJson(dynamic json) {
    final map = <String, SubmitSupportFeedbackDTO>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SubmitSupportFeedbackDTO.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SubmitSupportFeedbackDTO-objects as value to a dart map
  static Map<String, List<SubmitSupportFeedbackDTO>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SubmitSupportFeedbackDTO>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SubmitSupportFeedbackDTO.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'region',
    'email',
    'description',
  };
}

/// The region the support case is relevant to
class SubmitSupportFeedbackDTORegionEnum {
  /// Instantiate a new enum with the provided [value].
  const SubmitSupportFeedbackDTORegionEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const es = SubmitSupportFeedbackDTORegionEnum._(r'es');
  static const fr = SubmitSupportFeedbackDTORegionEnum._(r'fr');

  /// List of all possible values in this [enum][SubmitSupportFeedbackDTORegionEnum].
  static const values = <SubmitSupportFeedbackDTORegionEnum>[
    es,
    fr,
  ];

  static SubmitSupportFeedbackDTORegionEnum? fromJson(dynamic value) => SubmitSupportFeedbackDTORegionEnumTypeTransformer().decode(value);

  static List<SubmitSupportFeedbackDTORegionEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SubmitSupportFeedbackDTORegionEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SubmitSupportFeedbackDTORegionEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [SubmitSupportFeedbackDTORegionEnum] to String,
/// and [decode] dynamic data back to [SubmitSupportFeedbackDTORegionEnum].
class SubmitSupportFeedbackDTORegionEnumTypeTransformer {
  factory SubmitSupportFeedbackDTORegionEnumTypeTransformer() => _instance ??= const SubmitSupportFeedbackDTORegionEnumTypeTransformer._();

  const SubmitSupportFeedbackDTORegionEnumTypeTransformer._();

  String encode(SubmitSupportFeedbackDTORegionEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a SubmitSupportFeedbackDTORegionEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  SubmitSupportFeedbackDTORegionEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'es': return SubmitSupportFeedbackDTORegionEnum.es;
        case r'fr': return SubmitSupportFeedbackDTORegionEnum.fr;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [SubmitSupportFeedbackDTORegionEnumTypeTransformer] instance.
  static SubmitSupportFeedbackDTORegionEnumTypeTransformer? _instance;
}


