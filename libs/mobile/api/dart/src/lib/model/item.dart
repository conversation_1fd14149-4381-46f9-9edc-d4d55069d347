//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class Item {
  /// Returns a new [Item] instance.
  Item({
    required this.id,
    required this.description,
    required this.type,
    required this.text,
    required this.highlight,
  });

  /// This can be an address Id or a container Id for further results.
  String id;

  /// Descriptive information about the result.
  String description;

  /// If the Type is \"Address\" then the Id can be passed to the Retrieve service. Any other Id should be passed as the Container to a further Find request to get more results.
  ItemTypeEnum type;

  /// The name of the result.
  String text;

  /// A list of number ranges identifying the matched characters in the Text and Description.
  String highlight;

  @override
  bool operator ==(Object other) => identical(this, other) || other is Item &&
    other.id == id &&
    other.description == description &&
    other.type == type &&
    other.text == text &&
    other.highlight == highlight;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id.hashCode) +
    (description.hashCode) +
    (type.hashCode) +
    (text.hashCode) +
    (highlight.hashCode);

  @override
  String toString() => 'Item[id=$id, description=$description, type=$type, text=$text, highlight=$highlight]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'Id'] = this.id;
      json[r'Description'] = this.description;
      json[r'Type'] = this.type;
      json[r'Text'] = this.text;
      json[r'Highlight'] = this.highlight;
    return json;
  }

  /// Returns a new [Item] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static Item? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "Item[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "Item[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return Item(
        id: mapValueOfType<String>(json, r'Id')!,
        description: mapValueOfType<String>(json, r'Description')!,
        type: ItemTypeEnum.fromJson(json[r'Type'])!,
        text: mapValueOfType<String>(json, r'Text')!,
        highlight: mapValueOfType<String>(json, r'Highlight')!,
      );
    }
    return null;
  }

  static List<Item> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <Item>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = Item.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, Item> mapFromJson(dynamic json) {
    final map = <String, Item>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = Item.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of Item-objects as value to a dart map
  static Map<String, List<Item>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<Item>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = Item.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'Id',
    'Description',
    'Type',
    'Text',
    'Highlight',
  };
}

/// If the Type is \"Address\" then the Id can be passed to the Retrieve service. Any other Id should be passed as the Container to a further Find request to get more results.
class ItemTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const ItemTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const address = ItemTypeEnum._(r'Address');
  static const container = ItemTypeEnum._(r'Container');
  static const postcode = ItemTypeEnum._(r'Postcode');
  static const residential = ItemTypeEnum._(r'Residential');
  static const street = ItemTypeEnum._(r'Street');

  /// List of all possible values in this [enum][ItemTypeEnum].
  static const values = <ItemTypeEnum>[
    address,
    container,
    postcode,
    residential,
    street,
  ];

  static ItemTypeEnum? fromJson(dynamic value) => ItemTypeEnumTypeTransformer().decode(value);

  static List<ItemTypeEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ItemTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ItemTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ItemTypeEnum] to String,
/// and [decode] dynamic data back to [ItemTypeEnum].
class ItemTypeEnumTypeTransformer {
  factory ItemTypeEnumTypeTransformer() => _instance ??= const ItemTypeEnumTypeTransformer._();

  const ItemTypeEnumTypeTransformer._();

  String encode(ItemTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ItemTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ItemTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'Address': return ItemTypeEnum.address;
        case r'Container': return ItemTypeEnum.container;
        case r'Postcode': return ItemTypeEnum.postcode;
        case r'Residential': return ItemTypeEnum.residential;
        case r'Street': return ItemTypeEnum.street;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ItemTypeEnumTypeTransformer] instance.
  static ItemTypeEnumTypeTransformer? _instance;
}


