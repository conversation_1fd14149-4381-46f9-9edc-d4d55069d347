# openapi.api.API3Api

## Load the API package

```dart
import 'package:openapi/api.dart';
```

All URIs are relative to _http://localhost_

| Method                                                                              | HTTP request                               | Description              |
| ----------------------------------------------------------------------------------- | ------------------------------------------ | ------------------------ |
| [**api3ControllerClaimCharge**](API3Api.md#api3controllerclaimcharge)               | **POST** /api3/v5/charges                  | claim a charge           |
| [**api3ControllerGetCurrentFirmware**](API3Api.md#api3controllergetcurrentfirmware) | **GET** /api3/v5/units/{unitId}/firmware   | request firmware version |
| [**api3ControllerGetLocales**](API3Api.md#api3controllergetlocales)                 | **GET** /api3/v5/locales                   | get locales              |
| [**api3ControllerGetUser**](API3Api.md#api3controllergetuser)                       | **GET** /api3/v5/auth                      | retrieve user info       |
| [**api3ControllerStoreTariff**](API3Api.md#api3controllerstoretariff)               | **POST** /api3/v5/tariffs                  | store tariff             |
| [**api3ControllerTopUpAccount**](API3Api.md#api3controllertopupaccount)             | **POST** /api3/v5/users/{id}/account/topup | top up an account        |

# **api3ControllerClaimCharge**

> ChargeRequestResponse api3ControllerClaimCharge(chargeRequestDTO)

claim a charge

Claims a given charge

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = API3Api();
final chargeRequestDTO = ChargeRequestDTO(); // ChargeRequestDTO |

try {
    final result = api_instance.api3ControllerClaimCharge(chargeRequestDTO);
    print(result);
} catch (e) {
    print('Exception when calling API3Api->api3ControllerClaimCharge: $e\n');
}
```

### Parameters

| Name                 | Type                                        | Description | Notes |
| -------------------- | ------------------------------------------- | ----------- | ----- |
| **chargeRequestDTO** | [**ChargeRequestDTO**](ChargeRequestDTO.md) |             |

### Return type

[**ChargeRequestResponse**](ChargeRequestResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **api3ControllerGetCurrentFirmware**

> FirmwareStatusResponse api3ControllerGetCurrentFirmware(unitId)

request firmware version

Request firmware version

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = API3Api();
final unitId = 8.14; // num |

try {
    final result = api_instance.api3ControllerGetCurrentFirmware(unitId);
    print(result);
} catch (e) {
    print('Exception when calling API3Api->api3ControllerGetCurrentFirmware: $e\n');
}
```

### Parameters

| Name       | Type    | Description | Notes |
| ---------- | ------- | ----------- | ----- |
| **unitId** | **num** |             |

### Return type

[**FirmwareStatusResponse**](FirmwareStatusResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **api3ControllerGetLocales**

> LocaleResponse api3ControllerGetLocales(acceptLanguage)

get locales

Request to get all the locales

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = API3Api();
final acceptLanguage = acceptLanguage_example; // String | Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)

try {
    final result = api_instance.api3ControllerGetLocales(acceptLanguage);
    print(result);
} catch (e) {
    print('Exception when calling API3Api->api3ControllerGetLocales: $e\n');
}
```

### Parameters

| Name               | Type       | Description                                                                                                  | Notes                        |
| ------------------ | ---------- | ------------------------------------------------------------------------------------------------------------ | ---------------------------- |
| **acceptLanguage** | **String** | Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4) | [optional] [default to 'en'] |

### Return type

[**LocaleResponse**](LocaleResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **api3ControllerGetUser**

> UserResponse api3ControllerGetUser(include)

retrieve user info

Retrieve user info from API3 based on the JWT token

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = API3Api();
final include = include_example; // String | Query param for auth

try {
    final result = api_instance.api3ControllerGetUser(include);
    print(result);
} catch (e) {
    print('Exception when calling API3Api->api3ControllerGetUser: $e\n');
}
```

### Parameters

| Name        | Type       | Description          | Notes      |
| ----------- | ---------- | -------------------- | ---------- |
| **include** | **String** | Query param for auth | [optional] |

### Return type

[**UserResponse**](UserResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **api3ControllerStoreTariff**

> TariffResponse api3ControllerStoreTariff(tariffRequest)

store tariff

Request to store a new tariff or update an existing one

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = API3Api();
final tariffRequest = TariffRequest(); // TariffRequest |

try {
    final result = api_instance.api3ControllerStoreTariff(tariffRequest);
    print(result);
} catch (e) {
    print('Exception when calling API3Api->api3ControllerStoreTariff: $e\n');
}
```

### Parameters

| Name              | Type                                  | Description | Notes |
| ----------------- | ------------------------------------- | ----------- | ----- |
| **tariffRequest** | [**TariffRequest**](TariffRequest.md) |             |

### Return type

[**TariffResponse**](TariffResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **api3ControllerTopUpAccount**

> api3ControllerTopUpAccount(id, accountTopUpRequestDTO)

top up an account

Tops up the user's account

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = API3Api();
final id = id_example; // String |
final accountTopUpRequestDTO = AccountTopUpRequestDTO(); // AccountTopUpRequestDTO |

try {
    api_instance.api3ControllerTopUpAccount(id, accountTopUpRequestDTO);
} catch (e) {
    print('Exception when calling API3Api->api3ControllerTopUpAccount: $e\n');
}
```

### Parameters

| Name                       | Type                                                    | Description | Notes |
| -------------------------- | ------------------------------------------------------- | ----------- | ----- |
| **id**                     | **String**                                              |             |
| **accountTopUpRequestDTO** | [**AccountTopUpRequestDTO**](AccountTopUpRequestDTO.md) |             |

### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
