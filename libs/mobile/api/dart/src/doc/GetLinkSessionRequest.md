# openapi.model.GetLinkSessionRequest

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name            | Type       | Description                                                                                                                                                                                                                 | Notes      |
| --------------- | ---------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------- |
| **vendor**      | **String** | By specifying a vendor, the brand selection step in Link UI will be skipped. Instead, your user will go directly to the service selection view (if applicable for the specified vendor), or to the review data access step. | [optional] |
| **enodeUserId** | **String** | A unique identifier identifying the user to be used with the link session generated at the capture vehicle stage                                                                                                            | [optional] |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
