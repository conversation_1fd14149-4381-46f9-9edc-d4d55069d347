# openapi.model.PayoutResponseDTOImpl

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name              | Type       | Description                                                      | Notes |
| ----------------- | ---------- | ---------------------------------------------------------------- | ----- |
| **value**         | **num**    | The balance that was paid out in the lowest unit of the currency |
| **currency**      | **String** | The currency to payout in                                        |
| **totalMiles**    | **num**    | The total number of miles claimed                                |
| **transactionId** | **String** | The ID of the transaction                                        |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
