# openapi.api.VehiclesApi

## Load the API package

```dart
import 'package:openapi/api.dart';
```

All URIs are relative to _http://localhost_

| Method                                                                                            | HTTP request                                                 | Description                               |
| ------------------------------------------------------------------------------------------------- | ------------------------------------------------------------ | ----------------------------------------- |
| [**vehiclesControllerGetAllInterventions**](VehiclesApi.md#vehiclescontrollergetallinterventions) | **GET** /vehicles/{vehicleId}/interventions                  | get all interventions for a given vehicle |
| [**vehiclesControllerGetIntervention**](VehiclesApi.md#vehiclescontrollergetintervention)         | **GET** /vehicles/{vehicleId}/interventions/{interventionId} | get a specific intervention               |

# **vehiclesControllerGetAllInterventions**

> VehicleInterventionResponseDtoImpl vehiclesControllerGetAllInterventions(vehicleId, acceptLanguage)

get all interventions for a given vehicle

For a given vehicle, get all interventions

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VehiclesApi();
final vehicleId = 3fa85f64-5717-4562-b3fc-2c963f66afa6; // String | ID of the Vehicle
final acceptLanguage = acceptLanguage_example; // String | Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)

try {
    final result = api_instance.vehiclesControllerGetAllInterventions(vehicleId, acceptLanguage);
    print(result);
} catch (e) {
    print('Exception when calling VehiclesApi->vehiclesControllerGetAllInterventions: $e\n');
}
```

### Parameters

| Name               | Type       | Description                                                                                                  | Notes                        |
| ------------------ | ---------- | ------------------------------------------------------------------------------------------------------------ | ---------------------------- |
| **vehicleId**      | **String** | ID of the Vehicle                                                                                            |
| **acceptLanguage** | **String** | Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4) | [optional] [default to 'en'] |

### Return type

[**VehicleInterventionResponseDtoImpl**](VehicleInterventionResponseDtoImpl.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **vehiclesControllerGetIntervention**

> VehicleInterventionDtoImpl vehiclesControllerGetIntervention(vehicleId, interventionId, acceptLanguage)

get a specific intervention

For a given intervention, get its information

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = VehiclesApi();
final vehicleId = 3fa85f64-5717-4562-b3fc-2c963f66afa6; // String | vehicleId of the vehicle
final interventionId = 3fa85f64-5717-4562-b3fc-2c963f66afa7; // String | ID of the intervention
final acceptLanguage = acceptLanguage_example; // String | Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)

try {
    final result = api_instance.vehiclesControllerGetIntervention(vehicleId, interventionId, acceptLanguage);
    print(result);
} catch (e) {
    print('Exception when calling VehiclesApi->vehiclesControllerGetIntervention: $e\n');
}
```

### Parameters

| Name               | Type       | Description                                                                                                  | Notes                        |
| ------------------ | ---------- | ------------------------------------------------------------------------------------------------------------ | ---------------------------- |
| **vehicleId**      | **String** | vehicleId of the vehicle                                                                                     |
| **interventionId** | **String** | ID of the intervention                                                                                       |
| **acceptLanguage** | **String** | Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4) | [optional] [default to 'en'] |

### Return type

[**VehicleInterventionDtoImpl**](VehicleInterventionDtoImpl.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
