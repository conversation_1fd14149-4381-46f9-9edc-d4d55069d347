# openapi.model.ExpensesResponse

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name         | Type                          | Description | Notes                 |
| ------------ | ----------------------------- | ----------- | --------------------- |
| **expenses** | [**List<Object>**](Object.md) |             | [default to const []] |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
