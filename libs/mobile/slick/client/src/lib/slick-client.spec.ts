import * as z from 'zod';
import { ApplicationCreate, ApplicationUpdate } from './types';
import { Readable } from 'stream';
import { SlickClient } from './slick-client';
import { TestAffordabilityApplicationId, slickHandlers } from './fixtures';
import { createServer } from '@mswjs/http-middleware';

const slickClientConfig = {
  baseUrl: 'http://localhost:9888',
  apiToken: 'test-api-token',
  userAgent: 'SlickClient',
  maxRetry: 3,
};

describe('Slick Client', () => {
  let server: ReturnType<ReturnType<typeof createServer>['listen']>;

  beforeAll(() => {
    server = createServer(...slickHandlers).listen(9888);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  afterAll(() => {
    server.close();
  });

  /**
   * POST /v2/application/create
   */
  describe('POST Create endpoint', () => {
    it('can create an application', async () => {
      const createData: ApplicationCreate = {
        marital_status: 'married',
      };

      const client = new SlickClient(slickClientConfig);
      const response = await client.createApplication(createData);

      expect(response).toHaveProperty('code', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response['response']).toStrictEqual({
        id: 1,
        reference: '1234567890',
        uuid: '1234567890',
      });
    });

    it('throws if creating an application fails', async () => {
      const client = new SlickClient(slickClientConfig);

      await expect(async () => {
        await client.createApplication({
          marital_status: 'married',
          monthly_income: '40000000',
        });
      }).rejects.toThrow('Application update failed');
    });
  });
  /**
   * POST /v2/application/update
   */
  describe('POST Update endpoint', () => {
    it('can update an application', async () => {
      const updateData: ApplicationUpdate = {
        id: TestAffordabilityApplicationId.APPROVE,
        marital_status: 'married',
      };

      const client = new SlickClient(slickClientConfig);
      const response = await client.updateApplication(updateData);

      expect(response).toHaveProperty('code', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response['response']).toBeNull();
    });

    it('throws if updating an application fails', async () => {
      const client = new SlickClient(slickClientConfig);

      await expect(async () => {
        await client.updateApplication({
          id: TestAffordabilityApplicationId.REJECT,
          marital_status: 'married',
        });
      }).rejects.toThrow('Application update failed');
    });
  });

  /**
   * GET /v2/application/signing-link?id=&type=
   */
  describe('GET Signing Link endpoint', () => {
    it('can retrieve a signing link', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.getSigningLink(
        TestAffordabilityApplicationId.APPROVE,
        'rca'
      );

      expect(response).toHaveProperty('status', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response).toHaveProperty(
        'response',
        'https://sell.your-soul.here'
      );
    });

    it('throws if type is not valid', async () => {
      const client = new SlickClient(slickClientConfig);
      await expect(
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        client.getSigningLink(TestAffordabilityApplicationId.APPROVE, 'bla')
      ).rejects.toThrow(z.ZodError);
    });

    it('throws if retrieving a signing link fails', async () => {
      const client = new SlickClient(slickClientConfig);

      await expect(async () => {
        await client.getSigningLink(
          TestAffordabilityApplicationId.REJECT,
          'rca'
        );
      }).rejects.toThrow('Signing link retrieval failed');
    });
  });

  /**
   * GET /v2/application/decision?id=&force&engine
   */
  describe('GET Decision', () => {
    it('can retrieve an application decision', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.getApplicationDecision(12345, 'engine');

      expect(response).toHaveProperty('code', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response.response).toHaveProperty('decision', 'accept');
      expect(response.response).toHaveProperty('open_banking_url', null);
    });

    it('throws an error if retrieving an application decision fails', async () => {
      const client = new SlickClient(slickClientConfig);

      await expect(async () => {
        await client.getApplicationDecision(0, 'engine');
      }).rejects.toThrow('Invalid application ID');
    });
  });

  /**
   * POST /v2/application/release
   */
  describe('POST Release', () => {
    it('can successfully release an application', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.releaseApplication(2);

      expect(response).toHaveProperty('status', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response).toHaveProperty('response', null);
    });

    it('throws an error if releasing an application fails', async () => {
      const client = new SlickClient(slickClientConfig);

      await expect(async () => {
        await client.releaseApplication(0);
      }).rejects.toThrow('Invalid application ID');
    });
  });

  /**
   * GET /v2/application/get?id=
   */
  describe('GET Application', () => {
    it('should support null direct debit details', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.getApplication(404);

      expect(response).toHaveProperty('code', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response).toHaveProperty('response', {
        id: expect.any(Number),
        uuid: expect.any(String),
        reference: expect.any(String),
        created_at: expect.any(Number),
        updated_at: expect.any(Number),
        loan: expect.any(Number),
        account_holder_name: null,
        account_number: null,
        sortcode: null,
        payment_frequency_fixed_day: null,
      });
    });

    it('should get an application by id', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.getApplication(123);

      expect(response).toHaveProperty('code', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response).toHaveProperty('response', {
        id: expect.any(Number),
        uuid: expect.any(String),
        reference: expect.any(String),
        created_at: expect.any(Number),
        updated_at: expect.any(Number),
        loan: expect.any(Number),
        account_holder_name: expect.any(String),
        account_number: expect.any(String),
        sortcode: expect.any(String),
        payment_frequency_fixed_day: expect.any(Number),
      });
    });
  });

  /**
   * PUT /v2/application/meta-data
   */
  describe('PUT Application Meta Data', () => {
    it('should update given application metadata', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.updateMetadata(1, 'slug', 'value');

      expect(response).toStrictEqual({
        code: 200,
        message: 'Success',
        response: {
          slug: expect.any(String),
          value: expect.any(String),
        },
      });
    });
  });

  /**
   * GET /v2/application/documents
   */
  describe('GET Application Documents', () => {
    it('should get documents for a given application', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.getDocuments(1);

      expect(response).toHaveProperty('code', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response).toHaveProperty('response', [
        expect.objectContaining({
          sign_by_link: expect.objectContaining({
            status: 'signed',
            tag: 'ha',
          }),
          type: 'sign_by_link',
        }),
        expect.objectContaining({
          type: 'income_documents',
          sign_by_link: [],
        }),
      ]);
    });
  });

  /**
   * GET /v2/application/document
   */
  describe('GET Application Document', () => {
    it('should get document for a given document', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.getDocument(1);

      expect(response).toHaveProperty('code', 200);
      expect(response).toHaveProperty('message', 'Success');
      expect(response).toHaveProperty('response', {
        token: 'A0838D19-ED4H-1HD0-6F21-976J0ABCC627',
        expiry_time: 1749206780,
      });
    });
  });

  /**
   * GET /v2/document/download
   */
  describe('GET Document Document', () => {
    it('should get download for a given document', async () => {
      const client = new SlickClient(slickClientConfig);
      const response = await client.downloadDocumentByToken(
        'AH0D39AC-ANA5-A4FN-AGDA-22D951F705HB'
      );

      expect(response).toBeInstanceOf(Readable);
    });
  });

  /**
   * makeRequest() Retry mechanism
   */
  describe('Retry mechanism', () => {
    it('will retry on request failures up to maxRetry', async () => {
      const client = new SlickClient(slickClientConfig);

      await expect(async () => {
        await client.getSigningLink(
          TestAffordabilityApplicationId.RATED,
          'rca'
        );
      }).rejects.toThrow('Temporary error');
    });
  });
});
