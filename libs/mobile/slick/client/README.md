# **Slick Client (Nx Shared Library)**

The **Slick Client** is a **shared library** within an **Nx NestJS monorepo**, designed to interact with the **Slick Solutions API**. It provides a structured way to handle **loan applications, updates, decisions, signing links, and loan releases** with built-in **Zod validation** and **retry logic**.

---

## **📌 Features**

- ✅ **Reusable API Client** for Slick Solutions
- ✅ **Zod Validation** for typed API responses
- ✅ **Automatic Retry Logic** for network failures
- ✅ **Unit-tested** with Jest
- ✅ **Optimized for Nx Monorepos**

---

## **📂 Library Structure**

```
libs/
 ├── mobile/
 │    ├── slick/
 │    │    ├── client/
 │    │    │    ├── src/
 │    │    │    │    ├── lib/
 │    │    │    │    │    ├── slick-client.ts       # API Client
 │    │    │    │    │    ├── slick-client.spec.ts  # API Client
 │    │    │    │    │    ├── types.ts              # Zod Schema & Types
 │    │    │    │    ├── index.ts                   # Public API Export
 │    │    │    ├── README.md                       # This File
 │    │    │    ├── jest.config.ts                  # Jest Config
 │    │    │    ├── tsconfig.json                   # TypeScript Config
```

The **Slick Client** is located in `libs/mobile/slick/client` and can be **imported anywhere in your NestJS services**.

---

## **🚀 Usage in NestJS Services**

### **1️⃣ Inject into a Nest.js Module**

```ts
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SlickClient } from '@yourorg/mobile/slick/client';
import { LoanService } from './loan.service';
import { LoanController } from './loan.controller';

@Module({
  controllers: [LoanController],
  providers: [
    LoanService,
    {
      inject: [ConfigService],
      provide: SlickClient,
      useFactory: async (configService: ConfigService) =>
        new SlickClient({
          baseUrl: configService.getOrThrow('SLICK_API_BASE_URL') as string,
          apiToken: configService.getOrThrow('SLICK_API_TOKEN') as string,
          userAgent: 'SlickClient',
          maxRetry: configService.get<number>('SLICK_API_MAX_RETRY') ?? 3,
        }),
    },
  ],
  exports: [LoanService, SlickClient], // 👈 Export if needed in other modules
})
export class LoanModule {}
```

---

### **2️⃣ Inject into a Controller**

```ts
import { Controller, Post, Body } from '@nestjs/common';
import { LoanService } from './loan.service';

@Controller('loans')
export class LoanController {
  constructor(private readonly loanService: LoanService) {}

  @Post('apply')
  async apply(@Body() applicationData) {
    return this.loanService.submitApplication(applicationData);
  }
}
```

---

### **1️⃣ Import & Inject the Client in a Service**

```ts
import { Injectable } from '@nestjs/common';
import { SlickClient } from '@yourorg/mobile/slick/client';

@Injectable()
export class LoanService {
  private client = new SlickClient({
    baseUrl: 'https://api.slick.com',
    apiToken: process.env.SLICK_API_TOKEN!,
    userAgent: 'SlickClient',
    maxRetry: 3,
  });

  async submitApplication(applicationData) {
    return this.client.submitAllInOneApplication(applicationData);
  }
}
```

---

## **🛠 Development in the Nx Monorepo**

### **Building the Library**

To build the shared library, run:

```sh
nx build client
```

### **Running Unit Tests**

Run Jest tests for this library:

```sh
nx test client
```

### **Linting**

Ensure the code follows Nx linting rules:

```sh
nx lint client
```

---

## **📝 Available API Methods**

### **POST** - Create an Application

📌 **`createApplication(data: ApplicationCreate): Promise<ApplicationCreateResponse>`**

- Creates a new application.

### **POST** - Update an Application

📌 **`updateApplication(data: ApplicationUpdate): Promise<ApplicationUpdateResponse>`**

- Updates an existing application.

### **GET** - Retrieve a Signing Link

📌 **`getSigningLink(applicationId: number, type: string): Promise<ApplicationSigningLinkResponse>`**

- Fetches a signing link for an application.

### **GET** - Retrieve Application Decision

📌 **`getApplicationDecision(applicationId: number, engine: string, force: boolean): Promise<ApplicationDecisionResponse>`**

- Fetches the latest application decision from the specified engine (use force to require a new decision).

### **POST** - Release an Application

📌 **`releaseApplication(applicationId: number): Promise<ApplicationReleaseResponse>`**

- Releases the loan after installation.

---
