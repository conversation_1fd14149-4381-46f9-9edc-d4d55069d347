package seeding

import (
	"context"
	"database/sql"
	"errors"
	testSqlc "experience/libs/data-platform/test/sqlc"
	"fmt"
	"time"
)

func AwaitChargeReplication(ctx context.Context, chargeID int64, testQueries *testSqlc.Queries) error {
	timeout := 1 * time.Minute
	interval := 2 * time.Second
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		charge, err := testQueries.RetrieveCharge(ctx, chargeID)

		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				fmt.Println("awaiting charge replication.. charge not found, retrying...")
			} else {
				fmt.Printf("error retrieving charge with ID %d: %v\n", chargeID, err)
				return err
			}
		} else {
			fmt.Printf("charge found: %v\n", charge.ID)
			return nil
		}
		time.Sleep(interval)
	}
	return errors.New("timeout reached.. charge not found")
}

func AwaitBillingAccountReplication(ctx context.Context, billingAccountID int64, testQueries *testSqlc.Queries) error {
	timeout := 1 * time.Minute
	interval := 2 * time.Second
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		billingAccount, err := testQueries.RetrieveBillingAccount(ctx, billingAccountID)

		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				fmt.Println("awaiting billing account replication.. billing account not found, retrying...")
			} else {
				fmt.Printf("error retrieving billing account with ID %d: %v\n", billingAccountID, err)
				return err
			}
		} else {
			fmt.Printf("billing account found: %v\n", billingAccount.ID)
			return nil
		}
		time.Sleep(interval)
	}
	return errors.New("timeout reached.. billing account not found")
}
