package setup

import (
	"database/sql"
	cfg "experience/libs/shared/go/db"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/db/postgres/test"
	"testing"

	"github.com/jmoiron/sqlx"

	"github.com/stretchr/testify/require"
)

type PostgresTestDB struct {
	TestDB       *test.Database
	DBHandle     *sql.DB
	DBHandleSqlx *sqlx.DB
	ReadWriteDB  *postgres.ReadWriteDB
}

func NewPostgresTestDB(t *testing.T, migrationsPath string, opts ...test.Option) PostgresTestDB {
	t.Helper()

	testDB := test.NewDatabase(t, opts...)
	passwordConfig := testDB.PasswordConfig(t)
	dbh, err := postgres.NewPasswordDB(passwordConfig, true)
	require.NoError(t, err)

	_, err = migrate.MigrateUp(migrationsPath, testDB.PasswordConfig(t), nil, true)
	require.NoError(t, err)

	config := testDB.IamConfig(t)
	rwDB := postgres.NewReadWriteDB(&cfg.ServiceDatasource{
		ReadConfig:  cfg.ReadConfig{IAMConfig: config},
		WriteConfig: cfg.WriteConfig{IAMConfig: config},
	}, true)

	return PostgresTestDB{
		TestDB:       testDB,
		DBHandle:     dbh,
		DBHandleSqlx: sqlx.NewDb(dbh, "postgres"),
		ReadWriteDB:  rwDB,
	}
}
