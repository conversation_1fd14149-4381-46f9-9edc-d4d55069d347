package charges_test

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	charge "experience/libs/data-platform/event-sourcing/domain/events/charges"
	mockauthorisers "experience/libs/data-platform/event-sourcing/domain/events/charges/authorisers/mock"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	mockchargers "experience/libs/data-platform/event-sourcing/domain/events/charges/chargers/mock"
	mockcharges "experience/libs/data-platform/event-sourcing/domain/events/charges/mock"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/sites"
	mocksites "experience/libs/data-platform/event-sourcing/domain/events/charges/sites/mock"
	eventstore "experience/libs/shared/go/event-store"
	mockstore "experience/libs/shared/go/event-store/test/mock"
	"experience/libs/shared/go/service/utils"
	"experience/libs/shared/go/sqs"
	mockSqs "experience/libs/shared/go/sqs/test/mock"
	"fmt"
	"log"
	"regexp"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"
)

const claimedChargeCompletedBody = `
{
	"event": "Charge.Completed",
	"aggregateID": "570b2ccf-824d-4076-9da5-000000bbdef8",
	"payload": {
		"charge":{"id":12312312},
		"chargingStation": {"id": "PSL-532880", "doorId": "A"},
		"location": {"id": 12345},
		"authorisation":{"authoriserId": 246, "type": "user"}
	}
}`

const unclaimedChargeCompletedBody = `
{
	"event": "Charge.Completed",
	"aggregateID": "570b2ccf-824d-4076-9da5-000000bbdef8",
	"payload": {
		"charge":{"id":12312312},
		"chargingStation": {"id": "PSL-532880", "doorId": "A"},
		"location": {"id": 12345},
		"authorisation":{"authoriserId": null, "type": null}
	}
}`

func TestClaimChargeCommand_HappyPaths(t *testing.T) {
	tests := []struct {
		name            string
		mockLoadRecords []eventstore.Record
		charger         *chargers.Charger
		site            *sites.Site
		userUUIDs       []uuid.UUID
		expectedPayload charge.ClaimedPayload
	}{
		{
			name:            "charge, with authoriser",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(claimedChargeCompletedBody)}},
			userUUIDs:       []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
			expectedPayload: charge.ClaimedPayload{
				UserIDs:     []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
				ChargerID:   "PSL-532880",
				Door:        "A",
				ChargerType: "home",
			},
		},
		{
			name:            "charge, with home charger, no authoriser, multiple linked users",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(unclaimedChargeCompletedBody)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       1,
				IsPublic:     0,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("9aedd2a1-44ba-4eb8-9a20-5e2f132ca575"), uuid.MustParse("402aca51-9e1c-4652-83d1-97857c474ba8"), uuid.MustParse("163bcd5c-763b-4251-835d-f530f8a2f5b2")},
			expectedPayload: charge.ClaimedPayload{
				UserIDs:     []uuid.UUID{uuid.MustParse("9aedd2a1-44ba-4eb8-9a20-5e2f132ca575"), uuid.MustParse("402aca51-9e1c-4652-83d1-97857c474ba8"), uuid.MustParse("163bcd5c-763b-4251-835d-f530f8a2f5b2")},
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "home",
			},
		},
		{
			name:            "charge, with home charger, no authoriser, one linked users",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(unclaimedChargeCompletedBody)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       1,
				IsPublic:     0,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("402aca51-9e1c-4652-83d1-97857c474ba8")},
			expectedPayload: charge.ClaimedPayload{
				UserIDs:     []uuid.UUID{uuid.MustParse("402aca51-9e1c-4652-83d1-97857c474ba8")},
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "home",
			},
		},
		{
			name:            "charge, with private charger, no authoriser",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(unclaimedChargeCompletedBody)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       0,
				IsPublic:     0,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("524dd3e5-6a87-4ba1-8843-ee8979d0b655")},
			expectedPayload: charge.ClaimedPayload{
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "private",
			},
		},
		{
			name:            "charge, with private charger, with authoriser",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(claimedChargeCompletedBody)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       0,
				IsPublic:     0,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
			expectedPayload: charge.ClaimedPayload{
				UserIDs:     []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "private",
			},
		},
		{
			name:            "charge with multiple completed events, with authoriser on the second event",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(unclaimedChargeCompletedBody)}, {Version: 1, Data: []byte(claimedChargeCompletedBody)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       0,
				IsPublic:     0,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
			expectedPayload: charge.ClaimedPayload{
				UserIDs:     []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "private",
			},
		},
		{
			name:            "charge with multiple completed events, with no authoriser on the second event - doesn't find user",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(claimedChargeCompletedBody)}, {Version: 1, Data: []byte(`{"event": "Charge.Completed", "aggregateID": "570b2ccf-824d-4076-9da5-000000bbdef8", "payload": {"charge":{"id":12312312}, "chargingStation": {"id": "PSL-532880", "doorId": "A"}, "location": {"id": 12345}, "authorisation":{"authoriserId": null, "type": null}}}`)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       0,
				IsPublic:     0,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
			expectedPayload: charge.ClaimedPayload{
				UserIDs:     nil,
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "private",
			},
		},
		{
			name:            "charge with no authoriser id, but an authoriser type - doesn't find user",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(`{"event": "Charge.Completed", "aggregateID": "570b2ccf-824d-4076-9da5-000000bbdef8", "payload": {"charge":{"id":12312312}, "chargingStation": {"id": "PSL-532880", "doorId": "A"}, "location": {"id": 12345}, "authorisation":{"authoriserId": null, "type": "user"}}}`)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       0,
				IsPublic:     0,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
			expectedPayload: charge.ClaimedPayload{
				UserIDs:     nil,
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "private",
			},
		},
		{
			name:            "charge, with public charger, with rfid authoriser",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(`{"event": "Charge.Completed", "aggregateID": "77e13781-720a-4b92-97c3-095d203d96a7", "payload": {"chargingStation": {"id": "PSL-532880", "doorId": "A"}, "location": {"id": 12345}, "charge":{"id":2456789}, "authorisation":{"authoriserId": 746323, "type": "rfid"}}}`)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       0,
				IsPublic:     1,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
			expectedPayload: charge.ClaimedPayload{
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "public",
			},
		},
		{
			name:            "charge, with private charger, with site, with authoriser",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(claimedChargeCompletedBody)}},
			charger: &chargers.Charger{
				ChargerName:  ptr.To("Test-Unit"),
				BusinessName: ptr.To("Good Business"),
				IsHome:       0,
				IsPublic:     0,
			},
			site: &sites.Site{
				GroupID:   ptr.To(uuid.MustParse("0b06d103-33a9-4910-a209-ca4a66a56f72")),
				GroupName: ptr.To("Big Farmer"),
				ID:        123456789,
			},
			userUUIDs: []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
			expectedPayload: charge.ClaimedPayload{
				UserIDs:     []uuid.UUID{uuid.MustParse("92b9b024-e6fd-4a16-a2aa-2a5b914a6774")},
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Test-Unit"),
				Door:        "A",
				ChargerType: "private",
				GroupID:     ptr.To(uuid.MustParse("0b06d103-33a9-4910-a209-ca4a66a56f72")),
				GroupName:   ptr.To("Big Farmer"),
				SiteID:      ptr.To(utils.FabricateUUIDFromNumericID(123456789)),
				SiteName:    ptr.To("Good Business"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			serviceMocks := newClaimChargeServiceMocks(ctrl)

			chargeID := uuid.MustParse("2168222c-59d2-4d2d-935e-131c92bcd030")
			chargerID := chargers.StationID("PSL-532880")
			locationID := 12345
			authPK := 246

			serviceMocks.store.EXPECT().
				Load(gomock.AssignableToTypeOf(context.Background()), chargeID).
				Return(tt.mockLoadRecords, nil)

			serviceMocks.chargersRepo.EXPECT().
				GetLinkedChargerByID(context.Background(), chargerID).
				Return(tt.charger, nil).
				AnyTimes()

			serviceMocks.sitesRepo.EXPECT().
				GetByID(context.Background(), locationID).
				Return(tt.site, nil).
				AnyTimes()

			serviceMocks.authRepo.EXPECT().
				GetUserUUIDFromAuthPK(gomock.AssignableToTypeOf(context.Background()), authPK).
				Return(ptr.To(tt.userUUIDs[0].String()), nil).
				AnyTimes()

			serviceMocks.chargersRepo.EXPECT().
				GetLinkedUsersByID(context.Background(), chargerID).
				Return(tt.userUUIDs, nil).
				AnyTimes()

			payload, _ := json.Marshal(tt.expectedPayload)
			expectedMessageBodyRegex := charge.GenerateExpectedMessageBodyRegex(chargeID, charge.TypeClaimed, string(payload))

			serviceMocks.msgHandler.EXPECT().
				Process(context.Background(), gomock.Any()).
				DoAndReturn(func(_ context.Context, msg sqs.Message) error {
					regexMatch, err := regexp.MatchString(expectedMessageBodyRegex, *msg.Body)
					require.NoError(t, err)
					require.True(t, regexMatch)
					return nil
				}).
				Times(1)

			serviceMocks.sqsClient.EXPECT().
				SendMessage(gomock.AssignableToTypeOf(context.Background()), gomock.Any()).
				Times(0)

			aggregateLoader := charge.NewAggregateLoader(serviceMocks.store)

			service := charge.NewClaimChargeService(log.Default(), serviceMocks.sqsClient, aggregateLoader, serviceMocks.chargersRepo, serviceMocks.sitesRepo, serviceMocks.authRepo, serviceMocks.msgHandler)

			err := service.ClaimCharge(context.Background(), chargeID)
			require.NoError(t, err)
		})
	}
}

func TestClaimChargeCommand_UnhappyPaths(t *testing.T) {
	site := &sites.Site{
		GroupID:   ptr.To(uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162376")),
		GroupName: ptr.To("group name"),
		ID:        1,
	}
	charger := &chargers.Charger{
		ChargerName:  ptr.To("Charger-Name"),
		IsHome:       0,
		IsPublic:     0,
		BusinessName: ptr.To("Car Park X"),
	}
	homeCharger := &chargers.Charger{
		ChargerName: ptr.To("Charger-Home"),
		IsHome:      1,
		IsPublic:    0,
	}

	tests := []struct {
		name                  string
		mockAuthoriserUUID    *string
		linkedUserIDs         []uuid.UUID
		chargerID             chargers.StationID
		mockLoadRecords       []eventstore.Record
		mockErrors            mockErrors
		expectedError         error
		expectMsgNotProcessed bool
		expectSqsMessageSend  bool
		site                  *sites.Site
		charger               *chargers.Charger
		siteID                int
		energyCost            *int32
		expectedLogMessage    string
	}{
		{
			name:               "claimed charge event sends to sqs after failing to call sync handlers",
			mockAuthoriserUUID: ptr.To(uuid.NewString()),
			chargerID:          "PSL-532880",
			siteID:             12345,
			mockLoadRecords:    []eventstore.Record{{Version: 0, Data: []byte(claimedChargeCompletedBody)}},
			mockErrors: mockErrors{
				HandlerMockError: errSyncEventHandlerFailed,
			},
			site:                 site,
			charger:              charger,
			expectSqsMessageSend: true,
		},
		{
			name:          "authoriser type null, home charger with linked users, one of the user uuids was invalid - event sent still",
			linkedUserIDs: []uuid.UUID{uuid.New()},
			mockErrors: mockErrors{
				LinkedUsersByIDMockError: errors.New("an error affecting only one of the user uuids"),
			},
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(`{"event": "Charge.Completed", "aggregateID": "77e13781-720a-4b92-97c3-095d203d96a7", "payload": {"charge":{"id":2456789}, "authorisation":{"authoriserId": null, "type": null}}}`)}},
			site:            site,
			charger:         charger,
		},
		{
			name: "authoriser type null, home charger but linked user lookup failed - event not sent",
			mockErrors: mockErrors{
				LinkedUsersByIDMockError: errors.Join(errors.New("an error affecting the user uuids")),
			},
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(`{"event": "Charge.Completed", "aggregateID": "77e13781-720a-4b92-97c3-095d203d96a7", "payload": {"charge":{"id":2456789}, "authorisation":{"authoriserId": null, "type": null}}}`)}},
			site:            site,
			charger:         homeCharger,
			expectedError:   errors.Join(errors.New("an error affecting the user uuids")),
		},
		{
			name:            "load site error passed back to caller",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(`{"event": "Charge.Completed", "aggregateID": "74e13781-720a-4b92-97c3-095d203d96a7", "payload": {"charge":{"id":2456789}, "location": {"id": 12345}, "authorisation":{"authoriserId": null, "type": "null"}}}`)}},
			mockErrors: mockErrors{
				SitesMockError: errLoadSiteFailed,
			},
			expectedError: errLoadSiteFailed,
			site:          site,
			charger:       charger,
			siteID:        12345,
		},
		{
			name:               "load charge error passed back to caller",
			mockAuthoriserUUID: ptr.To(uuid.NewString()),
			mockErrors: mockErrors{
				LoadMockError: errLoadChargeFailed,
			},
			expectedError: fmt.Errorf("failed to load charge: 5e0f5caf-79d2-4396-b52a-0bb603162375, %s", errLoadChargeFailed.Error()),
			site:          site,
		},
		{
			name:               "auth repository error passed back to caller",
			mockAuthoriserUUID: ptr.To(uuid.NewString()),
			mockLoadRecords:    []eventstore.Record{{Version: 0, Data: []byte(`{"event": "Charge.Completed", "aggregateID": "74e13781-720a-4b92-97c3-095d203d96a7", "payload": {"charge":{"id":2456789}, "authorisation":{"authoriserId": 246, "type": "user"}}}`)}},
			mockErrors: mockErrors{
				AuthMockError: errGetUserUUIDFailed,
			},
			expectedError: errGetUserUUIDFailed,
			site:          site,
			charger:       charger,
		},
		{
			name:               "sqs error passed back to caller",
			mockAuthoriserUUID: ptr.To(uuid.NewString()),
			chargerID:          "PSL-532880",
			mockLoadRecords:    []eventstore.Record{{Version: 0, Data: []byte(claimedChargeCompletedBody)}},
			mockErrors: mockErrors{
				HandlerMockError: errSyncEventHandlerFailed,
				SqsMockError:     errSendToSqsFailed,
			},
			expectedError:        fmt.Errorf("send SQS message: %s", errSendToSqsFailed.Error()),
			site:                 site,
			charger:              charger,
			siteID:               12345,
			expectSqsMessageSend: true,
		},
		{
			name:               "authoriser not found handled gracefully",
			mockAuthoriserUUID: nil,
			chargerID:          "PSL-532880",
			mockLoadRecords:    []eventstore.Record{{Version: 0, Data: []byte(claimedChargeCompletedBody)}},
			site:               site,
			charger:            charger,
			siteID:             12345,
		},
		{
			name:            "charge with no auth, charger or site ",
			mockLoadRecords: []eventstore.Record{{Version: 0, Data: []byte(`{"event": "Charge.Completed", "aggregateID": "77e13781-720a-4b92-97c3-095d203d96a7", "payload": {"charge":{"id":2456789}, "authorisation":{"authoriserId": null, "type": null}}}`)}},
			expectedError:   nil,
		},
		{
			name:               "authoriser not uuid doesn't error",
			mockAuthoriserUUID: ptr.To("not-a-uuid"),
			chargerID:          "PSL-532880",
			mockLoadRecords:    []eventstore.Record{{Version: 0, Data: []byte(claimedChargeCompletedBody)}},
			site:               site,
			charger:            charger,
			siteID:             12345,
			expectedLogMessage: "ClaimCharge - error parsing user UUID err=invalid UUID length: 10 aggregateID=",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			serviceMocks := newClaimChargeServiceMocks(ctrl)
			var buff bytes.Buffer
			mockLogger := log.New(&buff, "[claim_charge_test]", log.Lmsgprefix)

			chargeID := uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375")
			authoriserPK := 246

			serviceMocks.store.EXPECT().
				Load(gomock.AssignableToTypeOf(context.Background()), chargeID).
				Return(tt.mockLoadRecords, tt.mockErrors.LoadMockError)

			serviceMocks.authRepo.
				EXPECT().
				GetUserUUIDFromAuthPK(gomock.AssignableToTypeOf(context.Background()), authoriserPK).
				Return(tt.mockAuthoriserUUID, tt.mockErrors.AuthMockError).
				AnyTimes()

			serviceMocks.chargersRepo.
				EXPECT().
				GetLinkedChargerByID(context.Background(), tt.chargerID).
				Return(tt.charger, tt.mockErrors.ChargerByIDMockError).
				AnyTimes()

			serviceMocks.chargersRepo.
				EXPECT().
				GetLinkedUsersByID(context.Background(), tt.chargerID).
				Return(tt.linkedUserIDs, tt.mockErrors.LinkedUsersByIDMockError).
				AnyTimes()

			serviceMocks.sitesRepo.
				EXPECT().
				GetByID(context.Background(), tt.siteID).
				Return(tt.site, tt.mockErrors.SitesMockError).
				AnyTimes()

			payload := charge.ClaimedPayload{
				ChargerID:   "PSL-532880",
				ChargerName: ptr.To("Charger-Name"),
				Door:        "A",
				ChargerType: "private",
			}

			if tt.charger != nil && tt.charger.IsHome == 1 {
				payload.UserIDs = tt.linkedUserIDs
			} else {
				userID := tt.mockAuthoriserUUID
				if userID != nil {
					userUUID, err := uuid.Parse(*userID)
					if err == nil {
						payload.UserIDs = []uuid.UUID{userUUID}
					}
				}
			}
			if tt.site != nil && tt.charger != nil && tt.charger.IsHome == 0 {
				payload.SiteID = ptr.To(utils.FabricateUUIDFromNumericID(int(tt.site.ID)))
				payload.SiteName = tt.charger.BusinessName

				if tt.site.GroupID != nil {
					payload.GroupID = tt.site.GroupID
				}
				if tt.site.GroupName != nil {
					payload.GroupName = tt.site.GroupName
				}
			}

			payloadJSON, _ := json.Marshal(payload)

			expectedMessageBodyRegex := charge.GenerateExpectedMessageBodyRegex(chargeID, charge.TypeClaimed, string(payloadJSON))

			processExpectation := serviceMocks.msgHandler.EXPECT().
				Process(context.Background(), gomock.AssignableToTypeOf(sqs.Message{})).
				Return(tt.mockErrors.HandlerMockError).
				AnyTimes()
			if tt.expectMsgNotProcessed {
				processExpectation.Times(0)
			}

			sqsExpectation := serviceMocks.sqsClient.
				EXPECT().SendMessage(gomock.AssignableToTypeOf(context.Background()), gomock.Regex(expectedMessageBodyRegex)).
				Return(ptr.To("message-1"), tt.mockErrors.SqsMockError).
				Times(0)
			if tt.expectSqsMessageSend {
				sqsExpectation.Times(1)
			}

			aggregateLoader := charge.NewAggregateLoader(serviceMocks.store)

			service := charge.NewClaimChargeService(mockLogger, serviceMocks.sqsClient, aggregateLoader, serviceMocks.chargersRepo, serviceMocks.sitesRepo, serviceMocks.authRepo, serviceMocks.msgHandler)

			err := service.ClaimCharge(context.Background(), chargeID)

			if tt.expectedError != nil {
				require.Error(t, err)
				require.Equal(t, tt.expectedError.Error(), err.Error())
				return
			}

			logMessages := buff.String()
			if tt.expectedLogMessage != "" {
				require.Contains(t, logMessages, tt.expectedLogMessage)
			} else {
				require.Empty(t, buff.String())
			}

			require.NoError(t, err)
		})
	}
}

type claimChargeServiceMocks struct {
	sqsClient       *mockSqs.MockClientOperations
	aggregateLoader *mockcharges.MockAggregateLoader
	msgHandler      *mockSqs.MockMessageHandler
	chargersRepo    *mockchargers.MockRepository
	sitesRepo       *mocksites.MockRepository
	authRepo        *mockauthorisers.MockRepository
	store           *mockstore.MockStore
}

func newClaimChargeServiceMocks(ctrl *gomock.Controller) *claimChargeServiceMocks {
	return &claimChargeServiceMocks{
		sqsClient:       mockSqs.NewMockClientOperations(ctrl),
		aggregateLoader: mockcharges.NewMockAggregateLoader(ctrl),
		msgHandler:      mockSqs.NewMockMessageHandler(ctrl),
		chargersRepo:    mockchargers.NewMockRepository(ctrl),
		sitesRepo:       mocksites.NewMockRepository(ctrl),
		authRepo:        mockauthorisers.NewMockRepository(ctrl),
		store:           mockstore.NewMockStore(ctrl),
	}
}
