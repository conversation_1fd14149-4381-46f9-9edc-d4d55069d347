package charges

import (
	"context"
	"errors"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/authorisers"
	"experience/libs/shared/go/service/utils"
	"experience/libs/shared/go/sqs"
	"fmt"
	"log"

	"github.com/google/uuid"
)

type confirmChargeServiceImpl struct {
	logger                *log.Logger
	sqsClient             sqs.ClientOperations
	aggregateLoader       AggregateLoader
	msgHandler            sqs.MessageHandler
	authorisersRepository authorisers.Repository
}

func NewConfirmChargeService(
	logger *log.Logger,
	sqsClient sqs.ClientOperations,
	aggregateLoader AggregateLoader,
	msgHandler sqs.MessageHandler,
	authorisersRepository authorisers.Repository,
) ConfirmChargeService {
	return &confirmChargeServiceImpl{
		logger:                logger,
		sqsClient:             sqsClient,
		aggregateLoader:       aggregateLoader,
		msgHandler:            msgHand<PERSON>,
		authorisersRepository: authorisersRepository,
	}
}

func (s *confirmChargeServiceImpl) ConfirmCharge(ctx context.Context, aggregateID uuid.UUID) error {
	charge, err := s.aggregateLoader.LoadCharge(ctx, aggregateID)
	if err != nil {
		return fmt.Errorf("load charge: %s, %w", aggregateID, err)
	}
	completedEvent, err := charge.FindCompletedEvent()
	if err != nil {
		return fmt.Errorf("find completed event: %s, %w", aggregateID, err)
	}

	if completedEvent.Payload.Authorisation.ClaimedChargeID == nil {
		return nil
	}

	var authID *string
	if completedEvent.Payload.Authorisation.AuthoriserID != nil {
		authID, err = s.authorisersRepository.GetUserUUIDFromAuthPK(ctx, *completedEvent.Payload.Authorisation.AuthoriserID)
		if err != nil {
			if !errors.Is(err, authorisers.ErrUserNotFound) {
				// If there's an error retrieving authoriser uuid it's not business critical, but we'd like to know about it
				s.logger.Printf("ConfirmCharge, cannot retrieve auth uuid for aggregate %s: %v", aggregateID, err)
			}
		}
	}

	chargeAuthorisationID := utils.FabricateUUIDFromNumericID(*completedEvent.Payload.Authorisation.ClaimedChargeID)
	confirmed := NewConfirmed(aggregateID, chargeAuthorisationID, completedEvent.Payload.Authorisation.Type, authID)
	return processDirectWithSQSFallback(ctx, &confirmed, s.msgHandler, s.sqsClient)
}
