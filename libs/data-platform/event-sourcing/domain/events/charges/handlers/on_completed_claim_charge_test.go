package handlers_test

import (
	"context"
	"encoding/json"
	"errors"
	chargecommands "experience/libs/data-platform/event-sourcing/domain/commands/charges"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/handlers"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/sqs"
	mocksqs "experience/libs/shared/go/sqs/test/mock"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestClaimChargeHandler_Execute(t *testing.T) {
	tests := []struct {
		name                    string
		event                   eventstore.Event
		mockSqsClientCalls      int
		mockSqsSendMessageError error
		expectedError           error
	}{
		{
			name: "It sends claimCharge command on charge.completed event",
			event: &chargeevents.Completed{
				Base: chargeevents.Base{
					AggregateID: uuid.New(),
				},
			},
			mockSqsClientCalls: 1,
		},
		{
			name: "It does not send claimCharge command on charge.costed event",
			event: &chargeevents.Costed{
				Base: chargeevents.Base{
					AggregateID: uuid.New(),
				},
			},
			mockSqsClientCalls: 0,
		},
		{
			name: "It returns error from the command to the caller",
			event: &chargeevents.Completed{
				Base: chargeevents.Base{
					AggregateID: uuid.New(),
				},
			},
			mockSqsClientCalls:      1,
			mockSqsSendMessageError: errors.New("mock_sqs_send_message_error"),
			expectedError:           errors.New("error claiming charge; mock_sqs_send_message_error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockSqsClient := mocksqs.NewMockClientOperations(ctrl)
			mockSqsClient.EXPECT().
				SendMessage(gomock.AssignableToTypeOf(context.Background()), gomock.AssignableToTypeOf(chargecommands.Claim{})).
				DoAndReturn(func(_ context.Context, command chargecommands.Claim) (sqs.MessageID, error) {
					require.Equal(t, chargecommands.TypeClaim, command.Type)
					return nil, tt.mockSqsSendMessageError
				}).
				Times(tt.mockSqsClientCalls)

			claimChargeHandler := handlers.NewClaimChargeHandler(mockSqsClient)

			err := claimChargeHandler.Execute(
				context.Background(),
				tt.event,
				json.RawMessage{})

			if tt.expectedError != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.expectedError.Error())
			}
		})
	}
}

func TestClaimChargeHandler_ErrorAllowed(t *testing.T) {
	tests := []struct {
		name string
		err  error
		want bool
	}{
		{
			name: "allows nil error",
			err:  nil,
			want: true,
		},
		{
			name: "forbids other errors",
			err:  errors.New("some error"),
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			u := handlers.NewClaimChargeHandler(nil)
			require.Equal(t, tt.want, u.ErrorAllowed(tt.err))
		})
	}
}
