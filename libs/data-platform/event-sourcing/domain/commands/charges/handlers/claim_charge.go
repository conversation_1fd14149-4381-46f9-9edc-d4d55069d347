package handlers

import (
	"context"
	chargecommands "experience/libs/data-platform/event-sourcing/domain/commands/charges"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	eventhandler "experience/libs/shared/go/event-store/handlers"
	"fmt"
)

type claimChargeCommand struct {
	claimChargeSvc chargeevents.ClaimChargeService
}

func NewClaimChargeCommandHandler(service chargeevents.ClaimChargeService) eventhandler.CommandHandler {
	return claimChargeCommand{
		claimChargeSvc: service,
	}
}

func (h claimChargeCommand) Execute(ctx context.Context, command eventstore.Command) error {
	if claimCmd, ok := command.(*chargecommands.Claim); ok {
		err := h.claimChargeSvc.ClaimCharge(ctx, claimCmd.Payload.ChargeID)
		if err != nil {
			return fmt.Errorf("claim charge: %w", err)
		}
		return err
	}
	return nil
}
