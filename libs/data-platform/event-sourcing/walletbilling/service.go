package walletbilling

import (
	"context"
	"experience/libs/shared/go/numbers"
	"fmt"
	"log"
	"time"

	"k8s.io/utils/ptr"
)

type walletbillingSvc struct {
	logger *log.Logger
	repo   TransactionWrapper
}

func NewService(
	logger *log.Logger,
	repo TransactionWrapper,
) Service {
	return &walletbillingSvc{
		logger: logger,
		repo:   repo,
	}
}

func (s *walletbillingSvc) Bill(ctx context.Context, chargeID uint32, presentmentAmount int32, presentmentCurrency string, settlementAmount int32, settlementCurrency, exchangeRate string) error {
	return s.repo.Atomic(ctx, func(t TransactionWrapper) error {
		billingData, err := t.BillingRepository().GetBillingDataFromCharge(ctx, chargeID)
		if err != nil {
			return fmt.Errorf("error when getting billing data from charge: %w", err)
		}

		if billingData.HasAlreadyBeenBilled() {
			s.logger.Printf("charge ID %d has already been billed (billing_event ID %d)", chargeID, *billingData.BillingEventID)
			return nil
		}

		var description string
		if billingData.LocationID != nil {
			description = fmt.Sprintf("Charged at location %d", *billingData.LocationID)
		}

		if billingData.BillingAccountID == nil {
			s.logger.Printf("accountID needs to be present in order to bill")
			return nil
		}

		var billingAccountID *int32
		if billingData.BillingAccountID != nil {
			convertedBillingAccountID, conversionErr := numbers.Convert[uint32, int32](*billingData.BillingAccountID)
			if conversionErr != nil {
				return fmt.Errorf("error converting billing account ID: %w", conversionErr)
			}
			billingAccountID = ptr.To(convertedBillingAccountID)
		}

		updateTime := time.Now().UTC()

		newBillingEventID, err := t.BillingRepository().CreateBillingEvent(
			ctx,
			presentmentAmount,
			presentmentCurrency,
			settlementAmount,
			settlementCurrency,
			exchangeRate,
			description,
			updateTime,
			billingAccountID,
			ptr.To(int32(0)),
		)
		if err != nil {
			return fmt.Errorf("creating billing event: %w", err)
		}

		billingEventID, err := numbers.Convert[int64, int32](newBillingEventID)
		if err != nil {
			return fmt.Errorf("error converting billingEventID: %w", err)
		}

		convertedChargeID, err := numbers.Convert[uint32, int32](chargeID)
		if err != nil {
			return fmt.Errorf("error converting charge ID: %w", err)
		}
		err = t.BillingRepository().SetEventIDAndAccountIDOnCharge(ctx, convertedChargeID, billingEventID, *billingAccountID)
		if err != nil {
			return fmt.Errorf("updating charge billing_event_id: %w", err)
		}

		newBalance, err := t.BillingRepository().UpdateBillingAccountBalance(ctx, *billingAccountID, settlementAmount, updateTime)
		if err != nil {
			return fmt.Errorf("updating billing_account balance: %w", err)
		}

		s.logger.Printf("billing complete: Charge ID: %d, Billing Account ID: %d, Billing Event ID: %d, Presentment Amount: %d %s, Settlement Amount: %d %s, New balance: %d", chargeID, billingAccountID, billingEventID, presentmentAmount, presentmentCurrency, settlementAmount, settlementCurrency, newBalance)

		if newBalance < 0 {
			s.logger.Printf("billing resulted in a negative balance: Charge ID: %d, Billing Account ID: %d, Billing Event ID: %d, Presentment Amount: %d %s, Settlement Amount: %d %s, New balance: %d", chargeID, *billingAccountID, billingEventID, presentmentAmount, presentmentCurrency, settlementAmount, settlementCurrency, newBalance)
			return nil
		}

		return nil
	})
}
