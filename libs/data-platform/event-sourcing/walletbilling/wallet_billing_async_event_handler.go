package walletbilling

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/billing"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/cost"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/event-store/handlers"
	"experience/libs/shared/go/numbers"
	exchangerates "experience/libs/shared/go/service/exchange-rates"
	"fmt"
	"log"
	"time"
)

const MaxDaysWithinWhichToBill = 120

type walletBillingAsyncEventHandler struct {
	logger               *log.Logger
	walletBillingService Service
	aggregateLoader      charges.AggregateLoader
	costCalcRepository   cost.Repository
	exchangeRates        exchangerates.API
}

func NewWalletBillingAsyncEventHandler(
	logger *log.Logger,
	walletBillingService Service,
	aggregateLoader charges.AggregateLoader,
	costCalcRepository cost.Repository,
	exchangeRates exchangerates.API,
) handlers.AsyncEventHandler {
	return &walletBillingAsyncEventHandler{
		logger,
		walletBillingService,
		aggregateLoader,
		costCalcRepository,
		exchangeRates,
	}
}

func (u *walletBillingAsyncEventHandler) Execute(ctx context.Context, tx *sql.Tx, event eventstore.Event, currentTransactionID uint64) error {
	switch event := event.(type) {
	case *charges.Billed:
		return u.handle(ctx, tx, event, currentTransactionID)
	default:
		return nil
	}
}

func (u *walletBillingAsyncEventHandler) handle(
	ctx context.Context,
	_ *sql.Tx,
	event *charges.Billed,
	_ uint64,
) error {
	if event.Payload.BillingType != billing.TypeWallet.String() {
		return nil
	}

	aggregate, err := u.aggregateLoader.LoadCharge(ctx, event.GetAggregateID())
	if err != nil {
		return fmt.Errorf("loading charge aggregate %s: %w", event.GetAggregateID(), err)
	}

	if isDateOlderThan(aggregate.UnpluggedAt, MaxDaysWithinWhichToBill) {
		return nil
	}

	completedEvent, err := aggregate.FindCompletedEvent()
	if err != nil {
		return fmt.Errorf("finding completed event %s: %w", aggregate.AggregateID, err)
	}

	u.logger.Printf("processed event type %v for aggregate ID %v \n", event.GetType(), aggregate.AggregateID)

	chargeID, err := numbers.Convert[int, uint32](completedEvent.Payload.Charge.ID)
	if err != nil {
		return fmt.Errorf("error converting charge ID: %w", err)
	}

	// we have to be confident that an Authoriser ID will definitely be present in the completed event when we are expected to perform wallet billing.
	presentmentCurrency, err := u.costCalcRepository.GetBillingAccountCurrencyByAuthoriserID(ctx, *completedEvent.Payload.Authorisation.AuthoriserID)
	if err != nil {
		return fmt.Errorf("failed to retrieve billing account for authoriser %d on aggregate %s: %w", *completedEvent.Payload.Authorisation.AuthoriserID, aggregate.AggregateID, err)
	}

	u.logger.Printf("Authoriser ID %d present for aggregate %s", *completedEvent.Payload.Authorisation.AuthoriserID, aggregate.AggregateID)

	exchangeRate, err := u.getExchangeRate(aggregate, event.Payload.SettlementCurrency, presentmentCurrency)
	if err != nil {
		return fmt.Errorf("unable to get exchange rate: %w", err)
	}

	presentmentAmount, err := aggregate.CalculatePresentmentAmount(*exchangeRate, event.Payload.SettlementAmount)
	if err != nil {
		return fmt.Errorf("calculating presentment amount: %w", err)
	}

	err = u.walletBillingService.Bill(
		ctx,
		chargeID,
		// Podadmin works based on an incorrect assumption where settlement and
		// presentment are flipped.
		-event.Payload.SettlementAmount,
		event.Payload.SettlementCurrency,
		-presentmentAmount,
		presentmentCurrency,
		fmt.Sprintf("%.6f", exchangeRate.Value),
	)
	if err != nil {
		u.logger.Printf("error when billing via wallet billing service: %s", err.Error())
		return nil
	}

	return nil
}

func (u *walletBillingAsyncEventHandler) getExchangeRate(aggregate *charges.Aggregate, settlementCurrency, presentmentCurrency string) (*exchangerates.ExchangeRate, error) {
	if settlementCurrency == presentmentCurrency {
		return &exchangerates.ExchangeRate{
			CurrencyFrom: settlementCurrency,
			CurrencyTo:   presentmentCurrency,
			Value:        1.0,
		}, nil
	}

	exchangeRate, err := u.exchangeRates.GetHistorical(settlementCurrency, presentmentCurrency, *aggregate.UnpluggedAt)
	if err != nil {
		return nil, fmt.Errorf("retrieving exchange rate: %w", err)
	}

	return exchangeRate, nil
}

func isDateOlderThan(date *time.Time, days int) bool {
	if date == nil {
		return false
	}
	thresholdDate := time.Now().AddDate(0, 0, -days)
	return date.Before(thresholdDate)
}
