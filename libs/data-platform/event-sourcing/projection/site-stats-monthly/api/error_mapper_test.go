package api_test

import (
	"errors"
	sitestatsmonthly "experience/libs/data-platform/event-sourcing/projection/site-stats-monthly"
	"experience/libs/data-platform/event-sourcing/projection/site-stats-monthly/api"
	errormapper "experience/libs/shared/go/http"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestHandleError(t *testing.T) {
	tests := []struct {
		name     string
		toHandle error
		want     error
	}{
		{
			name:     "unexpected error",
			toHandle: errors.New("random error"),
			want:     errors.New("unexpected error: random error"),
		},
		{
			name:     "group not found maps to ProjectionGroupStatisticsGroupNotFoundGoaError",
			toHandle: sitestatsmonthly.ErrGroupNotFound,
			want:     api.ProjectionGroupStatisticsGroupNotFoundGoaError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := errormapper.NewErrorMapper(api.ErrorMap).MapError(tt.toHandle)
			require.Error(t, got)
			require.Equal(t, tt.want.Error(), got.Error())
		})
	}
}
