package random

import (
	chargeprojection "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/shared/go/db"
	chargeevents "experience/libs/shared/go/service/utils"
	"fmt"
	"math"
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
)

type Driver struct {
	DriverID     *uuid.UUID
	AuthoriserID int64
}

type Charger struct {
	ID       string
	Name     *string
	Timezone *string
	SiteName *string
	Location int64
	Access   *string
	ChargePK int
	Rate     int64
}

type Authoriser struct {
	ID       int64
	UID      string
	Type     string
	GroupUID string
}

func ChargePK(lower, upper int) int {
	return gofakeit.IntRange(lower, upper)
}

func ChargeIDs(lower, upper int) (int, uuid.UUID) {
	chargePK := ChargePK(lower, upper)
	return chargePK, chargeevents.FabricateUUIDFromNumericID(chargePK)
}

func TimesCreateChargeProjections(times uint) []chargeprojection.Projection {
	projections := make([]chargeprojection.Projection, times)
	for i := uint(0); i < times; i++ {
		projections[i], _ = CreateChargeProjection(nil, nil)
	}
	return projections
}

func CreateChargeProjection(chargeID *uuid.UUID, seed *ChargeParamsSeed) (chargeprojection.Projection, sqlc.CreateChargeProjectionParams) {
	ccpp := CreateChargeProjectionParams(chargeID, seed)
	cp, _ := infra.ToChargeProjection(&ccpp)
	return *cp, ccpp
}

func TimesCreateChargeProjectionParams(times uint) []sqlc.CreateChargeProjectionParams {
	params := make([]sqlc.CreateChargeProjectionParams, times)
	for i := uint(0); i < times; i++ {
		params[i] = CreateChargeProjectionParams(nil, nil)
	}
	return params
}

func CreateChargeProjectionParams(chargeID *uuid.UUID, seed *ChargeParamsSeed) sqlc.CreateChargeProjectionParams {
	chargeID = PopulateUUID(chargeID)

	ccpp := sqlc.CreateChargeProjectionParams{
		ChargeUUID:           *chargeID,
		RewardEligibleEnergy: "0.00",
	}

	if gofakeit.Bool() {
		PopulateChargeClaimedData(nil, &ccpp, seed)
	}

	if gofakeit.Bool() {
		PopulateChargeConfirmedData(&ccpp, seed)
	}

	if gofakeit.Bool() {
		PopulateChargeCompletedData(&ccpp, seed)
	}

	if gofakeit.Bool() {
		PopulateChargeCostedData(&ccpp, seed)
	}

	if gofakeit.Bool() {
		PopulateChargeExpensedData(&ccpp)
	}

	PopulateRewardEligibleEnergy(&ccpp)

	return ccpp
}

func EnergyTotalTo1DecimalPlace(et float32) string {
	return fmt.Sprintf("%.1f", et)
}

func EnergyTotalFloat32() float32 {
	return gofakeit.Float32Range(0.1, 2000.0)
}

// EnergyTotalFloat64 returns an energy total rounded to the defined number of decimal places
func EnergyTotalFloat64(minimum, maximum float64, dp int) float64 {
	multiplier := math.Pow10(dp)
	energyTotal := gofakeit.Float64Range(minimum, maximum)
	return math.Round(energyTotal*multiplier) / multiplier
}

func PopulateChargeConfirmedData(ccpp *sqlc.CreateChargeProjectionParams, seed *ChargeParamsSeed) {
	confirmed := ptr.To(gofakeit.Bool())

	if seed != nil {
		confirmed = seed.Confirmed
	}

	ccpp.Confirmed = db.ToNullBool(confirmed)
}

func PopulateChargeClaimedData(userID *uuid.UUID, ccpp *sqlc.CreateChargeProjectionParams, seed *ChargeParamsSeed) {
	userID = PopulateUUID(userID)
	userIDs := UUIDsIncluding(userID)
	ccpp.UserIds = userIDs

	var chargerType *sqlc.Access
	if seed != nil {
		chargerType = seed.ChargerType
	}

	chargerData := NewChargerData(chargerType)
	ccpp.ChargerType = chargerData.Type
	ccpp.ChargerID = chargerData.ID
	ccpp.ChargerName = chargerData.Name
	ccpp.Door = chargerData.Door
	ccpp.ChargerTimezone = chargerData.Timezone
	ccpp.SiteName = chargerData.SiteName
	ccpp.GroupID = chargerData.GroupID
	ccpp.GroupName = chargerData.GroupName
	ccpp.SiteID = chargerData.SiteID

	if seed != nil {
		if seed.ChargerTimezone != nil {
			ccpp.ChargerTimezone = db.ToNullString(seed.ChargerTimezone)
		}
	}
}

func NewUTCTimeInLast12Months() (plugIn, startedAt time.Time) {
	maxDuration := time.Since(time.Now().AddDate(-1, 0, 0))
	billingCutoffDuration := 150 * 24 * 60 * 60 // added additional days to allow for longer durations
	randomDurationSeconds := gofakeit.IntRange(billingCutoffDuration, int(maxDuration.Seconds()))
	randomDuration := time.Second * time.Duration(randomDurationSeconds)

	plugIn = time.Now().UTC().Add(-randomDuration).Round(time.Second)
	startedAt = plugIn.Add(time.Second * time.Duration(gofakeit.IntRange(0, 10*24*60*60))).Round(time.Second)

	return plugIn, startedAt
}

func PopulateChargeCompletedDataInRange(ccpp *sqlc.CreateChargeProjectionParams) {
	plugIn, startedAt := NewUTCTimeInLast12Months()
	maxDuration := 10 * 24 * 60 * 60 // to avoid billing
	endedAt := startedAt.Add(time.Second * time.Duration(gofakeit.IntRange(0, maxDuration)))
	unplug := endedAt.Add(time.Second * time.Duration(gofakeit.IntRange(0, 1000)))

	duration := ptr.To(int32(endedAt.Sub(startedAt).Seconds()))

	ccpp.PluggedInAt = db.ToNullTime(ptr.To(plugIn))
	ccpp.UnpluggedAt = db.ToNullTime(ptr.To(unplug))
	ccpp.StartedAt = db.ToNullTime(ptr.To(startedAt))
	ccpp.EndedAt = db.ToNullTime(ptr.To(endedAt))
	ccpp.EnergyTotal = db.ToNullString(ptr.To(EnergyTotalTo1DecimalPlace(EnergyTotalFloat32())))
	ccpp.GenerationEnergyTotal = db.ToNullString(ptr.To(EnergyTotalTo1DecimalPlace(EnergyTotalFloat32())))
	ccpp.GridEnergyTotal = db.ToNullString(ptr.To(EnergyTotalTo1DecimalPlace(EnergyTotalFloat32())))
	ccpp.ChargeDurationTotal = db.ToNullInt32(duration)

	if gofakeit.Bool() {
		energyCost := int32(gofakeit.IntRange(0, 1000))
		ccpp.EnergyCost = db.ToNullInt32(ptr.To(energyCost))
	}

	ccpp.EnergyCostCurrency = db.ToNullString(ptr.To(gofakeit.CurrencyShort()))
}

func PopulateChargeCompletedData(ccpp *sqlc.CreateChargeProjectionParams, seed *ChargeParamsSeed) {
	plugIn := NewUTCTime()
	if seed != nil && seed.PluggedInAt != nil {
		plugIn = *seed.PluggedInAt
	}

	startedAt := plugIn.Add(time.Second * time.Duration(gofakeit.IntRange(0, 1000)))
	endedAt := startedAt.Add(time.Second * time.Duration(gofakeit.IntRange(0, 1000)))
	unplug := endedAt.Add(time.Second * time.Duration(gofakeit.IntRange(0, 1000)))
	if seed != nil && seed.UnpluggedAt != nil {
		unplug = *seed.UnpluggedAt
	}

	ccpp.PluggedInAt = db.ToNullTime(ptr.To(plugIn))
	ccpp.UnpluggedAt = db.ToNullTime(ptr.To(unplug))

	if gofakeit.Bool() {
		ccpp.StartedAt = db.ToNullTime(ptr.To(startedAt))
		ccpp.EndedAt = db.ToNullTime(ptr.To(endedAt))

		duration := int32(endedAt.Sub(startedAt).Seconds())
		generationEnergyTotal := EnergyTotalFloat32()
		gridEnergyTotal := EnergyTotalFloat32()
		energyTotal := generationEnergyTotal + gridEnergyTotal

		if seed != nil {
			if seed.ChargeDurationTotal != nil {
				duration = *seed.ChargeDurationTotal
			}
			if seed.EnergyTotal != nil {
				energyTotal = *seed.EnergyTotal
			}
			if seed.GenerationEnergyTotal != nil {
				generationEnergyTotal = *seed.GenerationEnergyTotal
			}
			if seed.GridEnergyTotal != nil {
				gridEnergyTotal = *seed.GridEnergyTotal
			}
		}

		ccpp.EnergyTotal = db.ToNullString(ptr.To(EnergyTotalTo1DecimalPlace(energyTotal)))
		ccpp.GenerationEnergyTotal = db.ToNullString(ptr.To(EnergyTotalTo1DecimalPlace(generationEnergyTotal)))
		ccpp.GridEnergyTotal = db.ToNullString(ptr.To(EnergyTotalTo1DecimalPlace(gridEnergyTotal)))
		ccpp.ChargeDurationTotal = db.ToNullInt32(ptr.To(duration))
	}
}

func PopulateChargeCostedData(ccpp *sqlc.CreateChargeProjectionParams, seed *ChargeParamsSeed) {
	if gofakeit.Bool() {
		energyCost := int32(gofakeit.IntRange(0, 1000))
		if seed != nil && seed.EnergyCost != nil {
			energyCost = *seed.EnergyCost
		}
		ccpp.EnergyCost = db.ToNullInt32(ptr.To(energyCost))
	}
	ccpp.EnergyCostCurrency = db.ToNullString(ptr.To(gofakeit.CurrencyShort()))
}

func PopulateChargeExpensedData(ccpp *sqlc.CreateChargeProjectionParams) {
	ccpp.ExpensedToGroup = db.ToNullUUID(ptr.To(uuid.New()))
	ccpp.ExpensedTo = db.ToNullString(ptr.To(gofakeit.Company()))
}

func PopulateRewardEligibleEnergy(ccpp *sqlc.CreateChargeProjectionParams) {
	ccpp.RewardEligibleEnergy = EnergyTotalTo1DecimalPlace(EnergyTotalFloat32())
	ccpp.VehicleID = db.ToNullUUID(ptr.To(uuid.New()))
}

type ChargeParamsSeed struct {
	PluggedInAt           *time.Time
	UnpluggedAt           *time.Time
	EnergyTotal           *float32
	GenerationEnergyTotal *float32
	GridEnergyTotal       *float32
	ChargeDurationTotal   *int32
	SettlementAmount      *int32
	SettlementCurrency    *string
	EnergyCost            *int32
	ChargerType           *sqlc.Access
	ChargerTimezone       *string
	ChargerName           *string
	SiteName              *string
	ChargerID             string
	Confirmed             *bool
}
