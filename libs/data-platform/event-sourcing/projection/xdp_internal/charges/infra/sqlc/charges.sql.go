// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: charges.sql

package sqlc

import (
	"context"
	"database/sql"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

const createChargeProjection = `-- name: CreateChargeProjection :one
INSERT INTO projections.charges(charge_uuid, started_at, ended_at, plugged_in_at, unplugged_at, energy_total,
                                charge_duration_total, charger_id, door, expensed_to_group, expensed_to,
                                settlement_amount, settlement_currency, charger_name, charger_type, site_name,
                                group_id, site_id, group_name, energy_cost, energy_cost_currency, charger_timezone,
                                user_ids, generation_energy_total, grid_energy_total, confirmed, reward_eligible_energy, vehicle_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28)
ON CONFLICT (charge_uuid)
  DO UPDATE SET started_at              = $2,
                ended_at                = $3,
                plugged_in_at           = $4,
                unplugged_at            = $5,
                energy_total            = $6,
                charge_duration_total   = $7,
                charger_id              = $8,
                door                    = $9,
                expensed_to_group       = $10,
                expensed_to             = $11,
                settlement_amount       = $12,
                settlement_currency     = $13,
                charger_name            = $14,
                charger_type            = $15,
                site_name               = $16,
                group_id                = $17,
                site_id                 = $18,
                group_name              = $19,
                energy_cost             = $20,
                energy_cost_currency    = $21,
                charger_timezone        = $22,
                user_ids                = $23,
                generation_energy_total = $24,
                grid_energy_total       = $25,
                confirmed               = $26,
                reward_eligible_energy  = $27,
                vehicle_id              = $28
       RETURNING id, charge_uuid, started_at, ended_at, energy_total, charge_duration_total, charger_id, door, plugged_in_at, unplugged_at, settlement_amount, settlement_currency, expensed_to_group, expensed_to, charger_name, charger_type, site_name, group_id, site_id, group_name, energy_cost, energy_cost_currency, charger_timezone, user_ids, generation_energy_total, grid_energy_total, confirmed, reward_eligible_energy, vehicle_id
`

type CreateChargeProjectionParams struct {
	ChargeUUID            uuid.UUID      `json:"charge_uuid"`
	StartedAt             sql.NullTime   `json:"started_at"`
	EndedAt               sql.NullTime   `json:"ended_at"`
	PluggedInAt           sql.NullTime   `json:"plugged_in_at"`
	UnpluggedAt           sql.NullTime   `json:"unplugged_at"`
	EnergyTotal           sql.NullString `json:"energy_total"`
	ChargeDurationTotal   sql.NullInt32  `json:"charge_duration_total"`
	ChargerID             sql.NullString `json:"charger_id"`
	Door                  sql.NullString `json:"door"`
	ExpensedToGroup       uuid.NullUUID  `json:"expensed_to_group"`
	ExpensedTo            sql.NullString `json:"expensed_to"`
	SettlementAmount      sql.NullInt32  `json:"settlement_amount"`
	SettlementCurrency    sql.NullString `json:"settlement_currency"`
	ChargerName           sql.NullString `json:"charger_name"`
	ChargerType           NullAccess     `json:"charger_type"`
	SiteName              sql.NullString `json:"site_name"`
	GroupID               uuid.NullUUID  `json:"group_id"`
	SiteID                uuid.NullUUID  `json:"site_id"`
	GroupName             sql.NullString `json:"group_name"`
	EnergyCost            sql.NullInt32  `json:"energy_cost"`
	EnergyCostCurrency    sql.NullString `json:"energy_cost_currency"`
	ChargerTimezone       sql.NullString `json:"charger_timezone"`
	UserIds               []uuid.UUID    `json:"user_ids"`
	GenerationEnergyTotal sql.NullString `json:"generation_energy_total"`
	GridEnergyTotal       sql.NullString `json:"grid_energy_total"`
	Confirmed             sql.NullBool   `json:"confirmed"`
	RewardEligibleEnergy  string         `json:"reward_eligible_energy"`
	VehicleID             uuid.NullUUID  `json:"vehicle_id"`
}

func (q *Queries) CreateChargeProjection(ctx context.Context, arg CreateChargeProjectionParams) (ProjectionsCharge, error) {
	row := q.db.QueryRowContext(ctx, createChargeProjection,
		arg.ChargeUUID,
		arg.StartedAt,
		arg.EndedAt,
		arg.PluggedInAt,
		arg.UnpluggedAt,
		arg.EnergyTotal,
		arg.ChargeDurationTotal,
		arg.ChargerID,
		arg.Door,
		arg.ExpensedToGroup,
		arg.ExpensedTo,
		arg.SettlementAmount,
		arg.SettlementCurrency,
		arg.ChargerName,
		arg.ChargerType,
		arg.SiteName,
		arg.GroupID,
		arg.SiteID,
		arg.GroupName,
		arg.EnergyCost,
		arg.EnergyCostCurrency,
		arg.ChargerTimezone,
		pq.Array(arg.UserIds),
		arg.GenerationEnergyTotal,
		arg.GridEnergyTotal,
		arg.Confirmed,
		arg.RewardEligibleEnergy,
		arg.VehicleID,
	)
	var i ProjectionsCharge
	err := row.Scan(
		&i.ID,
		&i.ChargeUUID,
		&i.StartedAt,
		&i.EndedAt,
		&i.EnergyTotal,
		&i.ChargeDurationTotal,
		&i.ChargerID,
		&i.Door,
		&i.PluggedInAt,
		&i.UnpluggedAt,
		&i.SettlementAmount,
		&i.SettlementCurrency,
		&i.ExpensedToGroup,
		&i.ExpensedTo,
		&i.ChargerName,
		&i.ChargerType,
		&i.SiteName,
		&i.GroupID,
		&i.SiteID,
		&i.GroupName,
		&i.EnergyCost,
		&i.EnergyCostCurrency,
		&i.ChargerTimezone,
		pq.Array(&i.UserIds),
		&i.GenerationEnergyTotal,
		&i.GridEnergyTotal,
		&i.Confirmed,
		&i.RewardEligibleEnergy,
		&i.VehicleID,
	)
	return i, err
}

const deleteChargesIDRange = `-- name: DeleteChargesIDRange :exec
DELETE
FROM projections.charges
WHERE id BETWEEN $1 AND $2
`

type DeleteChargesIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteChargesIDRange(ctx context.Context, arg DeleteChargesIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteChargesIDRange, arg.ID, arg.ID_2)
	return err
}

const findByChargeID = `-- name: FindByChargeID :one
SELECT id, charge_uuid, started_at, ended_at, energy_total, charge_duration_total, charger_id, door, plugged_in_at, unplugged_at, settlement_amount, settlement_currency, expensed_to_group, expensed_to, charger_name, charger_type, site_name, group_id, site_id, group_name, energy_cost, energy_cost_currency, charger_timezone, user_ids, generation_energy_total, grid_energy_total, confirmed, reward_eligible_energy, vehicle_id
FROM projections.charges
WHERE charge_uuid = $1
`

func (q *Queries) FindByChargeID(ctx context.Context, chargeUuid uuid.UUID) (ProjectionsCharge, error) {
	row := q.db.QueryRowContext(ctx, findByChargeID, chargeUuid)
	var i ProjectionsCharge
	err := row.Scan(
		&i.ID,
		&i.ChargeUUID,
		&i.StartedAt,
		&i.EndedAt,
		&i.EnergyTotal,
		&i.ChargeDurationTotal,
		&i.ChargerID,
		&i.Door,
		&i.PluggedInAt,
		&i.UnpluggedAt,
		&i.SettlementAmount,
		&i.SettlementCurrency,
		&i.ExpensedToGroup,
		&i.ExpensedTo,
		&i.ChargerName,
		&i.ChargerType,
		&i.SiteName,
		&i.GroupID,
		&i.SiteID,
		&i.GroupName,
		&i.EnergyCost,
		&i.EnergyCostCurrency,
		&i.ChargerTimezone,
		pq.Array(&i.UserIds),
		&i.GenerationEnergyTotal,
		&i.GridEnergyTotal,
		&i.Confirmed,
		&i.RewardEligibleEnergy,
		&i.VehicleID,
	)
	return i, err
}
