#!/bin/sh
echo "Create AWS Secrets Manager entries for localstack"

awslocal --endpoint-url=http://localstack:4566 secretsmanager create-secret --name data-platform-api-config --secret-string file:///etc/localstack/init/ready.d/env/data-platform-config-secret.json
awslocal --endpoint-url=http://localstack:4566 secretsmanager create-secret --name data-platform-carbon-intensity-store-config --secret-string file:///etc/localstack/init/ready.d/env/data-platform-carbon-intensity-config-secret.json
awslocal --endpoint-url=http://localstack:4566 secretsmanager create-secret --name data-platform-task-runner-config --secret-string file:///etc/localstack/init/ready.d/env/data-platform-task-runner-config-secret.json
awslocal --endpoint-url=http://localstack:4566 secretsmanager create-secret --name data-platform-test-config --secret-string file:///etc/localstack/init/ready.d/env/data-platform-test-config-secret.json
awslocal --endpoint-url=http://localstack:4566 secretsmanager create-secret --name data-platform-events-queue-worker-config --secret-string file:///etc/localstack/init/ready.d/env/data-platform-events-queue-worker-config-secret.json
awslocal --endpoint-url=http://localstack:4566 secretsmanager create-secret --name data-platform-async-processor-config --secret-string file:///etc/localstack/init/ready.d/env/data-platform-async-processor-config-secret.json

touch /tmp/localstack # signal that localstack has finished setup
