#!/bin/sh

echo "Create AWS EventBridge target queue"
awslocal --endpoint-url=http://localstack:4566 sqs create-queue --queue-name event-bridge-target-queue

echo "Create AWS EventBridge event bus"
awslocal --endpoint-url=http://localstack:4566 events create-event-bus --name experience-event-bus

echo "Create EventBridge rule"
awslocal --endpoint-url=http://localstack:4566 events put-rule --name send-events-to-event-bridge-target-queue --event-bus-name experience-event-bus --event-pattern '{"detail-type": ["ChargeCompleted"]}'

echo "Event rule created: send-events-from-experience-event-bus-to-my-queue-arn"
awslocal --endpoint-url=http://localstack:4566 events put-targets --event-bus-name experience-event-bus --rule send-events-to-event-bridge-target-queue --targets "Id"="1","Arn"="arn:aws:sqs:eu-west-1:000000000000:event-bridge-target-queue"

echo "Event rule target created for: send-events-from-experience-event-bus-to-my-queue-arn"
