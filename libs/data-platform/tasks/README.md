# Data Platform Tasks

This library contains long-running tasks for Experience Data Platform. Currently, tasks are executed by `data-platform-task-runner` app.

## Rules for a task

1. Resides in its own folder and package.
2. Has its own queries and sqlc folder.
3. Implements a `RunnableTask` interface.
4. Can have any number of params, but these need to be returned by `GetParamNames` function in task's package.
