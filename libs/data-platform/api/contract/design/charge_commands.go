package design

import (
	. "goa.design/goa/v3/dsl"
)

var _ = Service("Charge commands", func() {
	Description("Commands API for interfacing with event store.")

	Method("Correct energy cost", func() {
		Description("Update the energy cost of a charge.")

		Payload(func() {
			Attribute("chargeID", String, func() {
				Description("UUID of the charge to correct.")
				Format(FormatUUID)
			})
			Attribute("cost", Int, func() {
				Description("New cost of the charge in lowest denomination of its currency (pence, euro cents).")
				Minimum(1)
				Example(16)
			})
			Attribute("submittedBy", String, func() {
				Description("Who has submitted the correction request.")
				Example("John Doe")
			})
			Required("chargeID", "cost")
		})

		Result(AggregateCostCorrectedResponse)

		Error("charge_not_found", ChargeNotFound)
		Error("charge_expensed", ChargeExpensed)
		Error("duplicate_requests", EventOutOfDate)
		Error("bad_request")

		HTTP(func() {
			POST("/commands/charges/{chargeID}/correct-energy-cost")
			Response(StatusOK)
			Response("charge_not_found", StatusNotFound)
			Response("charge_expensed", StatusBadRequest)
			Response("duplicate_requests", StatusConflict)
			Response("bad_request", StatusBadRequest, func() {
				Description("Returns bad request when request parameters are invalid")
			})
		})
	})

	Method("Correct settlement amount", func() {
		Description("Update the settlement amount of a charge.")

		Payload(func() {
			Attribute("chargeID", String, func() {
				Description("UUID of the charge to correct.")
				Format(FormatUUID)
			})
			Attribute("settlementAmount", Int, func() {
				Description("New settlement amount of the charge in lowest denomination of its currency (pence, euro cents).")
				Minimum(1)
				Example(16)
			})
			Attribute("submittedBy", String, func() {
				Description("Who has submitted the correction request.")
				Example("John Doe")
			})
			Required("chargeID", "settlementAmount")
		})

		Result(AggregateSettlementAmountCorrectedResponse)

		Error("charge_not_found", ChargeNotFound)
		Error("duplicate_requests", EventOutOfDate)
		Error("bad_request")

		HTTP(func() {
			POST("/commands/charges/{chargeID}/correct-settlement-amount")
			Response(StatusOK)
			Response("charge_not_found", StatusNotFound)
			Response("duplicate_requests", StatusConflict)
			Response("bad_request", StatusBadRequest, func() {
				Description("Returns bad request when request parameters are invalid")
			})
		})
	})
})
