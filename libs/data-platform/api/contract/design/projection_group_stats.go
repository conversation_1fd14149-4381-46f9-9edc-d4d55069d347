package design

import (
	. "goa.design/goa/v3/dsl"
)

var _ = Service("Projection Group Statistics", func() {
	Description("Group charging statistics from projections")

	Method("Group site statistics", func() {
		Description("Charge statistics for all sites within a group for the given month.")
		Payload(func() {
			Attribute("groupId", String, func() {
				Description("UUID of the group.")
				Format(FormatUUID)
			})
			Attribute("year", Int, func() {
				Description("Year to be queried.")
				Minimum(1)
				Example(2021)
			})
			Attribute("month", Int, func() {
				Description("Month to be queried where Jan = 1, Feb = 2 etc...")
				Minimum(1)
				Maximum(12)
			})
			Required("groupId", "year", "month")
		})

		Result(GroupSitesStatsResponse)

		Error("group_not_found", GroupNotFound, "Group not found")
		Error("bad_request")

		HTTP(func() {
			GET("/charges/groups/{groupId}/sites")
			Param("groupId", String)
			Param("year", Int)
			Param("month", Int)
			Response("group_not_found", StatusNotFound)
			Response("bad_request", StatusBadRequest, func() {
				Description("Returns bad request when request parameters are invalid")
			})
		})
	})
})
