// Code generated by goa v3.20.1, DO NOT EDIT.
//
// User Charges client HTTP transport
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"context"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// Client lists the User Charges service endpoint HTTP clients.
type Client struct {
	// GroupAndUserCharges Doer is the HTTP client used to make requests to the
	// Group And User Charges endpoint.
	GroupAndUserChargesDoer goahttp.Doer

	// RestoreResponseBody controls whether the response bodies are reset after
	// decoding so they can be read again.
	RestoreResponseBody bool

	scheme  string
	host    string
	encoder func(*http.Request) goahttp.Encoder
	decoder func(*http.Response) goahttp.Decoder
}

// NewClient instantiates HTTP clients for all the User Charges service servers.
func NewClient(
	scheme string,
	host string,
	doer goahttp.Doer,
	enc func(*http.Request) goahttp.Encoder,
	dec func(*http.Response) goahttp.Decoder,
	restoreBody bool,
) *Client {
	return &Client{
		GroupAndUserChargesDoer: doer,
		RestoreResponseBody:     restoreBody,
		scheme:                  scheme,
		host:                    host,
		decoder:                 dec,
		encoder:                 enc,
	}
}

// GroupAndUserCharges returns an endpoint that makes HTTP requests to the User
// Charges service Group And User Charges server.
func (c *Client) GroupAndUserCharges() goa.Endpoint {
	var (
		encodeRequest  = EncodeGroupAndUserChargesRequest(c.encoder)
		decodeResponse = DecodeGroupAndUserChargesResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildGroupAndUserChargesRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.GroupAndUserChargesDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("User Charges", "Group And User Charges", err)
		}
		return decodeResponse(resp)
	}
}
