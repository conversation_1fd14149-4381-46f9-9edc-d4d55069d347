// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Link user endpoints
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package linkuser

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Endpoints wraps the "Link user" service endpoints.
type Endpoints struct {
	LinkUserToHomeCharger goa.Endpoint
}

// NewEndpoints wraps the methods of the "Link user" service with endpoints.
func NewEndpoints(s Service) *Endpoints {
	return &Endpoints{
		LinkUserToHomeCharger: NewLinkUserToHomeChargerEndpoint(s),
	}
}

// Use applies the given middleware to all the "Link user" service endpoints.
func (e *Endpoints) Use(m func(goa.Endpoint) goa.Endpoint) {
	e.LinkUserToHomeCharger = m(e.LinkUserToHomeCharger)
}

// NewLinkUserToHomeChargerEndpoint returns an endpoint function that calls the
// method "Link user to home charger" of service "Link user".
func NewLinkUserToHomeChargerEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*LinkUserToHomeChargerPayload)
		return nil, s.LinkUserToHomeCharger(ctx, p)
	}
}
