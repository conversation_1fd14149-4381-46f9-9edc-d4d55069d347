CREATE TABLE IF NOT EXISTS podpoint.ev_driver_domains
(
    id           bigint                    NOT NULL PRIMARY KEY,
    domain_name  varchar(190)              NOT NULL,
    group_id     bigint                    NOT NULL,
    created_at timestamp WITHOUT TIME ZONE NOT NULL,
    updated_at timestamp WITHOUT TIME ZONE,
    deleted_at timestamp WITHOUT TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS ev_driver_domains_domain_name_unique
  ON podpoint.ev_driver_domains (domain_name);

-- these are not unique
-- this index does not exist in podadmin mysql but we do query by this
CREATE INDEX IF NOT EXISTS ev_driver_domains_group_id
  ON podpoint.ev_driver_domains (group_id);

ALTER TABLE IF EXISTS podpoint.ev_driver_domains OWNER TO postgres;
GRANT SELECT ON podpoint.ev_driver_domains TO xdp_api;
