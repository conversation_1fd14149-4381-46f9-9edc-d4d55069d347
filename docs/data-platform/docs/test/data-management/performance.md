## Performance tests:

Performance tests are used as an automated early indicator of endpoint performance and, over time, will test query stability as dev/staging dataset grows.

### As-is:
In the performance suite we use polluter to seed test data. Using testify this data is inserted before any test is run and cleaned up after all tests in the file have finished
XDP **performance** test ids are in the range of ********* - *********. Each run of tests will tidy up any data created and start the id sequence over again on the next run
**We will assume dev/staging data with ids in this range (********* - *********) can be deleted**
In order to reduce risks of conflicts and permission required to the test account we will **use negative ids for the events_store.events table id column**
We need to consider conflicts if concurrent builds against dev and staging are being run due to there only being a single staging PodAdmin database
- Conflicts have been seen with other teams inserting into our range during execution
  - To alleviate this we've added persistent test data above our id range in the tables where we've seen conflicts
  - If we still see conflicts we'll look to replace polluter with the end-to-end test approach of only removing what we have inserted
- When writing to `event_store.events` we are unable to pass in the current transaction id through polluter, so for dev and staging we have added the default value pg_current_xact_id() for event_store.events.transaction_id

### Do:
- Set up a scenario where we can make repeated calls to an endpoint and receive `200` responses (or any other status code that is considered a success for this endpoint)
- Verify the api returns a meaningful response and is not simply empty
- Set a baseline for response time
  - Be open to changing the response time
  - Track changes to response time in Git

### Do not:
- Write a performance test unless we understand what we want to test
  - most complex path
- Make optimisations to anywhere other than the bottleneck
