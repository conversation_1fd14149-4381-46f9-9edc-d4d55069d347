# Procedure to create test projections in staging for mobile app testing

* This creates charge projections across every month starting on Jan 2023 up to and including the current date
* Charge projections can be of charger type `home`, `public` or `private`
* When charger type is `home` then generation and grid energy values will be present


```sql
DO
$do$
    DECLARE
        user_uuid uuid := '488e2e98-4e3d-4919-812f-35c539edb607';
        charger_psl varchar := 'PSL-800001';
        charger_access access := 'home';
    BEGIN
        FOR k IN 2023..2024 LOOP
            FOR i IN 1..12 LOOP
                FOR j in 1..31 LOOP
                    IF i = 2 AND j > 28 THEN
                        CONTINUE;
                    END IF;
                    IF (i = 4 OR i = 6 OR i = 9 OR i = 11) AND j > 30 THEN
                        CONTINUE;
                    END IF;
                    IF (k || '-' || i || '-' || j)::timestamp  > current_date THEN
                        RETURN;
                    END IF;
INSERT INTO projections.charges (id, charge_uuid, started_at, ended_at, energy_total, charge_duration_total, charger_id, door, plugged_in_at, unplugged_at, settlement_amount, settlement_currency, expensed_to_group, expensed_to, charger_name, charger_type, site_name, group_id, site_id, group_name, energy_cost, energy_cost_currency, charger_timezone, user_ids, generation_energy_total, grid_energy_total)
VALUES (DEFAULT, gen_random_uuid(), null, null, 1.5 * j * i, 0, charger_psl, 'A', (k || '-' || i || '-' || j || ' 11:29:57.238000 +00:00')::timestamp, (k || '-' || i || '-' || j || ' 21:30:02.405000 +00:00')::timestamp, 0, 'GBP', null, null, null, charger_access, null, null, null, null, i * j * 10, 'GBP', 'Europe/London', ('{' || user_uuid || '}')::uuid[], CASE WHEN charger_access = 'home' THEN 0.5 * j * i END, CASE WHEN charger_access = 'home' THEN j * i END);
                END LOOP;
            END LOOP;
        END LOOP;
    END
$do$
```
