workspace extends ../now.dsl  {
    name "Mobile"
    description "This is a model of the Mobile system as it currently is."

    !docs .

    model {
      googleIdentityPlatform = softwareSystem "Google Identity Platform"  {
        description "Authentication PaaS run by Google."
        tags "External"
        url https://cloud.google.com/identity-platform

        googleIdentityPlatformApi = container "Google Identity Platform API" {
          description "Provides authentication API for client and server implementations."
          tags "External"
          url https://firebase.google.com/docs/firestore/client/libraries
        }
      }

      stripe = softwareSystem "Stripe Payment Platform" {
        description "Payment Platform run by Stripe."
        tags "External"
        url http://www.stripe.com
      }

      loqate = softwareSystem "Loqate service"  {
        description "Address verification service."
        tags "External"
        url https://www.loqate.com/en-gb/

        captureApi = container "Loqate Address Capture API" {
          description "Provides an API find and retrieve an address."
          tags "External"
          url https://www.loqate.com/developers/api/capture/
        }
      }

      !ref mobile {
        driverApp = container "Opencharge Mobile App" {
          description "Mobile app used by authenticated drivers."
          technology "React Native"
          tags "Mobile,React Native"
        }
        driverAccountPrivateLink = container "Driver Account PrivateLink" {
          description "Establish connectivity with Driver Account VPC."
          technology "AWS PrivateLink"
          tags "Mobile,PrivateLink"
        }
        driverWebapp = container "Opencharge Webapp" {
          description "Web app used by annonymous drivers."
          technology "PHP"
          tags "Mobile,PHP"
        }
        installerApp = container "Installer Mobile App" {
          description "Mobile app used by authenticated installers."
          technology "Flutter"
          tags "Mobile,Flutter"
        }
      }

       # domain level relationships

      ## relationships between software systems and software systems
      experienceDomain -> googleIdentityPlatform "Uses"

      # subdomain level relationships
      mobile -> googleIdentityPlatform "Uses"

      # relationships to/from containers
      driverAccountPrivateLink -> driverAccountApi "Make API calls via" "JSON/HTTP"
      driverAccountApi ->  googleIdentityPlatformApi "Make API calls to" "JSON/HTTPS"
      driverAccountWebapp ->  googleIdentityPlatformApi "Make API calls to" "JSON/HTTPS"
      driverApp -> googleIdentityPlatformApi "Authenticate user requests" "JSON/HTTPS"
      driverApp -> mobileApi "Make API calls to" "JSON/HTTPS"
      driverApp -> api3 "Make API calls to" "JSON/HTTPS"
      driverWebapp -> api3 "Make API calls to" "JSON/HTTPS"
      installerBff -> googleIdentityPlatformApi "Make API calls to" "JSON/HTTPS"
      installerBff -> installerApi "Make API calls to" "JSON/HTTPS"
      installerApp -> googleIdentityPlatformApi "Authenticate user requests" "JSON/HTTPS"
      installerApp -> installerBff "Make API calls to" "JSON/HTTPS"
      installerAccountWebapp ->  googleIdentityPlatformApi "Make API calls to" "JSON/HTTPS"
      billingAPI -> stripe "Make API calls to" "JSON/HTTPS"
      mobileApi -> captureApi "Make API calls to" "JSON/HTTPS"
      mobileApi -> driverAccountApi "Make API calls to" "JSON/HTTPS"
      mobileApi -> billingAPI "Make API calls to" "JSON/HTTPS"
    }

    views {
        container dataPlatform "XdpContainers" {
            include *
            autoLayout
        }
        systemContext mobile "MobileSystemContext" {
            include *
            autoLayout
        }
        container mobile "MobileContainers" {
            include *
            autoLayout
        }
        container googleIdentityPlatform "GoogleIdentityPlatformContainers" {
            include *
            autoLayout
        }
        container loqate "LoqateContainers" {
            include *
            autoLayout
        }
    }
}
