# How to refresh RFID authorisers from the source of truth

## The purpose of this article

This article describes how to refresh the authorisers table in the podadmin database with authorisers of type `rfid`
based on the source of truth that is the RFID database itself. This can be necessary if / when the data becomes out of
sync.

**This is a manual process requiring production database access and so care must be taken.**

We will aim to retire the need for this by offering similar functionality via the Site Service.

## Trigger

When the authorisers table in the podadmin databae has become out of sync with the cards table in the RFID database.

## Before you begin

In order to complete this task you will require the following:

- Access to the podadmin production database
- Access to the RFID production database

## Step 1 - Generate SQL to populate active cards

The following SQL will generate the necessary INSERT statements to populate the authorisers table with currently active
RFID cards. This SQL should be run on the production RFID database.

```
select concat('insert into authorisers (uid, `type`) values(\'', c.uid, '\', \'rfid\'', ');')
from cards c
where c.deleted_at is null
order by uid asc;
```

## Step 2 - Generate SQL to populate inactive cards

The following SQL will generate the necessary INSERT statements to populate the authorisers table with currently
inactive RFID cards. This SQL should be run on the production RFID database.

```
select concat('insert into authorisers (uid, `type`, deleted_at) values(\'', c.uid, '\', \'rfid\', \'', c.deleted_at, '\'', ');')
from cards c
where c.deleted_at is not null
order by uid asc;
```

## Step 3 - Combine the outputs from steps 1 and 2

Create a file named `refresh.sql` or something to that effect and add the following line of SQL:

```
delete from authorisers where `type` = 'rfid';
```

Then paste in the INSERT statements generated in steps 1 and 2.

## Step 4 - Run .sql script

Connect to the production podadmin database with a read-write connection and execute your SQL. Be sure to do this with
in a transaction and commit the transaction once the change has been verified. Note that the authorisers table is
heavily used so a long running transaction is likely to cause some errors in production.
