# Support for Tesco Clubcard integration

- **Status**: Accepted
- **Deciders**: <PERSON>, <PERSON>, <PERSON>
- **Date**: 2025-02-04

## Context

- Development of the Pod Point mobile app, which is used by users of public chargers, is being transitioned to Create Futures.
- One of the first features they will be adding is integration with Tesco Clubcard.
- Over time the goal is for Create Futures to transition the mobile app to be only for use on public chargers.
- This will be done using the Pod Point OCPI solution as a back end with all dependencies on API3 removed.
- By November 2025 Pod Point need to have implemented payment roaming within our OCPI solution.

## Decision Drivers

- To get Create Futures up and running with development as soon as possible.
- To begin the transition from API3 to OCPI as the back end.

## Considered Options

- Fetch Charge Detail Records (CDRs) over OCPI.
- Fetch recent charges via API3.

## Decision

Chosen option: "Fetch CDRs over OCPI", because it begins the transition towards OCPI without requiring a complete roaming implementation.
Additionally, relying on API3 would not scale well as polling for recent charges would need to be per user whereas CDRs can be system-wide.

## Consequences

### Positive Consequences

- Begins the transition towards the public app using OCPI as its back end.
- Begins the implementation of the OCPI modules required to deliver payment roaming later this year.
- Avoids adding new dependencies on API3.
- Avoids a potential scaling issue polling API3 for completed charges.

### Negative Consequences

- Need to implement CDRs module before Commands and Session modules.
- Need to implement charge completion event handler which persists CDRs which will likely be thrown away eventually (all other code will be reusable).
