```mermaid
---
title: Stripe payout status diagram
---
stateDiagram-v2
  classDef scheduled fill:#d2ccca;
  classDef webhook fill:#df8a24

  statement_generated: Statement generated
  connected_account_updated: Connected account updated (webhook)
  transfers_enabled: Are transfers enabled?
  pending: Pending
  deferred: Deferred
  warning: Warning
  transferred: Transferred
  paid: Paid

  statement_generated --> transfers_enabled
  connected_account_updated --> transfers_enabled
  transfers_enabled --> pending: Yes
  transfers_enabled --> deferred: No
  deferred --> pending
  note right of deferred: Scheduled task. Daily until the 8th. If transfers enabled
  pending --> warning
  note right of warning: Scheduled task. Daily from the 22nd. If creation of the Stripe transfer fails
  pending --> transferred
  note right of transferred: Scheduled task. Daily from the 22nd. If creation of the Stripe transfer is successful
  warning --> transferred
  transferred --> paid
  note right of paid: <PERSON><PERSON> moves money into connected account bank account and informs us via a webhook

  class statement_generated,transfers_enabled,pending,deferred,warning,transferred scheduled
  class connected_account_updated,paid webhook
```
