import './tailwind.css';
import {
  NavigationLinkProps,
  SidebarLayout,
} from '@experience/shared/react/application-shells/sidebar-layout';
import { ttCommonsPro } from './font';
import React from 'react';

export const metadata = {
  icons: {
    icon: '/icon.ico',
  },
  title: 'OCPI Service',
  description: 'OCPI Service',
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface RootLayoutProps {
  children: React.ReactNode;
}

const RootLayout = async ({ children }: RootLayoutProps) => {
  const navigationLinks: NavigationLinkProps[] = [
    {
      name: 'Home',
      href: '/',
      icon: 'HomeIcon',
    },
    {
      name: 'Locations',
      href: '/locations',
      icon: 'CompanyIcon',
    },
    {
      name: 'Tariffs',
      href: '/tariffs',
      icon: 'MoneyIcon',
    },
  ];

  return (
    <html lang="en" className={ttCommonsPro.className}>
      <body>
        <SidebarLayout
          showNavigationBottom={false}
          navigationLinks={navigationLinks}
        >
          {children}
        </SidebarLayout>
      </body>
    </html>
  );
};

export default RootLayout;
