import './tailwind.css';
import { ttCommonsPro } from './font';
import React from 'react';

export const metadata = {
  icons: {
    icon: '/icon.ico',
  },
  title: 'Welcome',
  description: 'Welcome to Pod Point',
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface RootLayoutProps {
  children: React.ReactNode;
}

const RootLayout = async ({ children }: RootLayoutProps) => (
  <html lang="en" className={ttCommonsPro.className}>
    <body>{children}</body>
  </html>
);

export default RootLayout;
