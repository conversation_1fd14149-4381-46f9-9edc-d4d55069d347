{"name": "internal-site-admin-api-msw", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/commercial/internal/site-admin-api-msw/src", "projectType": "application", "tags": ["commercial", "site-admin"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/commercial/internal/site-admin-api-msw", "main": "apps/commercial/internal/site-admin-api-msw/src/node.ts", "tsConfig": "apps/commercial/internal/site-admin-api-msw/tsconfig.app.json", "webpackConfig": "apps/commercial/internal/site-admin-api-msw/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "internal-site-admin-api-msw:build"}, "configurations": {"development": {"buildTarget": "internal-site-admin-api-msw:build:development"}, "production": {"buildTarget": "internal-site-admin-api-msw:build:production"}}}, "generate-mocks": {"executor": "nx:run-commands", "options": {"commands": [{"description": "Generate mocks using configuration file.", "command": "start-server-and-test 'nx run internal-site-admin-api:serve' http-get://localhost:4102/api-json 'npx msw-auto-mock http://localhost:4102/api-json -o apps/commercial/internal/site-admin-api-msw/src'", "forwardAllArgs": false}, {"description": "Convert handlers.js to typescript", "command": "mv apps/commercial/internal/site-admin-api-msw/src/handlers.js apps/commercial/internal/site-admin-api-msw/src/handlers.ts", "forwardAllArgs": false}, {"description": "Convert and rename node.js to main.ts", "command": "mv apps/commercial/internal/site-admin-api-msw/src/node.js apps/commercial/internal/site-admin-api-msw/src/node.ts", "forwardAllArgs": false}, {"description": "Remove any 503 responses from mocks.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' '/ctx.status(503)/d' {} \\;", "forwardAllArgs": false}, {"description": "Fix any 204 responses in mocks.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/null,\\ {\\ status:\\ 204\\ }/undefined,\\ {\\ status:\\ 204\\ }/g' {} \\;", "forwardAllArgs": false}, {"description": "Remove randomisation of supportsConfirmCharge field.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/supportsConfirmCharge:\\ faker.datatype.boolean()/supportsConfirmCharge:\\ true/g' {} \\;", "forwardAllArgs": false}, {"description": "Remove randomisation of supportsEnergyTariff field.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/supportsEnergyTariff:\\ faker.datatype.boolean()/supportsEnergyTariff:\\ true/g' {} \\;", "forwardAllArgs": false}, {"description": "Remove randomisation of supportsOcpp field.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/supportsOcpp:\\ faker.datatype.boolean()/supportsOcpp:\\ false/g' {} \\;", "forwardAllArgs": false}, {"description": "Remove randomisation of supportsPerKwh field.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/supportsPerKwh:\\ faker.datatype.boolean()/supportsPerKwh:\\ true/g' {} \\;", "forwardAllArgs": false}, {"description": "Remove randomisation of supportsTariffs field.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/supportsTariffs:\\ faker.datatype.boolean()/supportsTariffs:\\ true/g' {} \\;", "forwardAllArgs": false}, {"description": "Ensure start day is a realistic value.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/startDay:\\ faker.number.int()/startDay:\\ faker.number.int({\\ min:\\ 0,\\ max:\\ 6\\ })/g' {} \\;", "forwardAllArgs": false}, {"description": "Ensure end day is a realistic value.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/endDay:\\ faker.number.int()/endDay:\\ faker.number.int({\\ min:\\ 0,\\ max:\\ 6\\ })/g' {} \\;", "forwardAllArgs": false}, {"description": "Ensure start time is a realistic value.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/startTime:\\ faker.lorem.slug(1)/startTime:\\ \"00:00:00\"/g' {} \\;", "forwardAllArgs": false}, {"description": "Ensure end time is a realistic value.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/endTime:\\ faker.lorem.slug(1)/endTime:\\ \"00:00:00\"/g' {} \\;", "forwardAllArgs": false}, {"description": "Ensure readOnly is set to false.", "command": "find apps/commercial/internal/site-admin-api-msw/src/handlers.ts -type f -name '*' -exec sed -i '' 's/readOnly:\\ faker.datatype.boolean()/readOnly:\\ false/g' {} \\;", "forwardAllArgs": false}, {"description": "<PERSON><PERSON> generated mocks.", "command": "npx nx lint internal-site-admin-api-msw --fix", "forwardAllArgs": false}, {"description": "Format generated mocks.", "command": "npx prettier apps/commercial/internal/site-admin-api-msw --write", "forwardAllArgs": false}], "parallel": false}, "outputs": ["{projectRoot}/src/models"]}}}