import { NextApiRequest, NextApiResponse } from 'next';
import { authOptions } from '../../auth/[...nextauth]';
import { handleNextApiRequest } from '@experience/commercial/next/api-request-utils';

const BASE_URL = process.env.SITE_ADMIN_API_URL;

const handler = async (req: NextApiRequest, res: NextApiResponse) =>
  handleNextApiRequest(
    `${BASE_URL}/expenses/stats/monthly`,
    req,
    res,
    authOptions
  );

export default handler;
