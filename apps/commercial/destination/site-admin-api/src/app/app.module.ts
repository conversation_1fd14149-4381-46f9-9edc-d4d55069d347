import { AdminModule } from '@experience/commercial/site-admin/nest/admin-module';
import { BillingModule } from '@experience/commercial/site-admin/nest/billing-module';
import { ChargeModule } from '@experience/commercial/site-admin/nest/charge-module';
import { ClsModule } from 'nestjs-cls';
import { Command, CommandRunner } from 'nest-commander';
import { DomainModule } from '@experience/commercial/site-admin/nest/domain-module';
import { DriverModule } from '@experience/commercial/site-admin/nest/driver-module';
import { ExpenseModule } from '@experience/commercial/site-admin/nest/expense-module';
import { HealthModule } from '../health/health.module';
import { InsightsModule } from '@experience/commercial/site-admin/nest/insights-module';
import { LoggerModule } from 'nestjs-pino';
import { Module } from '@nestjs/common';
import { PINO_LOGGER_OPTIONS, bootstrap } from '@experience/shared/nest/utils';
import { PodModule } from '@experience/commercial/site-admin/nest/pod-module';
import { SiteModule } from '@experience/commercial/site-admin/nest/site-module';
import { TariffModule } from '@experience/commercial/site-admin/nest/tariff-module';
import { UserModule } from '@experience/commercial/site-admin/nest/user-module';
import { VersionModule } from '@experience/shared/nest/version-module';

@Module({
  imports: [
    ClsModule.forRoot({ global: true, middleware: { mount: true } }),
    LoggerModule.forRoot(PINO_LOGGER_OPTIONS),
    AdminModule,
    BillingModule,
    ChargeModule,
    DomainModule,
    DriverModule,
    ExpenseModule,
    HealthModule,
    InsightsModule,
    PodModule,
    SiteModule,
    TariffModule,
    UserModule,
    VersionModule,
  ],
})
@Command({ name: 'default', options: { hidden: true, isDefault: true } })
export class AppModule extends CommandRunner {
  async run(): Promise<void> {
    await bootstrap({
      module: AppModule,
      port: 4202,
    });
  }
}
