import { startOpenTelemetryWithXRayPropagator } from '@experience/shared/nest/utils';
startOpenTelemetryWithXRayPropagator('site-admin-api');

import * as Sentry from '@sentry/node';
import { AppModule } from './app/app.module';
import { bootstrapCommandRunner } from '@experience/shared/nest/utils';
import axios from 'axios';

Sentry.init({
  dsn: 'https://<EMAIL>/4505249329184768',
  enabled: ['dev', 'stage', 'prod'].includes(process.env.ENVIRONMENT),
  environment: process.env.ENVIRONMENT,
  ignoreTransactions: ['/health'],
  release: process.env.SENTRY_RELEASE,
  tracesSampleRate: 0.001,
});

axios.defaults.headers.common['User-Agent'] =
  'commercial/destination-site-admin-api';

void bootstrapCommandRunner(AppModule);
