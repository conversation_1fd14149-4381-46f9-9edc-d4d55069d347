import { ChargerFeesPage } from '@experience/commercial/statement-service/next/chargers-pages';
import {
  ChargerWithPod,
  STATEMENT_SERVICE_API_URL,
} from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

export const metadata = {
  title: 'Commercial Statement Service - Charger Fees',
  description: 'Commercial Statement Service charger fees page',
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const ChargerFees = async (props: {
  params: Promise<{ groupId: string; siteId: string }>;
}) => {
  const params = await props.params;
  const chargers = await appRequestHandler<ChargerWithPod[]>(
    `${STATEMENT_SERVICE_API_URL}/sites/${params.siteId}/chargers`
  );

  return (
    <ChargerFeesPage
      chargers={chargers}
      groupId={params.groupId}
      siteId={params.siteId}
    />
  );
};

export default ChargerFees;
