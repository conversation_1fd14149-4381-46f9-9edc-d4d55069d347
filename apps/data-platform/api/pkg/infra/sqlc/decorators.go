package sqlc

import (
	"experience/apps/data-platform/api/pkg/stats/models"
	"strconv"
)

func (s *RetrieveSubmittedChargesByOrganisationAndDriverRow) ChargeDuration() int64 {
	if s.KwhUsed == "0.00" {
		return 0
	}
	if s.Duration.Int64 > 0 {
		return s.Duration.Int64
	}
	duration := s.EndsAt.Time.Sub(s.StartsAt.Time)
	return duration.Milliseconds() / 1000
}

func (s *RetrieveSubmittedChargesByOrganisationAndDriverRow) LocationType() string {
	if s.IsHome == 1 {
		return "home"
	}
	return "public"
}

func (s *RetrieveSiteStatisticsRow) ChargeEnergySummary() (models.ChargeEnergySummary, error) {
	return chargeEnergySummary(s.TotalEnergyUsage, s.ClaimedEnergyUsage, s.RevenueGeneratingClaimedEnergyUsage, s.UnclaimedEnergyUsage, s.EnergyCost)
}

func (s *RetrieveGroupStatisticsRow) ChargeEnergySummary() (models.ChargeEnergySummary, error) {
	return chargeEnergySummary(s.TotalEnergyUsage, s.ClaimedEnergyUsage, s.RevenueGeneratingClaimedEnergyUsage, s.UnclaimedEnergyUsage, s.EnergyCost)
}

func (s *RetrieveChargerStatisticsRow) ChargeEnergySummary() (models.ChargeEnergySummary, error) {
	return chargeEnergySummary(s.TotalEnergyUsage, s.ClaimedEnergyUsage, s.RevenueGeneratingClaimedEnergyUsage, s.UnclaimedEnergyUsage, s.EnergyCost)
}

func chargeEnergySummary(total, claimed, revenueGeneratingClaimed, unclaimed string, cost int32) (models.ChargeEnergySummary, error) {
	totalUsageFloat, parseErr := strconv.ParseFloat(total, 64)
	result := models.ChargeEnergySummary{}
	if parseErr != nil {
		return result, parseErr
	}
	claimedUsageFloat, parseErr := strconv.ParseFloat(claimed, 64)
	if parseErr != nil {
		return result, parseErr
	}
	revenueGeneratingClaimedFloat, parseErr := strconv.ParseFloat(revenueGeneratingClaimed, 64)
	if parseErr != nil {
		return result, parseErr
	}
	unclaimedUsageFloat, parseErr := strconv.ParseFloat(unclaimed, 64)
	if parseErr != nil {
		return result, parseErr
	}

	return models.ChargeEnergySummary{
		TotalUsage:                    totalUsageFloat,
		ClaimedUsage:                  claimedUsageFloat,
		RevenueGeneratingClaimedUsage: revenueGeneratingClaimedFloat,
		UnclaimedUsage:                unclaimedUsageFloat,
		Cost:                          int(cost),
	}, nil
}
