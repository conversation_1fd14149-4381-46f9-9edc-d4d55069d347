// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: commercial.sql

package sqlc

import (
	"context"
	"database/sql"
	"time"

	"github.com/lib/pq"
)

const createSubmittedChargesForOrganisation = `-- name: CreateSubmittedChargesForOrganisation :many
INSERT INTO commercial.submitted_charges (organisation_id, driver_id, charge_id, submitted_at, created_by, created_at,
                   status)
VALUES (UNNEST($1::bigint[]),
        UNNEST($2::bigint[]),
        UNNEST($3::bigint[]),
        UNNEST($4::timestamptz[]),
        UNNEST($5::text[]),
        UNNEST($6::timestamptz[]),
        UNNEST($7::submitted_charge_status[]))
RETURNING id, organisation_id, driver_id, charge_id, submitted_at, created_by, created_at, status, processed_by, processed_at
`

type CreateSubmittedChargesForOrganisationParams struct {
	OrganisationIds []int64                 `json:"organisation_ids"`
	DriverIds       []int64                 `json:"driver_ids"`
	ChargeIds       []int64                 `json:"charge_ids"`
	SubmittedAts    []time.Time             `json:"submitted_ats"`
	CreatedBys      []string                `json:"created_bys"`
	CreatedAts      []time.Time             `json:"created_ats"`
	Statuses        []SubmittedChargeStatus `json:"statuses"`
}

func (q *Queries) CreateSubmittedChargesForOrganisation(ctx context.Context, arg CreateSubmittedChargesForOrganisationParams) ([]CommercialSubmittedCharge, error) {
	rows, err := q.db.QueryContext(ctx, createSubmittedChargesForOrganisation,
		pq.Array(arg.OrganisationIds),
		pq.Array(arg.DriverIds),
		pq.Array(arg.ChargeIds),
		pq.Array(arg.SubmittedAts),
		pq.Array(arg.CreatedBys),
		pq.Array(arg.CreatedAts),
		pq.Array(arg.Statuses),
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CommercialSubmittedCharge
	for rows.Next() {
		var i CommercialSubmittedCharge
		if err := rows.Scan(
			&i.ID,
			&i.OrganisationID,
			&i.DriverID,
			&i.ChargeID,
			&i.SubmittedAt,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.Status,
			&i.ProcessedBy,
			&i.ProcessedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveFleetUsage = `-- name: RetrieveFleetUsage :one
WITH base AS (SELECT COUNT(c.id)                           AS total_charges,
                     COALESCE(SUM(c.kwh_used), 0)::numeric AS kwh_used,
                     COUNT(DISTINCT (sc.driver_id))        AS number_of_drivers,
                     COALESCE(SUM(CASE
                                    WHEN pl.is_home = 1 THEN c.kwh_used
                       END), 0)::numeric                   AS kwh_used_home,
                     COALESCE(SUM(CASE
                                    WHEN pl.is_home = 0 THEN c.kwh_used
                       END), 0)::numeric                   AS kwh_used_public,
                     COUNT(CASE
                             WHEN pl.is_home = 1 THEN c.id
                       END)::bigint                        AS total_charges_home,
                     COUNT(CASE
                             WHEN pl.is_home = 0 THEN c.id
                       END)::bigint                        AS total_charges_public
              FROM commercial.submitted_charges sc
                     INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
                     INNER JOIN podpoint.charges c ON c.id = sc.charge_id
                     INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
                     INNER JOIN podpoint.users u ON u.id = sc.driver_id
                     INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
                     INNER JOIN podpoint.pod_addresses pa ON pa.id = pl.address_id
              WHERE g.uid = $1
                AND c.ends_at > (SELECT (DATE_TRUNC('month', NOW()))))
SELECT total_charges, kwh_used, number_of_drivers, kwh_used_home, kwh_used_public, total_charges_home, total_charges_public
FROM base
WHERE EXISTS(SELECT id FROM podpoint.groups g WHERE g.uid = $1)
`

type RetrieveFleetUsageRow struct {
	TotalCharges       int64  `json:"total_charges"`
	KwhUsed            string `json:"kwh_used"`
	NumberOfDrivers    int64  `json:"number_of_drivers"`
	KwhUsedHome        string `json:"kwh_used_home"`
	KwhUsedPublic      string `json:"kwh_used_public"`
	TotalChargesHome   int64  `json:"total_charges_home"`
	TotalChargesPublic int64  `json:"total_charges_public"`
}

func (q *Queries) RetrieveFleetUsage(ctx context.Context, uid string) (RetrieveFleetUsageRow, error) {
	row := q.db.QueryRowContext(ctx, retrieveFleetUsage, uid)
	var i RetrieveFleetUsageRow
	err := row.Scan(
		&i.TotalCharges,
		&i.KwhUsed,
		&i.NumberOfDrivers,
		&i.KwhUsedHome,
		&i.KwhUsedPublic,
		&i.TotalChargesHome,
		&i.TotalChargesPublic,
	)
	return i, err
}

const retrieveSubmittedChargesByOrganisation = `-- name: RetrieveSubmittedChargesByOrganisation :many
SELECT sc.id,
       sc.driver_id,
       sc.organisation_id,
       sc.status,
       u.email,
       u.first_name,
       u.last_name,
       c.id                                                                                 AS charge_id,
       c.kwh_used,
       COALESCE(CASE WHEN pl.is_home = 1 THEN c.energy_cost ELSE ABS(be.presentment_amount) END,
                0)::integer                                                                 AS energy_cost,
       pl.is_home,
       c.starts_at,
       c.ends_at,
       COALESCE(CASE WHEN pl.is_home = 1 THEN pu.ppid ELSE pu.name END, 'UNKNOWN')::varchar AS unit_name,
       c.location_id,
       pa.line_1                                                                            AS address_line_1,
       pa.line_2                                                                            AS address_line_2,
       pa.postal_town,
       pa.postcode,
       pa.country,
       sc.processed_by,
       sc.processed_at,
       sc.submitted_at,
       pru.first_name                                                                       AS processed_by_first_name,
       pru.last_name                                                                        AS processed_by_last_name
FROM podpoint.charges c
       INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
       INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
       INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
       INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
       INNER JOIN podpoint.pod_addresses pa ON pa.id = pl.address_id
       INNER JOIN podpoint.users u ON u.id = sc.driver_id
       LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
       LEFT JOIN podpoint.users pru ON sc.processed_by = pru.id
WHERE g.uid = $1
  AND c.is_closed = 1
  AND submitted_at >= $2::timestamptz
  AND submitted_at < $3::timestamptz
ORDER BY c.starts_at DESC
`

type RetrieveSubmittedChargesByOrganisationParams struct {
	Uid      string    `json:"uid"`
	FromDate time.Time `json:"from_date"`
	ToDate   time.Time `json:"to_date"`
}

type RetrieveSubmittedChargesByOrganisationRow struct {
	ID                   int64                 `json:"id"`
	DriverID             int64                 `json:"driver_id"`
	OrganisationID       int64                 `json:"organisation_id"`
	Status               SubmittedChargeStatus `json:"status"`
	Email                string                `json:"email"`
	FirstName            string                `json:"first_name"`
	LastName             string                `json:"last_name"`
	ChargeID             int64                 `json:"charge_id"`
	KwhUsed              string                `json:"kwh_used"`
	EnergyCost           int32                 `json:"energy_cost"`
	IsHome               int16                 `json:"is_home"`
	StartsAt             sql.NullTime          `json:"starts_at"`
	EndsAt               sql.NullTime          `json:"ends_at"`
	UnitName             string                `json:"unit_name"`
	LocationID           sql.NullInt64         `json:"location_id"`
	AddressLine1         string                `json:"address_line_1"`
	AddressLine2         string                `json:"address_line_2"`
	PostalTown           string                `json:"postal_town"`
	Postcode             string                `json:"postcode"`
	Country              string                `json:"country"`
	ProcessedBy          sql.NullInt64         `json:"processed_by"`
	ProcessedAt          sql.NullTime          `json:"processed_at"`
	SubmittedAt          time.Time             `json:"submitted_at"`
	ProcessedByFirstName sql.NullString        `json:"processed_by_first_name"`
	ProcessedByLastName  sql.NullString        `json:"processed_by_last_name"`
}

func (q *Queries) RetrieveSubmittedChargesByOrganisation(ctx context.Context, arg RetrieveSubmittedChargesByOrganisationParams) ([]RetrieveSubmittedChargesByOrganisationRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveSubmittedChargesByOrganisation, arg.Uid, arg.FromDate, arg.ToDate)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveSubmittedChargesByOrganisationRow
	for rows.Next() {
		var i RetrieveSubmittedChargesByOrganisationRow
		if err := rows.Scan(
			&i.ID,
			&i.DriverID,
			&i.OrganisationID,
			&i.Status,
			&i.Email,
			&i.FirstName,
			&i.LastName,
			&i.ChargeID,
			&i.KwhUsed,
			&i.EnergyCost,
			&i.IsHome,
			&i.StartsAt,
			&i.EndsAt,
			&i.UnitName,
			&i.LocationID,
			&i.AddressLine1,
			&i.AddressLine2,
			&i.PostalTown,
			&i.Postcode,
			&i.Country,
			&i.ProcessedBy,
			&i.ProcessedAt,
			&i.SubmittedAt,
			&i.ProcessedByFirstName,
			&i.ProcessedByLastName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveSubmittedChargesByOrganisationAndDriver = `-- name: RetrieveSubmittedChargesByOrganisationAndDriver :many
SELECT sc.id                                                                                AS submitted_charge_id,
       sc.status,
       sc.submitted_at,
       c.id                                                                                 AS charge_id,
       c.starts_at,
       c.ends_at,
       c.duration,
       COALESCE(CASE WHEN pl.is_home = 1 THEN c.energy_cost ELSE ABS(be.presentment_amount) END,
                0)::integer                                                                 AS energy_cost,
       c.kwh_used,
       COALESCE(CASE WHEN pl.is_home = 1 THEN pu.ppid ELSE pu.name END, 'UNKNOWN')::varchar AS unit_name,
       pl.is_home,
       sc.processed_at,
       CASE WHEN sc.status = 'NEW' THEN '' ELSE us.first_name END::varchar                  AS processed_by_first_name,
       CASE WHEN sc.status = 'NEW' THEN '' ELSE us.last_name END::varchar                   AS processed_by_last_name,
       pl.id                                                                                AS location_id,
       pa.line_1                                                                            AS address_line_1,
       pa.line_2                                                                            AS address_line_2,
       pa.postal_town,
       pa.postcode,
       pa.country
FROM podpoint.charges c
       INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
       INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
       LEFT OUTER JOIN podpoint.users us ON us.id = sc.processed_by
       INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
       INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
       INNER JOIN podpoint.pod_addresses pa ON pl.address_id = pa.id
       LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
WHERE g.uid = $1
  AND sc.driver_id = $2
ORDER BY c.starts_at DESC
`

type RetrieveSubmittedChargesByOrganisationAndDriverParams struct {
	Uid      string `json:"uid"`
	DriverID int64  `json:"driver_id"`
}

type RetrieveSubmittedChargesByOrganisationAndDriverRow struct {
	SubmittedChargeID    int64                 `json:"submitted_charge_id"`
	Status               SubmittedChargeStatus `json:"status"`
	SubmittedAt          time.Time             `json:"submitted_at"`
	ChargeID             int64                 `json:"charge_id"`
	StartsAt             sql.NullTime          `json:"starts_at"`
	EndsAt               sql.NullTime          `json:"ends_at"`
	Duration             sql.NullInt64         `json:"duration"`
	EnergyCost           int32                 `json:"energy_cost"`
	KwhUsed              string                `json:"kwh_used"`
	UnitName             string                `json:"unit_name"`
	IsHome               int16                 `json:"is_home"`
	ProcessedAt          sql.NullTime          `json:"processed_at"`
	ProcessedByFirstName string                `json:"processed_by_first_name"`
	ProcessedByLastName  string                `json:"processed_by_last_name"`
	LocationID           int64                 `json:"location_id"`
	AddressLine1         string                `json:"address_line_1"`
	AddressLine2         string                `json:"address_line_2"`
	PostalTown           string                `json:"postal_town"`
	Postcode             string                `json:"postcode"`
	Country              string                `json:"country"`
}

func (q *Queries) RetrieveSubmittedChargesByOrganisationAndDriver(ctx context.Context, arg RetrieveSubmittedChargesByOrganisationAndDriverParams) ([]RetrieveSubmittedChargesByOrganisationAndDriverRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveSubmittedChargesByOrganisationAndDriver, arg.Uid, arg.DriverID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveSubmittedChargesByOrganisationAndDriverRow
	for rows.Next() {
		var i RetrieveSubmittedChargesByOrganisationAndDriverRow
		if err := rows.Scan(
			&i.SubmittedChargeID,
			&i.Status,
			&i.SubmittedAt,
			&i.ChargeID,
			&i.StartsAt,
			&i.EndsAt,
			&i.Duration,
			&i.EnergyCost,
			&i.KwhUsed,
			&i.UnitName,
			&i.IsHome,
			&i.ProcessedAt,
			&i.ProcessedByFirstName,
			&i.ProcessedByLastName,
			&i.LocationID,
			&i.AddressLine1,
			&i.AddressLine2,
			&i.PostalTown,
			&i.Postcode,
			&i.Country,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveSubmittedChargesByOrganisationAndStatus = `-- name: RetrieveSubmittedChargesByOrganisationAndStatus :many
SELECT sc.id,
       sc.driver_id,
       sc.organisation_id,
       sc.status,
       u.email,
       u.first_name,
       u.last_name,
       c.id                                                                                 AS charge_id,
       c.kwh_used,
       COALESCE(CASE WHEN pl.is_home = 1 THEN c.energy_cost ELSE ABS(be.presentment_amount) END,
                0)::integer                                                                 AS energy_cost,
       pl.is_home,
       c.starts_at,
       c.ends_at,
       COALESCE(CASE WHEN pl.is_home = 1 THEN pu.ppid ELSE pu.name END, 'UNKNOWN')::varchar AS unit_name,
       c.location_id,
       pa.line_1                                                                            AS address_line_1,
       pa.line_2                                                                            AS address_line_2,
       pa.postal_town,
       pa.postcode,
       pa.country,
       sc.processed_by,
       sc.processed_at,
       sc.submitted_at,
       pru.first_name                                                                       AS processed_by_first_name,
       pru.last_name                                                                        AS processed_by_last_name
FROM podpoint.charges c
       INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
       INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
       INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
       INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
       INNER JOIN podpoint.pod_addresses pa ON pa.id = pl.address_id
       INNER JOIN podpoint.users u ON u.id = sc.driver_id
       LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
       LEFT JOIN podpoint.users pru ON sc.processed_by = pru.id
WHERE g.uid = $1
  AND sc.status = $2
  AND c.is_closed = 1
  AND submitted_at >= $3::timestamptz
  AND submitted_at < $4::timestamptz
ORDER BY c.starts_at DESC
`

type RetrieveSubmittedChargesByOrganisationAndStatusParams struct {
	Uid      string                `json:"uid"`
	Status   SubmittedChargeStatus `json:"status"`
	FromDate time.Time             `json:"from_date"`
	ToDate   time.Time             `json:"to_date"`
}

type RetrieveSubmittedChargesByOrganisationAndStatusRow struct {
	ID                   int64                 `json:"id"`
	DriverID             int64                 `json:"driver_id"`
	OrganisationID       int64                 `json:"organisation_id"`
	Status               SubmittedChargeStatus `json:"status"`
	Email                string                `json:"email"`
	FirstName            string                `json:"first_name"`
	LastName             string                `json:"last_name"`
	ChargeID             int64                 `json:"charge_id"`
	KwhUsed              string                `json:"kwh_used"`
	EnergyCost           int32                 `json:"energy_cost"`
	IsHome               int16                 `json:"is_home"`
	StartsAt             sql.NullTime          `json:"starts_at"`
	EndsAt               sql.NullTime          `json:"ends_at"`
	UnitName             string                `json:"unit_name"`
	LocationID           sql.NullInt64         `json:"location_id"`
	AddressLine1         string                `json:"address_line_1"`
	AddressLine2         string                `json:"address_line_2"`
	PostalTown           string                `json:"postal_town"`
	Postcode             string                `json:"postcode"`
	Country              string                `json:"country"`
	ProcessedBy          sql.NullInt64         `json:"processed_by"`
	ProcessedAt          sql.NullTime          `json:"processed_at"`
	SubmittedAt          time.Time             `json:"submitted_at"`
	ProcessedByFirstName sql.NullString        `json:"processed_by_first_name"`
	ProcessedByLastName  sql.NullString        `json:"processed_by_last_name"`
}

func (q *Queries) RetrieveSubmittedChargesByOrganisationAndStatus(ctx context.Context, arg RetrieveSubmittedChargesByOrganisationAndStatusParams) ([]RetrieveSubmittedChargesByOrganisationAndStatusRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveSubmittedChargesByOrganisationAndStatus,
		arg.Uid,
		arg.Status,
		arg.FromDate,
		arg.ToDate,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveSubmittedChargesByOrganisationAndStatusRow
	for rows.Next() {
		var i RetrieveSubmittedChargesByOrganisationAndStatusRow
		if err := rows.Scan(
			&i.ID,
			&i.DriverID,
			&i.OrganisationID,
			&i.Status,
			&i.Email,
			&i.FirstName,
			&i.LastName,
			&i.ChargeID,
			&i.KwhUsed,
			&i.EnergyCost,
			&i.IsHome,
			&i.StartsAt,
			&i.EndsAt,
			&i.UnitName,
			&i.LocationID,
			&i.AddressLine1,
			&i.AddressLine2,
			&i.PostalTown,
			&i.Postcode,
			&i.Country,
			&i.ProcessedBy,
			&i.ProcessedAt,
			&i.SubmittedAt,
			&i.ProcessedByFirstName,
			&i.ProcessedByLastName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveSubmittedChargesByOrganisationAndStatusPerDriver = `-- name: RetrieveSubmittedChargesByOrganisationAndStatusPerDriver :many
SELECT q.driver_id,
       q.email,
       q.first_name,
       q.last_name,
       SUM(q.total_charges)          AS total_charges,
       ARRAY(SELECT id
             FROM commercial.submitted_charges sc
             WHERE sc.driver_id = q.driver_id
               AND sc.organisation_id = q.organisation_id
               AND sc.status = $2
               AND submitted_at >= $3::timestamptz
               AND submitted_at < $4::timestamptz
             ORDER BY id)::bigint[]  AS charge_ids,
       SUM(kwh_used_home)::numeric   AS kwh_home,
       SUM(kwh_used_public)::numeric AS kwh_public,
       SUM(energy_cost_home)         AS cost_home,
       SUM(energy_cost_public)       AS cost_public
FROM (SELECT sc.driver_id,
             sc.organisation_id,
             u.email,
             u.first_name,
             u.last_name,
             COUNT(sc.id)                             AS total_charges,
             SUM(c.kwh_used)                          AS kwh_used_home,
             0.0                                      AS kwh_used_public,
             COALESCE(SUM(c.energy_cost)::integer, 0) AS energy_cost_home,
             0                                        AS energy_cost_public
      FROM podpoint.charges c
             INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
             INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
             INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
             INNER JOIN podpoint.users u ON u.id = sc.driver_id
      WHERE g.uid = $1
        AND sc.status = $2
        AND pl.is_home = 1
        AND c.is_closed = 1
        AND sc.submitted_at >= $3::timestamptz
        AND sc.submitted_at < $4::timestamptz
      GROUP BY sc.driver_id, sc.organisation_id, u.email, u.first_name, u.last_name

      UNION

      SELECT sc.driver_id,
             sc.organisation_id,
             u.email,
             u.first_name,
             u.last_name,
             COUNT(sc.id)                                          AS total_charges,
             0.0                                                   AS kwh_used_home,
             SUM(c.kwh_used)                                       AS kwh_used_public,
             0                                                     AS energy_cost_home,
             COALESCE(SUM(ABS(be.presentment_amount))::integer, 0) AS energy_cost_public
      FROM podpoint.charges c
             INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
             INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
             INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
             INNER JOIN podpoint.users u ON u.id = sc.driver_id
             LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
      WHERE g.uid = $1
        AND sc.status = $2
        AND pl.is_home = 0
        AND c.is_closed = 1
        AND sc.submitted_at >= $3::timestamptz
        AND sc.submitted_at < $4::timestamptz
      GROUP BY sc.driver_id, sc.organisation_id, u.email, u.first_name, u.last_name

      ) AS q
GROUP BY q.driver_id, q.organisation_id, q.email, q.first_name, q.last_name
ORDER BY q.last_name, q.first_name
`

type RetrieveSubmittedChargesByOrganisationAndStatusPerDriverParams struct {
	Uid      string                `json:"uid"`
	Status   SubmittedChargeStatus `json:"status"`
	FromDate time.Time             `json:"from_date"`
	ToDate   time.Time             `json:"to_date"`
}

type RetrieveSubmittedChargesByOrganisationAndStatusPerDriverRow struct {
	DriverID     int64   `json:"driver_id"`
	Email        string  `json:"email"`
	FirstName    string  `json:"first_name"`
	LastName     string  `json:"last_name"`
	TotalCharges int64   `json:"total_charges"`
	ChargeIds    []int64 `json:"charge_ids"`
	KwhHome      string  `json:"kwh_home"`
	KwhPublic    string  `json:"kwh_public"`
	CostHome     int64   `json:"cost_home"`
	CostPublic   int64   `json:"cost_public"`
}

func (q *Queries) RetrieveSubmittedChargesByOrganisationAndStatusPerDriver(ctx context.Context, arg RetrieveSubmittedChargesByOrganisationAndStatusPerDriverParams) ([]RetrieveSubmittedChargesByOrganisationAndStatusPerDriverRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveSubmittedChargesByOrganisationAndStatusPerDriver,
		arg.Uid,
		arg.Status,
		arg.FromDate,
		arg.ToDate,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveSubmittedChargesByOrganisationAndStatusPerDriverRow
	for rows.Next() {
		var i RetrieveSubmittedChargesByOrganisationAndStatusPerDriverRow
		if err := rows.Scan(
			&i.DriverID,
			&i.Email,
			&i.FirstName,
			&i.LastName,
			&i.TotalCharges,
			pq.Array(&i.ChargeIds),
			&i.KwhHome,
			&i.KwhPublic,
			&i.CostHome,
			&i.CostPublic,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveSubmittedChargesByOrganisationPerDriver = `-- name: RetrieveSubmittedChargesByOrganisationPerDriver :many
SELECT q.driver_id,
       q.email,
       q.first_name,
       q.last_name,
       SUM(q.total_charges)          AS total_charges,
       ARRAY(SELECT id
             FROM commercial.submitted_charges
             WHERE driver_id = q.driver_id
               AND organisation_id = q.organisation_id
               AND submitted_at >= $2::timestamptz
               AND submitted_at < $3::timestamptz
             ORDER BY id)::bigint[]  AS charge_ids,
       SUM(kwh_used_home)::numeric   AS kwh_home,
       SUM(kwh_used_public)::numeric AS kwh_public,
       SUM(energy_cost_home)         AS cost_home,
       SUM(energy_cost_public)       AS cost_public
FROM (SELECT sc.driver_id,
             sc.organisation_id,
             u.email,
             u.first_name,
             u.last_name,
             COUNT(sc.id)                             AS total_charges,
             SUM(c.kwh_used)                          AS kwh_used_home,
             0.0                                      AS kwh_used_public,
             COALESCE(SUM(c.energy_cost)::integer, 0) AS energy_cost_home,
             0                                        AS energy_cost_public
      FROM podpoint.charges c
             INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
             INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
             INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
             INNER JOIN podpoint.users u ON u.id = sc.driver_id
      WHERE g.uid = $1
        AND pl.is_home = 1
        AND c.is_closed = 1
        AND sc.submitted_at >= $2::timestamptz
        AND sc.submitted_at < $3::timestamptz
      GROUP BY sc.driver_id, sc.organisation_id, u.email, u.first_name, u.last_name

      UNION

      SELECT sc.driver_id,
             sc.organisation_id,
             u.email,
             u.first_name,
             u.last_name,
             COUNT(sc.id)                                          AS total_charges,
             0.0                                                   AS kwh_used_home,
             SUM(c.kwh_used)                                       AS kwh_used_public,
             0                                                     AS energy_cost_home,
             COALESCE(SUM(ABS(be.presentment_amount))::integer, 0) AS energy_cost_public
      FROM podpoint.charges c
             INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
             INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
             INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
             INNER JOIN podpoint.users u ON u.id = sc.driver_id
             LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
      WHERE g.uid = $1
        AND pl.is_home = 0
        AND c.is_closed = 1
        AND sc.submitted_at >= $2::timestamptz
        AND sc.submitted_at < $3::timestamptz
      GROUP BY sc.driver_id, sc.organisation_id, u.email, u.first_name, u.last_name

      ) AS q
GROUP BY q.driver_id, q.organisation_id, q.email, q.first_name, q.last_name
ORDER BY q.last_name, q.first_name
`

type RetrieveSubmittedChargesByOrganisationPerDriverParams struct {
	Uid      string    `json:"uid"`
	FromDate time.Time `json:"from_date"`
	ToDate   time.Time `json:"to_date"`
}

type RetrieveSubmittedChargesByOrganisationPerDriverRow struct {
	DriverID     int64   `json:"driver_id"`
	Email        string  `json:"email"`
	FirstName    string  `json:"first_name"`
	LastName     string  `json:"last_name"`
	TotalCharges int64   `json:"total_charges"`
	ChargeIds    []int64 `json:"charge_ids"`
	KwhHome      string  `json:"kwh_home"`
	KwhPublic    string  `json:"kwh_public"`
	CostHome     int64   `json:"cost_home"`
	CostPublic   int64   `json:"cost_public"`
}

func (q *Queries) RetrieveSubmittedChargesByOrganisationPerDriver(ctx context.Context, arg RetrieveSubmittedChargesByOrganisationPerDriverParams) ([]RetrieveSubmittedChargesByOrganisationPerDriverRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveSubmittedChargesByOrganisationPerDriver, arg.Uid, arg.FromDate, arg.ToDate)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveSubmittedChargesByOrganisationPerDriverRow
	for rows.Next() {
		var i RetrieveSubmittedChargesByOrganisationPerDriverRow
		if err := rows.Scan(
			&i.DriverID,
			&i.Email,
			&i.FirstName,
			&i.LastName,
			&i.TotalCharges,
			pq.Array(&i.ChargeIds),
			&i.KwhHome,
			&i.KwhPublic,
			&i.CostHome,
			&i.CostPublic,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveSubmittedChargesSummaryByOrganisationAndDriver = `-- name: RetrieveSubmittedChargesSummaryByOrganisationAndDriver :one
SELECT COALESCE(SUM(CASE WHEN pl.is_home = 1 THEN c.kwh_used END), 0)::numeric                AS kwh_used_home,
       COALESCE(SUM(CASE WHEN pl.is_home = 0 THEN c.kwh_used END), 0)::numeric                AS kwh_used_public,
       COALESCE(SUM(CASE WHEN pl.is_home = 1 THEN c.energy_cost END), 0)::bigint              AS energy_cost_home,
       COALESCE(SUM(CASE WHEN pl.is_home = 0 THEN ABS(be.presentment_amount) END), 0)::bigint AS energy_cost_public
FROM podpoint.charges c
       INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
       INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
       INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
       LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
WHERE g.uid = $1
  AND sc.driver_id = $2
`

type RetrieveSubmittedChargesSummaryByOrganisationAndDriverParams struct {
	Uid      string `json:"uid"`
	DriverID int64  `json:"driver_id"`
}

type RetrieveSubmittedChargesSummaryByOrganisationAndDriverRow struct {
	KwhUsedHome      string `json:"kwh_used_home"`
	KwhUsedPublic    string `json:"kwh_used_public"`
	EnergyCostHome   int64  `json:"energy_cost_home"`
	EnergyCostPublic int64  `json:"energy_cost_public"`
}

func (q *Queries) RetrieveSubmittedChargesSummaryByOrganisationAndDriver(ctx context.Context, arg RetrieveSubmittedChargesSummaryByOrganisationAndDriverParams) (RetrieveSubmittedChargesSummaryByOrganisationAndDriverRow, error) {
	row := q.db.QueryRowContext(ctx, retrieveSubmittedChargesSummaryByOrganisationAndDriver, arg.Uid, arg.DriverID)
	var i RetrieveSubmittedChargesSummaryByOrganisationAndDriverRow
	err := row.Scan(
		&i.KwhUsedHome,
		&i.KwhUsedPublic,
		&i.EnergyCostHome,
		&i.EnergyCostPublic,
	)
	return i, err
}

const retrieveUserById = `-- name: RetrieveUserById :one
SELECT u.id, u.first_name, u.last_name, u.email
FROM podpoint.users u
WHERE u.id = $1
`

type RetrieveUserByIdRow struct {
	ID        int64  `json:"id"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Email     string `json:"email"`
}

func (q *Queries) RetrieveUserById(ctx context.Context, id int64) (RetrieveUserByIdRow, error) {
	row := q.db.QueryRowContext(ctx, retrieveUserById, id)
	var i RetrieveUserByIdRow
	err := row.Scan(
		&i.ID,
		&i.FirstName,
		&i.LastName,
		&i.Email,
	)
	return i, err
}

const setStatusOnSubmittedChargesById = `-- name: SetStatusOnSubmittedChargesById :many
UPDATE commercial.submitted_charges
SET status       = $1,
    processed_by = $2,
    processed_at = $3
WHERE id = ANY ($4::bigint[])
  AND status != $1
RETURNING id, organisation_id, driver_id, charge_id, submitted_at, created_by, created_at, status, processed_by, processed_at
`

type SetStatusOnSubmittedChargesByIdParams struct {
	Status      SubmittedChargeStatus `json:"status"`
	ProcessedBy sql.NullInt64         `json:"processed_by"`
	ProcessedAt sql.NullTime          `json:"processed_at"`
	ID          []int64               `json:"id"`
}

func (q *Queries) SetStatusOnSubmittedChargesById(ctx context.Context, arg SetStatusOnSubmittedChargesByIdParams) ([]CommercialSubmittedCharge, error) {
	rows, err := q.db.QueryContext(ctx, setStatusOnSubmittedChargesById,
		arg.Status,
		arg.ProcessedBy,
		arg.ProcessedAt,
		pq.Array(arg.ID),
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CommercialSubmittedCharge
	for rows.Next() {
		var i CommercialSubmittedCharge
		if err := rows.Scan(
			&i.ID,
			&i.OrganisationID,
			&i.DriverID,
			&i.ChargeID,
			&i.SubmittedAt,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.Status,
			&i.ProcessedBy,
			&i.ProcessedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
