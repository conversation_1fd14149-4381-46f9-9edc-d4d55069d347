package stats

import (
	"context"
	"experience/apps/data-platform/api/pkg/infra/sqlc"
	"experience/apps/data-platform/api/pkg/stats"
	"experience/apps/data-platform/api/pkg/stats/models"
	"experience/libs/shared/go/db"
	"experience/libs/shared/go/db/postgres"
	"time"

	"github.com/jinzhu/copier"
)

type repository struct {
	queriesRead *sqlc.Queries
}

func NewStatsRepository(xdb *postgres.ReadWriteDB) stats.Repository {
	return &repository{
		queriesRead: sqlc.New(xdb.ReadDB),
	}
}

func (r *repository) SiteSummary(ctx context.Context, siteID int, from, to time.Time) (models.SiteSummary, error) {
	params := sqlc.RetrieveSiteStatisticsParams{
		ID:      int64(siteID),
		Column2: from,
		Column3: to.Add(time.Hour * 24),
	}
	var result models.SiteSummary
	summary, err := r.queriesRead.RetrieveSiteStatistics(ctx, params)
	if err != nil {
		return result, err
	}

	result.Energy, err = summary.ChargeEnergySummary()
	if err != nil {
		return result, err
	}

	err = copier.CopyWithOption(&result, &summary, copier.Option{
		IgnoreEmpty: false,
		DeepCopy:    false,
		Converters: []copier.TypeConverter{
			db.StringToFloatConverter,
		},
	})
	if err != nil {
		return result, err
	}
	return result, nil
}

func (r *repository) GroupSummary(ctx context.Context, groupID string, from, to time.Time) (models.GroupSummary, error) {
	params := sqlc.RetrieveGroupStatisticsParams{
		GroupUid: groupID,
		Column1:  from,
		Column2:  to.Add(time.Hour * 24),
	}
	var result models.GroupSummary
	summary, err := r.queriesRead.RetrieveGroupStatistics(ctx, params)
	if err != nil {
		return result, err
	}

	result.Energy, err = summary.ChargeEnergySummary()
	if err != nil {
		return result, err
	}

	err = copier.CopyWithOption(&result, &summary, copier.Option{
		IgnoreEmpty: false,
		DeepCopy:    false,
		Converters: []copier.TypeConverter{
			db.StringToFloatConverter,
		},
	})
	if err != nil {
		return result, err
	}
	return result, nil
}

func (r *repository) ChargerSummary(ctx context.Context, locationID int, from, to time.Time) (models.ChargerSummary, error) {
	params := sqlc.RetrieveChargerStatisticsParams{
		ID:      int64(locationID),
		Column2: from,
		Column3: to.Add(time.Hour * 24),
	}
	var result models.ChargerSummary
	summary, err := r.queriesRead.RetrieveChargerStatistics(ctx, params)
	if err != nil {
		return result, err
	}

	result.Energy, err = summary.ChargeEnergySummary()
	if err != nil {
		return result, err
	}

	err = copier.CopyWithOption(&result, &summary, copier.Option{
		IgnoreEmpty: false,
		DeepCopy:    false,
		Converters: []copier.TypeConverter{
			db.StringToFloatConverter,
		},
	})
	if err != nil {
		return result, err
	}
	return result, nil
}
