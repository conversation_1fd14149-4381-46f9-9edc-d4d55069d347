package config_test

import (
	"encoding/json"
	"experience/apps/data-platform/api/pkg/config"
	"experience/libs/shared/go/db"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"os"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestServiceConfig_Unmarshalling(t *testing.T) {
	expectedMigrateDs := migrate.MigrateDatasource{
		PasswordConfig: db.PasswordConfig{
			Host:     "migrate.db",
			Name:     "migrate_db",
			Password: "secret",
			Port:     1234,
			Username: "postgres",
		},
	}

	expectedPodadminDs := config.PodadminDatasource{
		ReadConfig: config.ReadConfig{
			PasswordConfig: db.PasswordConfig{
				Host:     "podadmin.db",
				Name:     "podpoint",
				Password: "secret2",
				Port:     5678,
				Username: "pod",
			},
		},
		WriteConfig: config.WriteConfig{
			PasswordConfig: db.PasswordConfig{
				Host:     "podadmin.db",
				Name:     "podpoint",
				Password: "secret2",
				Port:     5678,
				Username: "pod",
			},
		},
	}

	expectedServiceDs := db.ServiceDatasource{
		ReadConfig: db.ReadConfig{
			IAMConfig: db.IAMConfig{
				Host:     "service-read.db",
				Name:     "service_db",
				Port:     9876,
				Username: "service_api",
			},
		},
		WriteConfig: db.WriteConfig{
			IAMConfig: db.IAMConfig{
				Host:     "service-write.db",
				Name:     "service_db",
				Port:     9876,
				Username: "service_api",
			},
		},
	}

	expectedSQSConfig := config.SQS{
		ChargeEventsURL: "a_queue",
	}

	expectedOpenExchangeRatesAppID := config.OpenExchangeRatesAppID("dummy_app_id")

	contents, err := os.ReadFile("../test/data/service-config.json")
	require.NoError(t, err)

	var c config.Config
	err = json.Unmarshal(contents, &c)
	require.NoError(t, err)

	require.Equal(t, true, c.IsLocal())
	require.Equal(t, expectedMigrateDs, c.MigrateDatasource)
	require.Equal(t, expectedPodadminDs, c.PodadminDatasource)
	require.Equal(t, expectedServiceDs, c.ServiceDatasource)
	require.Equal(t, expectedSQSConfig, c.SQS)
	require.Equal(t, expectedOpenExchangeRatesAppID, c.OpenExchangeRatesAppID)
}
