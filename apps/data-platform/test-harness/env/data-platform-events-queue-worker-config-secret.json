{"environment": "local", "service_datasource": {"read": {"host": "postgres_data_platform", "port": 5432, "name": "data_platform", "username": "xdp_events_queue_worker"}, "write": {"host": "postgres_data_platform", "port": 5432, "name": "data_platform", "username": "xdp_events_queue_worker"}}, "podadmin_datasource": {"read": {"host": "<PERSON><PERSON><PERSON>", "port": 3306, "name": "podpoint", "username": "user", "password": "password"}, "write": {"host": "<PERSON><PERSON><PERSON>", "port": 3306, "name": "podpoint", "username": "user", "password": "password"}}, "migrate_datasource": {"host": "postgres_data_platform", "port": 5432, "name": "data_platform", "username": "postgres", "password": "secret"}, "sqs": {"charge_events_url": "http://localhost:4566/000000000000/charge-events", "charge_commands_url": "http://localhost:4566/000000000000/charge-commands"}, "openexchangerates_app_id": "dummy_app_id", "smart_charging_service_host": "localhost", "asset_service_api_host": "localhost", "tariffs_api_host": "localhost", "energy_metrics_api_host": "localhost"}