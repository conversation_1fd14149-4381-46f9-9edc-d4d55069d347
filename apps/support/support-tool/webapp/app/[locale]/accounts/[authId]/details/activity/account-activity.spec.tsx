import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { renderWithProviders } from '../../../../test-utils';
import { waitFor } from '@testing-library/react';
import AccountActivity from './account-activity';

jest.mock('@experience/commercial/next/app-request-utils');
const mockHandler = jest.mocked(appRequestHandler);

jest.mock('../../../actions/get-details', () => ({
  getAccountActivity: jest.fn(),
}));

describe('Account activity', () => {
  beforeEach(() => {
    mockHandler.mockResolvedValue([
      {
        timestamp: Date.now(),
        event: 'Password reset',
        details: 'Password reset requested',
        status: 'success',
      },
    ]);
  });

  it('should render correctly', async () => {
    const { baseElement } = renderWithProviders(
      <AccountActivity authId="abc123" />
    );
    await waitFor(() => {
      expect(baseElement).toBeTruthy();
    });
  });

  it('should match snapshot', async () => {
    const { baseElement } = renderWithProviders(
      <AccountActivity authId="abc123" />
    );
    await waitFor(() => {
      expect(baseElement).toMatchSnapshot();
    });
  });
});
