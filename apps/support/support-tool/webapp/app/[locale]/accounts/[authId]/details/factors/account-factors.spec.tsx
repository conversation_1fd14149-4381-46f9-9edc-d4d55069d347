import { renderWithProviders } from '../../../../test-utils';
import { screen, waitFor } from '@testing-library/react';
import AccountFactors from './account-factors';

jest.mock('../../../actions/get-details', () => ({
  getAccountFactors: jest.fn().mockResolvedValue([
    {
      id: '919276fd-bbad-438f-ad48-a2d2c22d0812',
      phoneNumber: '+*************',
      enrollmentTime: 'Fri, 22 Sep 2017 01:49:58 GMT',
    },
  ]),
}));

describe('Accounts factors', () => {
  it('should render correctly', async () => {
    const { baseElement } = renderWithProviders(
      <AccountFactors authId="abc123" />
    );
    await waitFor(() => {
      expect(screen.getByText('Factors')).toBeInTheDocument();
      expect(baseElement).toBeTruthy();
    });
  });

  it('should match snapshot', async () => {
    const { baseElement } = renderWithProviders(
      <AccountFactors authId="abc123" />
    );
    await waitFor(() => {
      expect(screen.getByText('Factors')).toBeInTheDocument();
      expect(baseElement).toMatchSnapshot();
    });
  });
});
