import { Metadata } from 'next';
import { PersistedSubscriptionDTO } from '@experience/mobile/subscriptions-api/axios';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { generatePageMetadata } from '@experience/support/support-tool/next';
import SubscriptionStatusPageContent from './page-content';

export const generateMetadata = async (): Promise<Metadata> =>
  await generatePageMetadata({
    namespace: 'Subscriptions.StatusPage',
  });

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const SubscriptionStatus = async (props: {
  params: Promise<{ subscriptionId: string }>;
}) => {
  const { subscriptionId } = await props.params;
  const subscription = await appRequestHandler<PersistedSubscriptionDTO>(
    `${process.env.SUPPORT_TOOL_API_URL}/subscriptions/${subscriptionId}`
  );
  return <SubscriptionStatusPageContent subscription={subscription} />;
};

export default SubscriptionStatus;
