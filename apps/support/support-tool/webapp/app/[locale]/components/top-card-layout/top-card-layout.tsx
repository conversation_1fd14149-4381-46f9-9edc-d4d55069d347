'use client';

import {
  Anchor,
  <PERSON>LeftIcon,
  Card,
  Heading,
  HeadingSizes,
  Paragraph,
  TextWeight,
} from '@experience/shared/react/design-system';
import { Charger as CommercialAttributes } from '@experience/shared/axios/internal-site-admin-api-client';
import { JSX } from 'react';
import { OidcRoles } from '@experience/shared/typescript/oidc-utils';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { joinStrings } from '@experience/shared/typescript/utils';
import { useRole } from '@experience/shared/react/hooks';
import { useTranslations } from 'next-intl';

interface TopCardHeadingProps {
  heading: string;
}

interface TopCardLayoutProps {
  actions?: JSX.Element;
  commercialAttributes?: CommercialAttributes;
  content?: JSX.Element;
  heading: string | JSX.Element;
  name?: string;
  ppid?: string;
}

export const TopCardHeading = ({ heading }: TopCardHeadingProps) => (
  <Heading.H1 fontSize={HeadingSizes.M} className="font-bold ">
    {heading}
  </Heading.H1>
);

const TopCardLayout = ({
  actions,
  commercialAttributes,
  content,
  heading,
  name,
  ppid,
}: TopCardLayoutProps) => {
  const t = useTranslations('Chargers.Components.TopCardLayout');
  const canViewGroup = useRole(OidcRoles.GROUP_VIEW_ALL);
  const canGenerateCommissioningCertificate = useRole(
    OidcRoles.CHARGER_COMMISSIONING
  );

  return (
    <>
      {ppid ? (
        <Anchor
          className="flex items-center gap-1 mb-2 font-bold max-w-fit"
          href={`/chargers/${ppid}`}
        >
          <ArrowLeftIcon.LIGHT width="w-4 h-4" />
          {t('backLinkLabel')}
        </Anchor>
      ) : null}
      <Card>
        <header>
          <div className="flex">
            {typeof heading === 'string' ? (
              <TopCardHeading heading={heading} />
            ) : (
              heading
            )}
            <div className="ml-auto">{actions ?? null}</div>
          </div>
          {name ? (
            <Paragraph textWeight={TextWeight.Semibold}>{name}</Paragraph>
          ) : null}
          {commercialAttributes ? (
            <>
              <VerticalSpacer />
              {canViewGroup ? (
                <Paragraph className="mb-1">
                  <Anchor
                    isNativeLink={true}
                    href={`${
                      process.env.NEXT_PUBLIC_SMS_BASE_URL ??
                      'https://sites.podenergy.com'
                    }/groups?groupUidOverride=${
                      commercialAttributes.group.uid
                    }`}
                    target="_blank"
                  >
                    {`${t('groupPrefix')} ${commercialAttributes.group.name}`}
                  </Anchor>
                </Paragraph>
              ) : (
                <Paragraph className="mb-1">
                  {`${t('groupPrefix')} ${commercialAttributes.group.name}`}
                </Paragraph>
              )}
              <Paragraph className="mb-1">
                {`${t('sitePrefix')} ${commercialAttributes.site.address.name}`}
                {canGenerateCommissioningCertificate ? (
                  <>
                    {' '}
                    <Anchor
                      isNativeLink={true}
                      href={`/chargers/${commercialAttributes.ppid}/commissioning/certificate`}
                      target="_blank"
                    >
                      ({t('generateCommissioningCertificateLink')})
                    </Anchor>
                  </>
                ) : null}
              </Paragraph>
              <Paragraph className="mb-1">
                {`${t('siteContactPrefix')} ${joinStrings(
                  [
                    commercialAttributes.site.contactDetails.name,
                    commercialAttributes.site.contactDetails.email,
                    commercialAttributes.site.contactDetails.telephone,
                  ],
                  ', '
                )}`}
              </Paragraph>
              <Paragraph className="mb-1">
                {`${t('addressPrefix')} ${
                  commercialAttributes.site.address.prettyPrint
                }`}
              </Paragraph>
              <VerticalSpacer />
            </>
          ) : null}
          {content ? <div>{content}</div> : null}
        </header>
      </Card>
    </>
  );
};

export default TopCardLayout;
