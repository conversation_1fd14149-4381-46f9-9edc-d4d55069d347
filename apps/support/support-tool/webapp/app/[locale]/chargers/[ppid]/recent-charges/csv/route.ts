import { NextRequest } from 'next/server';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export const GET = async (
  _: NextRequest,
  props: {
    params: Promise<{ ppid: string }>;
  }
): Promise<Response> => {
  const params = await props.params;

  return appRequestHandler(
    `${process.env.SUPPORT_TOOL_API_URL}/chargers/${params.ppid}/charges/csv`,
    { method: 'GET' },
    {},
    true
  );
};
