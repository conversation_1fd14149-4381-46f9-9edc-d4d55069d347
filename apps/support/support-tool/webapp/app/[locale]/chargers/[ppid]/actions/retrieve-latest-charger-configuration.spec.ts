import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { retrieveLatestChargerConfiguration } from './retrieve-latest-charger-configuration';
import { revalidatePath } from 'next/cache';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

jest.mock('next/cache');
const mockRevalidatePath = jest.mocked(revalidatePath);

describe('refresh charger configuration action', () => {
  const prevEnv = process.env;

  beforeEach(() => {
    process.env.SUPPORT_TOOL_API_URL = 'http://localhost:7102';
  });

  afterEach(() => {
    process.env = prevEnv;
  });

  it('should retrieve latest charger configuration', async () => {
    const ppid = 'PSL-12345';
    const socket = 'A';

    await retrieveLatestChargerConfiguration(ppid, socket);

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:7102/chargers/${ppid}/configuration/refresh?socket=${socket}`,
      {
        headers: { 'content-type': 'application/json' },
        method: 'POST',
      }
    );
    expect(mockRevalidatePath).toHaveBeenCalledWith(`/chargers/${ppid}`);
  });
});
