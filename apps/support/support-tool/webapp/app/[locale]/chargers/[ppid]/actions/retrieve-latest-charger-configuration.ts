'use server';

import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { revalidatePath } from 'next/cache';

export const retrieveLatestChargerConfiguration = async (
  ppid: string,
  socket: string
): Promise<void> => {
  await appRequestHandler(
    `${process.env.SUPPORT_TOOL_API_URL}/chargers/${ppid}/configuration/refresh?socket=${socket}`,
    {
      headers: { 'content-type': 'application/json' },
      method: 'POST',
    }
  );
  revalidatePath(`/chargers/${ppid}`);
};
