import { GET } from './route';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('download recent charges csv route', () => {
  const prevEnv = process.env;

  beforeEach(() => {
    process.env.SUPPORT_TOOL_API_URL = 'http://localhost:7102';
  });

  afterEach(() => {
    process.env = prevEnv;
  });

  it('should download a csv of recent charges', async () => {
    mockRequestHandler.mockResolvedValueOnce('csv data');

    // @ts-expect-error - testing
    const response = await GET({}, { params: { ppid: 'PG-123456' } });

    expect(response).toEqual('csv data');
    expect(mockRequestHandler).toHaveBeenCalledWith(
      'http://localhost:7102/chargers/PG-123456/charges/csv',
      { method: 'GET' },
      {},
      true
    );
  });
});
