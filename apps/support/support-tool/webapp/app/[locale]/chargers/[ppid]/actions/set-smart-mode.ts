'use server';

import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { revalidatePath } from 'next/cache';
import dayjs from 'dayjs';

export const setSmartModeOn = async (ppid: string) => {
  await appRequestHandler(
    `${process.env.SUPPORT_TOOL_API_URL}/chargers/${ppid}/charge-overrides/smart-mode`,
    {
      method: 'DELETE',
      headers: { 'content-type': 'application/json' },
    }
  );

  revalidatePath(`/chargers/${ppid}`);
};

export const setSmartModeOff = async (ppid: string) => {
  await appRequestHandler(
    `${process.env.SUPPORT_TOOL_API_URL}/chargers/${ppid}/charge-overrides/smart-mode`,
    {
      body: JSON.stringify({
        requestedAt: dayjs().toISOString(),
        endAt: null,
      }),
      method: 'POST',
      headers: { 'content-type': 'application/json' },
    }
  );

  revalidatePath(`/chargers/${ppid}`);
};
