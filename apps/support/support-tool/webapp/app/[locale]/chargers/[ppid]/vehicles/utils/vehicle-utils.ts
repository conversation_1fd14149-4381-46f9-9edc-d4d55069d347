import { ConnectedChargeState } from '@experience/shared/axios/vehicle-api-client';
import {
  VehicleIntentEntryDto,
  VehicleLinkResponseDtoVehicle,
} from '@experience/shared/axios/smart-charging-service-client';

export const formatVehicleMakeAndModel = (
  vehicle?: VehicleLinkResponseDtoVehicle,
  includeRegistration = false
) => {
  if (!vehicle) return '-';

  const { brand, model, modelVariant, vehicleRegistrationPlate } =
    vehicle.vehicleInformation;

  const makeModel = `${brand} ${model}${
    modelVariant ? ` (${modelVariant})` : ''
  }`;

  return includeRegistration && vehicleRegistrationPlate
    ? `${makeModel} - ${vehicleRegistrationPlate}`
    : makeModel;
};

export const formatBatteryPercentage = (
  vehicle?: VehicleLinkResponseDtoVehicle
): string => {
  if (!vehicle) return '-';
  const chargeState = vehicle.chargeState as ConnectedChargeState;
  if (chargeState && chargeState.batteryLevelPercent) {
    return `${chargeState.batteryLevelPercent}%`;
  }
  return '-';
};

export const formatIntentKwhAsPercentage = (
  intent?: VehicleIntentEntryDto,
  vehicle?: VehicleLinkResponseDtoVehicle
) => {
  if (!intent || !vehicle) return '-';
  return `${Math.round(
    (intent.chargeKWh / vehicle.chargeState.batteryCapacity) * 100
  )}%`;
};

export const isENodeConnected = (vehicle?: VehicleLinkResponseDtoVehicle) => {
  if (!vehicle) return false;
  return 'isPluggedIn' in vehicle.chargeState;
};
