import { Anchor, Card } from '@experience/shared/react/design-system';
import { ChargingIntentsTable } from '../../../vehicles/tables/charging-intents-table';
import {
  ConnectedChargeState,
  ExtendedVehicleLinkResponseDto,
} from '@experience/shared/axios/smart-charging-service-client';
import {
  KeyValueTable,
  VerticalSpacer,
  VerticalSpacerSize,
} from '@experience/shared/react/layouts';
import {
  formatVehicleMakeAndModel,
  isENodeConnected,
} from '../../../vehicles/utils/vehicle-utils';
import { useTranslations } from 'next-intl';

export interface VehicleProps {
  ppid?: string;
  vehicleLink?: ExtendedVehicleLinkResponseDto;
}

export const Vehicle = ({ ppid, vehicleLink }: VehicleProps) => {
  const t = useTranslations('Chargers.Components.Cards');

  const getRows = (vehicleLink?: ExtendedVehicleLinkResponseDto) => {
    const defaultRows = [
      {
        key: t('vehicleMakeAndModel'),
        value: formatVehicleMakeAndModel(vehicleLink?.vehicle),
      },
      {
        key: t('vehicleEnodeConnected'),
        value: vehicleLink?.vehicle.enodeVehicleId ? t('yes') : t('no'),
      },
      {
        key: t('vehiclePluggedIn'),
        value: vehicleLink?.isPluggedInToThisCharger ? t('yes') : t('no'),
      },
    ];

    if (!isENodeConnected(vehicleLink?.vehicle)) {
      return defaultRows;
    }
    const chargeState = vehicleLink?.vehicle
      ?.chargeState as ConnectedChargeState;

    return [
      ...defaultRows,
      {
        key: t('vehicleBatteryPercentage'),
        value: chargeState ? `${chargeState.batteryLevelPercent}%` : '-',
      },
      {
        key: t('vehicleChargeLimit'),
        value: `${chargeState.chargeLimitPercent}%`,
      },
    ];
  };

  return (
    <Card>
      <div className="flex justify-between items-baseline">
        <Card.Header>{t('vehicleTitle')}</Card.Header>
        <Anchor href={`/chargers/${ppid}/vehicles`} className="text-right ml-2">
          {t('vehicleViewAllVehicles')}
        </Anchor>
      </div>

      <KeyValueTable caption={t('vehicleTitle')} rows={getRows(vehicleLink)} />

      {!isENodeConnected(vehicleLink?.vehicle) ? (
        <>
          <VerticalSpacer size={VerticalSpacerSize.Small} />
          <ChargingIntentsTable vehicleLink={vehicleLink} />
        </>
      ) : null}
    </Card>
  );
};
