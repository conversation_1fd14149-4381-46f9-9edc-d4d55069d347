// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommandsMenu should match snapshot 1`] = `
<body>
  <div>
    <div
      class="relative inline-block text-left"
      data-headlessui-state=""
    >
      <button
        aria-expanded="false"
        aria-haspopup="menu"
        class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
        data-headlessui-state=""
        id="headlessui-menu-button-:test-id-2"
        type="button"
      >
        <span>
          Commands
        </span>
        <svg
          class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
            />
          </g>
        </svg>
      </button>
    </div>
  </div>
</body>
`;
