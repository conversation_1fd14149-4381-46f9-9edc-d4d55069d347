import 'whatwg-fetch';
import { Tariffs } from './tariffs';
import {
  mockTariffSuppliers,
  mockTariffs,
} from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../../test-utils';
import { screen } from '@testing-library/react';
import { setIn } from 'immutable';

describe('Tariffs', () => {
  const [mockTariff] = mockTariffs.data;
  const [mockSupplier] = mockTariffSuppliers;

  it('should render successfully', () => {
    const { baseElement } = renderWithProviders(
      <Tariffs supplier={mockSupplier} tariff={mockTariff} />
    );
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(
      <Tariffs supplier={mockSupplier} tariff={mockTariff} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render a tariff with one flat rate', () => {
    const tariffWithMultiplePriceBands = setIn(
      mockTariff,
      ['tariffInfo'],
      [
        {
          start: '00:00:00',
          end: '00:00:00',
          price: 0.3,
          days: [
            'MONDAY',
            'TUESDAY',
            'WEDNESDAY',
            'THURSDAY',
            'FRIDAY',
            'SATURDAY',
            'SUNDAY',
          ],
        },
      ]
    );

    renderWithProviders(
      <Tariffs supplier={mockSupplier} tariff={tariffWithMultiplePriceBands} />
    );

    expect(screen.getAllByText('Rate')).toHaveLength(1);
    expect(screen.getAllByText('All day')).toHaveLength(1);
  });

  it('should render a tariff with peak / off-peak rates', () => {
    const tariffWithMultiplePriceBands = setIn(
      mockTariff,
      ['tariffInfo'],
      [
        {
          start: '00:00:00',
          end: '03:00:00',
          price: 0.2,
          days: [
            'MONDAY',
            'TUESDAY',
            'WEDNESDAY',
            'THURSDAY',
            'FRIDAY',
            'SATURDAY',
            'SUNDAY',
          ],
        },
        {
          start: '03:00:00',
          end: '00:00:00',
          price: 0.3,
          days: [
            'MONDAY',
            'TUESDAY',
            'WEDNESDAY',
            'THURSDAY',
            'FRIDAY',
            'SATURDAY',
            'SUNDAY',
          ],
        },
      ]
    );

    renderWithProviders(
      <Tariffs supplier={mockSupplier} tariff={tariffWithMultiplePriceBands} />
    );

    expect(screen.getAllByText('Peak rate')).toHaveLength(1);
    expect(screen.getAllByText('Off-peak rate')).toHaveLength(1);
    expect(screen.getAllByText('Monday - Sunday')).toHaveLength(2);
    expect(screen.getAllByText('00:00 - 03:00')).toHaveLength(1);
    expect(screen.getAllByText('03:00 - 00:00')).toHaveLength(1);
  });
});
