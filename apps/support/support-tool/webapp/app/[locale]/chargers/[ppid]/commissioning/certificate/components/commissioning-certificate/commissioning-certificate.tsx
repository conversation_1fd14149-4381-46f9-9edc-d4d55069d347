import {
  Card,
  Heading,
  HeadingSizes,
  Paragraph,
  TextSize,
  TextWeight,
} from '@experience/shared/react/design-system';
import { CommissioningCertificate } from '@experience/support/support-tool/shared';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

interface CommissioningCertificateProps {
  certificate: CommissioningCertificate;
}

export const CommissioningCertificateComponent = ({
  certificate,
}: CommissioningCertificateProps) => {
  const t = useTranslations('Chargers.CommissioningCertificatePage');

  return (
    <Card className="p-8">
      <div className="flex mb-6 items-center">
        <Heading.H1
          fontSize={HeadingSizes.XL}
          className="text-navy flex-grow text-center print:text-2xl"
        >
          {t('certificateTitle')}
        </Heading.H1>
        <div className="w-[75px] h-[75px] print:w-[50px] print:h-[50px] flex-shrink-0 ml-4 relative">
          <Image src="/icon.ico" alt="logo" fill className="object-contain" />
        </div>
      </div>

      <div className="mb-4">
        <div className="print:print-color-adjust bg-primary text-white p-4 mb-4">
          <div className="flex justify-between items-center">
            <span className="font-semibold">{`${t('siteLabel')} ${
              certificate.siteName
            }`}</span>
            <span className="font-semibold">{`${t('groupLabel')} ${
              certificate.group
            }`}</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 print:grid-cols-2 gap-4 mb-6 items-stretch">
          <div className="h-full">
            <div className="border border-smoke p-3 h-full">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('ppidLabel')}
              </div>
              <div>{certificate.ppids}</div>
            </div>
          </div>
          <div className="h-full">
            <div className="border border-smoke p-3 h-full">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('addressLabel')}
              </div>
              <div>{certificate.address}</div>
            </div>
          </div>
          <div className="h-full col-span-2">
            <div className="border border-smoke p-3 h-full">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('adminEmailLabel')}
              </div>
              <div>{certificate.adminEmailAddress}</div>
            </div>
          </div>
        </div>

        <div className="mb-4">
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2 border-b border-smoke">
              <span>{t('communicatingStatement')}</span>
              <span className="font-semibold text-navy">
                {certificate.connectivityStatusOnline ? t('yes') : t('no')}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-smoke">
              <span>{t('appearOnAppStatement')}</span>
              <span className="font-semibold text-navy">
                {certificate.isPublic ? t('yes') : t('no')}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-smoke">
              <span>{t('authenticatedViaAppStatement')}</span>
              <span className="font-semibold text-navy">
                {certificate.confirmChargeEnabled ? t('yes') : t('no')}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-smoke">
              <span>{t('paygTariffStatement')}</span>
              <span className="font-semibold text-navy">
                {certificate.hasTariff ? t('yes') : t('no')}
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 print:grid-cols-2 gap-4 mb-4 items-stretch">
          <div className="h-full">
            <div className="border border-smoke p-3 h-full">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('warrantyStartDateLabel')}
              </div>
              <div>{certificate.warrantyStartDate}</div>
            </div>
          </div>
          <div className="h-full">
            <div className="border border-smoke p-3 h-full">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('warrantyEndDateLabel')}
              </div>
              <div>{certificate.warrantyEndDate}</div>
            </div>
          </div>
          <div className="h-full">
            <div className="border border-smoke p-3 h-full">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('installedByLabel')}
              </div>
              <div>{certificate.installedBy}</div>
            </div>
          </div>
          <div className="h-full">
            <div className="border border-smoke p-3 h-full">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('installerCompanyLabel')}
              </div>
              <div>{certificate.company}</div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 space-y-3">
        <Heading.H3 fontSize={HeadingSizes.XS} className="mb-3">
          {t('handoverTitle')}
        </Heading.H3>

        <div className="space-y-2">
          <div className="flex">
            <Paragraph
              textSize={TextSize.Small}
              textWeight={TextWeight.Semibold}
              className="mr-2"
            >
              {t('companyRepresented')}
            </Paragraph>
            <Paragraph textSize={TextSize.Small}>{t('companyName')}</Paragraph>
          </div>

          <div className="flex">
            <Paragraph
              textSize={TextSize.Small}
              textWeight={TextWeight.Semibold}
              className="mr-2"
            >
              {t('addressLabel')}
            </Paragraph>
            <Paragraph textSize={TextSize.Small}>
              {t('companyAddress')}
            </Paragraph>
          </div>

          <div className="flex">
            <Paragraph
              textSize={TextSize.Small}
              textWeight={TextWeight.Semibold}
              className="mr-2"
            >
              {t('contactNumberLabel')}
            </Paragraph>
            <Paragraph textSize={TextSize.Small}>
              {t('contactNumber')}
            </Paragraph>
          </div>

          <div className="flex">
            <Paragraph
              textSize={TextSize.Small}
              textWeight={TextWeight.Semibold}
              className=" mr-2"
            >
              {t('handoverDate')}
            </Paragraph>
            <Paragraph textSize={TextSize.Small} className="">
              {new Date().toLocaleDateString('en-GB')}
            </Paragraph>
          </div>
        </div>
      </div>
    </Card>
  );
};
