import 'whatwg-fetch';
import { EditScheduleForm, EditScheduleFormProps } from './edit-schedule-form';
import { TEST_CHARGE_SCHEDULE } from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../../test-utils';

jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  useFormStatus: () => ({
    pending: false,
  }),
}));

describe('Edit schedule form', () => {
  const defaultProps: EditScheduleFormProps = {
    ppid: 'PSL-12345',
    schedule: TEST_CHARGE_SCHEDULE,
  };
  it('should render correctly', () => {
    const { baseElement } = renderWithProviders(
      <EditScheduleForm {...defaultProps} />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(
      <EditScheduleForm {...defaultProps} />
    );
    expect(baseElement).toMatchSnapshot();
  });
});
