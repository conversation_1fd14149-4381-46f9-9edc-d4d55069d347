import { Default<PERSON>pi as ConnectivityCommandsApi } from '@experience/shared/axios/connectivity-commands-api-client';
import { Injectable, Logger } from '@nestjs/common';
import { OidcUser } from '@experience/shared/nest/utils';
import {
  ResetChargerRequest,
  SetChargerAvailabilityRequest,
  UnlockConnectorRequest,
} from '@experience/support/support-tool/shared';

@Injectable()
export class ChargerCommandsService {
  private readonly logger = new Logger(ChargerCommandsService.name);

  constructor(
    private readonly connectivityCommandsApi: ConnectivityCommandsApi
  ) {}

  async sendResetCommand(
    ppid: string,
    request: ResetChargerRequest,
    user: OidcUser
  ): Promise<void> {
    const context = { ppid, request, user };
    this.logger.log(context, 'sending reset command');

    await this.connectivityCommandsApi
      .commandsChargingStationsPpidResetPost(ppid, {
        ...request,
        clientRef: `${request.clientRef}_${user.email}`,
      })
      .catch((error) => {
        this.logger.error({ error, ...context }, 'error sending reset command');
        throw error;
      });
  }

  async sendAvailabilityCommand(
    ppid: string,
    request: SetChargerAvailabilityRequest,
    user: OidcUser
  ): Promise<void> {
    const context = { ppid, request, user };
    this.logger.log(context, `sending availability command`);

    await this.connectivityCommandsApi
      .commandsChargingStationsPpidAvailabilitySetPost(ppid, {
        ...request,
        clientRef: `${request.clientRef}_${user.email}`,
      })
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          `error sending availability command`
        );
        throw error;
      });
  }

  async sendUnlockConnectorCommand(
    ppid: string,
    request: UnlockConnectorRequest,
    user: OidcUser
  ): Promise<void> {
    const context = { ppid, request, user };
    this.logger.log(context, `sending unlock connector command`);
    await this.connectivityCommandsApi
      .commandsChargingStationsPpidUnlockConnectorPost(ppid, {
        ...request,
        clientRef: `${request.clientRef}_${user.email}`,
      })
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          `error sending unlock connector command`
        );
        throw error;
      });
  }
}
