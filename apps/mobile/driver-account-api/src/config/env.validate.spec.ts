import { validate } from './env.validate';
describe('Queue configuration requires mandatory env variables to be available', () => {
  let env: Record<string, string>;
  beforeEach(() => {
    env = {
      USER_QUEUE_URL:
        'https://sqs.eu-west-1.amazonaws.com/XXXX/migrate-users-events',
      IDENTITY_BASE_URL_CLASSIC: 'identity.podpoint.com',
      IDENTITY_BASE_URL_POD_ENERGY: 'identity.pod-energy.com',
      FIREBASE_PROJECT_ID: 'firebase-project-id',
      FIREBASE_API_KEY: 'firebase',
      DRIVER_ACCOUNT_DB_CONFIG: 'driver-account-db-config',
    };
  });
  it('Config validator successful if env variables set', () => {
    const config = () => validate(env);
    expect(config).not.toThrow();
  });
  it('Config validator throws error if USER_QUEUE_URL env not set', () => {
    env.USER_QUEUE_URL = '';
    const configEmpty = () => validate(env);
    expect(configEmpty).toThrow(/Missing required env vars : USER_QUEUE_URL/);
    delete env.USER_QUEUE_URL;
    const configMissing = () => validate(env);
    expect(configMissing).toThrow(/Missing required env vars : USER_QUEUE_URL/);
  });
  it('Config validator will not throws error if additional properties set', () => {
    env.ADDITION_PROPERTY = '';
    const config = () => validate(env);
    expect(config).not.toThrow();
  });
});
