import {
  CONNECTION_NAME as AUTH_SERVICE_CONNECTION_NAME,
  AuthServiceSequelizeModule,
  Users as AuthServiceUsers,
} from '@experience/shared/sequelize/auth-service';
import { INestApplication, Logger, Module } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import {
  CONNECTION_NAME as POD_ADMIN_CONNECTION_NAME,
  Authorisers as PodAdminAuthorisers,
  BillingAccounts as PodAdminBillingAccounts,
  Users as PodAdminUsers,
  PodadminSequelizeModule,
} from '@experience/shared/sequelize/podadmin';
import { ParseArgsConfig, parseArgs } from 'util';
import { Sequelize, Transaction } from 'sequelize';
import { UserRecord } from 'firebase-admin/auth';
import { getAuth } from '@experience/shared/firebase/admin';
import { getConnectionToken } from '@nestjs/sequelize';
import { readFileSync, writeFileSync } from 'fs';

interface GenericUser {
  uuid: string;
  firebaseRecord?: UserRecord;
  databaseRecord?: AuthServiceUsers;
}

interface Record {
  uuid: string;
  email: string;
  disabled: boolean;
  deletedAt?: Date;
}

const COMMAND_LINE_OPTIONS: ParseArgsConfig = {
  options: {
    help: {
      type: 'boolean',
      short: 'h',
    },
    fix: {
      type: 'boolean',
      short: 'f',
    },
    import: {
      type: 'string',
      short: 'i',
    },
  },
};

@Module({
  imports: [AuthServiceSequelizeModule, PodadminSequelizeModule],
  providers: [
    {
      provide: 'AUTH_SERVICE_USERS_REPOSITORY',
      useValue: AuthServiceUsers,
    },
    {
      provide: 'PODADMIN_USERS_REPOSITORY',
      useValue: PodAdminUsers,
    },
    {
      provide: 'PODADMIN_BILLING_ACCOUNTS_REPOSITORY',
      useValue: PodAdminBillingAccounts,
    },
    {
      provide: 'PODADMIN_AUTHORISERS_REPOSITORY',
      useValue: PodAdminAuthorisers,
    },
  ],
})
class AppModule {}

const logger = new Logger();

const displayUsage = () => {
  // eslint-disable-next-line no-console
  console.log(`
    auth-sync-verify is a script which scans firebase and the auth service database to identify
    users whose state of enablement differs between the data sources.

    usage: auth-sync-verify [options]

    options:
      --help, -h : display this help message
      --fix, -f : update identified user records using firebase as source of truth
      --import, -i [path to csv] : skip the initial scan and import an existing result CSV to fix
  `);

  process.exit(0);
};

const run = async () => {
  let args = null;

  try {
    args = parseArgs(COMMAND_LINE_OPTIONS);
  } catch (error) {
    displayUsage();
  }

  if (args.values.help) {
    displayUsage();
  }

  const app = await NestFactory.create(AppModule);

  const records = args.values.import
    ? readResultCsv(args.values.import)
    : await scanUserDataSources(app);

  if (args.values.fix) {
    await updateAuthUsers(app, records);
  } else {
    await writeResultCsv('results.csv', records);
  }

  await app.close();
};

const getDatabaseUsers = async (
  app: INestApplication
): Promise<AuthServiceUsers[]> => {
  const authServiceUsersRepository = app.get<typeof AuthServiceUsers>(
    'AUTH_SERVICE_USERS_REPOSITORY'
  );

  return await authServiceUsersRepository.findAll({ paranoid: false });
};

const getFirebaseUsers = async () => {
  let users = [];

  try {
    let result = await getAuth().listUsers(1000);
    let { pageToken } = result;

    users = [...result.users];

    logger.log({ retrieved: users.length }, 'Fetching Firebase Users');

    while (pageToken != null) {
      result = await getAuth().listUsers(1000, pageToken);
      ({ pageToken } = result);
      users = [...users, ...result.users];

      logger.log({ retrieved: users.length }, 'Fetching Firebase Users');
    }
  } catch (error) {
    logger.log('Error fetching user data:', error);
  }

  return users;
};

const scanUserDataSources = async (
  app: INestApplication
): Promise<Record[]> => {
  const dbUsers = await getDatabaseUsers(app);
  const fbUsers = await getFirebaseUsers();
  const merged = new Map<string, GenericUser>();

  dbUsers.forEach((user) => {
    if (merged.has(user.uuid)) {
      merged.get(user.uuid).databaseRecord = user;
    } else {
      merged.set(user.uuid, {
        uuid: user.uuid,
        databaseRecord: user,
      });
    }
  });

  fbUsers.forEach((user) => {
    if (merged.has(user.uid)) {
      merged.get(user.uid).firebaseRecord = user;
    } else {
      merged.set(user.uid, {
        uuid: user.uid,
        firebaseRecord: user,
      });
    }
  });

  const differs = [];

  merged.forEach((value) => {
    if (value.firebaseRecord == null || value.databaseRecord == null) {
      return;
    }

    const authDeleted = value.databaseRecord.deletedAt != null;
    const fbDeleted = value.firebaseRecord.disabled;

    if (authDeleted != fbDeleted) {
      differs.push(value);
    }
  });

  logger.log(
    {
      differs: differs.length,
    },
    'Identified differing users'
  );

  return differs.map((value) => ({
    uuid: value.uuid,
    email: value.firebaseRecord.email,
    disabled: value.firebaseRecord.disabled,
    deletedAt: value.databaseRecord.deletedAt,
  }));
};

const updateAuthUsers = async (app: INestApplication, records: Record[]) => {
  const uuidsToEnable = records
    .filter((record) => record.disabled == false)
    .map((record) => record.uuid);

  const uuidsToDisable = records
    .filter((record) => record.disabled == true)
    .map((record) => record.uuid);

  logger.log({ uuidsToEnable }, 'Enabling users');
  await executeDatabaseUpdate(app, uuidsToEnable, null);

  logger.log({ uuidsToDisable }, 'Disabling users');
  await executeDatabaseUpdate(app, uuidsToDisable, new Date());
};

const executeDatabaseUpdate = async (
  app: INestApplication,
  uuids: string[],
  deletedAt: Date
) => {
  if (uuids.length === 0) {
    return;
  }

  const podAdminConnection = app.get<Sequelize>(
    getConnectionToken(POD_ADMIN_CONNECTION_NAME)
  );

  const authServiceConnection = app.get<Sequelize>(
    getConnectionToken(AUTH_SERVICE_CONNECTION_NAME)
  );

  const podAdminTransaction: Transaction =
    await podAdminConnection.transaction();
  const authServiceTransaction: Transaction =
    await authServiceConnection.transaction();

  try {
    const podAdminUserIds = await getPodAdminUserIds(app, uuids);

    await updatePodAdminUsers(app, uuids, deletedAt, podAdminTransaction);
    await updatePodAdminBillingAccounts(
      app,
      podAdminUserIds,
      deletedAt,
      podAdminTransaction
    );
    await updatePodAdminAuthorisers(app, uuids, deletedAt, podAdminTransaction);

    await updateAuthServiceUsers(app, uuids, deletedAt, authServiceTransaction);

    await podAdminTransaction.commit();
    await authServiceTransaction.commit();
  } catch (error) {
    logger.error({ error }, 'Failed to update users, changes rolled back');

    await podAdminTransaction.rollback();
    await authServiceTransaction.rollback();
  }
};

const updateAuthServiceUsers = async (
  app: INestApplication,
  uuids: string[],
  deletedAt: Date,
  transaction: Transaction
) => {
  const authServiceUsersRepository = app.get<typeof AuthServiceUsers>(
    'AUTH_SERVICE_USERS_REPOSITORY'
  );

  return await authServiceUsersRepository.update(
    {
      deletedAt,
    },
    {
      where: {
        uuid: uuids,
      },
      paranoid: false,
      transaction,
    }
  );
};

const getPodAdminUserIds = async (app: INestApplication, uuids: string[]) => {
  const podAdminUsersRepository = app.get<typeof PodAdminUsers>(
    'PODADMIN_USERS_REPOSITORY'
  );

  const result = await podAdminUsersRepository.findAll({
    where: {
      authId: uuids,
    },
    paranoid: false,
  });

  return result.map((user) => user.id);
};

const updatePodAdminUsers = async (
  app: INestApplication,
  uuids: string[],
  deletedAt: Date,
  transaction: Transaction
) => {
  const podAdminUsersRepository = app.get<typeof PodAdminUsers>(
    'PODADMIN_USERS_REPOSITORY'
  );

  return await podAdminUsersRepository.update(
    {
      deletedAt,
    },
    {
      where: {
        authId: uuids,
      },
      paranoid: false,
      transaction,
    }
  );
};

const updatePodAdminBillingAccounts = async (
  app: INestApplication,
  userIds: number[],
  deletedAt: Date,
  transaction: Transaction
) => {
  const podAdminBillingAccountsRepository = app.get<
    typeof PodAdminBillingAccounts
  >('PODADMIN_BILLING_ACCOUNTS_REPOSITORY');

  return await podAdminBillingAccountsRepository.update(
    {
      deletedAt,
    },
    {
      where: {
        userId: userIds,
      },
      paranoid: false,
      transaction,
    }
  );
};

const updatePodAdminAuthorisers = async (
  app: INestApplication,
  uuids: string[],
  deletedAt: Date,
  transaction: Transaction
) => {
  const podAdminAuthorisersRepository = app.get<typeof PodAdminAuthorisers>(
    'PODADMIN_AUTHORISERS_REPOSITORY'
  );

  return await podAdminAuthorisersRepository.update(
    {
      deletedAt,
    },
    {
      where: {
        uid: uuids,
      },
      paranoid: false,
      transaction,
    }
  );
};

const writeResultCsv = async (path: string, records: Record[]) => {
  let csv = 'uuid,email,firebaseDisabled,authServiceDeletedAt\n';

  records.forEach((value) => {
    csv += `${value.uuid},${value.email},${value.disabled},${value.deletedAt}\n`;
  });

  writeFileSync(path, csv, 'utf8');

  logger.log(`Wrote results to ${path}`);
};

const readResultCsv = (path: string): Record[] => {
  const data = readFileSync(path, 'utf-8');

  return data
    .split('\n')
    .slice(1)
    .filter(Boolean)
    .map((line) => {
      const columns = line.split(',');

      return {
        uuid: columns[0],
        email: columns[1],
        disabled: columns[2].toUpperCase() == 'TRUE' ? true : false,
        deletedAt:
          columns[3] != 'null' ? new Date(Date.parse(columns[3])) : undefined,
      };
    });
};

run().catch((error) => logger.error(error));
