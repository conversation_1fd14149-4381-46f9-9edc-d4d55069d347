import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

import { PasswordResetService } from '@experience/mobile/nest/auth';
import { TrackLoginRequest } from '@experience/mobile/driver-account/domain/user';
import { UpdateEmailService } from './update-email-and-revert.service';
import { UserChargersService } from './user-chargers.service';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { createMock } from '@golevelup/ts-jest';
import { getUserByEmail, mockAuth } from '@experience/mobile/test/mocking';

export const user = {
  uid: '12345',
  email: '<EMAIL>',
  emailVerified: true,
  disabled: false,
  passwordHash: '12345',
  metadata: {
    creationTime: '2020-11-03T00:00:00',
    lastSignInTime: '2020-11-05T00:00:00',
  },
  providerData: [],
};

jest.mock('@experience/shared/firebase/admin', () => ({
  getAuth: () => mockAuth,
}));

describe('UserController', () => {
  let app: INestApplication;
  let module: TestingModule;
  let userService: UserService;
  let updateEmailService: UpdateEmailService;
  let userChargerService: UserChargersService;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: {
            createUser: jest.fn(),
            getUser: jest.fn(),
            getByFilter: jest.fn(),
            updateUser: jest.fn(),
            enableUser: jest.fn(),
            getSuppressedStatus: jest.fn(),
            removeSuppressionStatus: jest.fn(),
            deleteUser: jest.fn(),
            trackLogin: jest.fn(),
          },
        },
        {
          provide: UpdateEmailService,
          useValue: {
            updateEmail: jest.fn(),
          },
        },
        {
          provide: UserChargersService,
          useValue: {
            getChargersForUser: jest.fn(),
            unLinkChargerFromUser: jest.fn(),
          },
        },
        {
          provide: PasswordResetService,
          useValue: createMock<PasswordResetService>(),
        },
      ],
    }).compile();

    userService = module.get<UserService>(UserService);
    updateEmailService = module.get<UpdateEmailService>(UpdateEmailService);
    userChargerService = module.get<UserChargersService>(UserChargersService);

    app = module.createNestApplication();

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('createUser', () => {
    it('should forward call to user userService"', async () => {
      const response = await request(app.getHttpServer())
        .post(`/users`)
        .send({});
      expect(userService.createUser).toHaveBeenCalledWith(
        {},
        'en',
        undefined,
        undefined
      );
      expect(response.status).toEqual(201);
    });

    it('should forward call to user userService and set the language"', async () => {
      const response = await request(app.getHttpServer())
        .post(`/users`)
        .set('Accept-Language', 'es')
        .send({});
      expect(userService.createUser).toHaveBeenCalledWith(
        {},
        'es',
        undefined,
        undefined
      );
      expect(response.status).toEqual(201);
    });
  });

  describe('getUser', () => {
    it('should forward call to user userService and return 200', async () => {
      const response = await request(app.getHttpServer()).get(`/users/123`);
      expect(response.status).toEqual(200);
      expect(userService.getUser).toHaveBeenCalledWith('123');
    });
  });

  describe('getByFilter', () => {
    it('should forward request to user userService with an email and return 200', async () => {
      const response = await request(app.getHttpServer()).get(
        `/users?email=<EMAIL>`
      );
      expect(response.status).toEqual(200);
      expect(userService.getByFilter).toHaveBeenCalledWith({
        email: '<EMAIL>',
      });
    });

    it('should forward request to user userService with a fuzzy email and return 200', async () => {
      const response = await request(app.getHttpServer()).get(
        `/users?emailLike=<EMAIL>`
      );
      expect(response.status).toEqual(200);
      expect(userService.getByFilter).toHaveBeenCalledWith({
        emailLike: '<EMAIL>',
      });
    });

    it('should forward request to user userService with no filters and return 200', async () => {
      const response = await request(app.getHttpServer()).get(`/users`);
      expect(response.status).toEqual(200);
      expect(userService.getByFilter).toHaveBeenCalledWith({});
    });

    it('should forward request to user userService with a ppid and return 200', async () => {
      const response = await request(app.getHttpServer()).get(
        `/users?ppid=PSL-123456`
      );
      expect(response.status).toEqual(200);
      expect(userService.getByFilter).toHaveBeenCalledWith({
        ppid: 'PSL-123456',
      });
    });
  });

  describe('updateUser', () => {
    it('should forward the request to user userService', async () => {
      const response = await request(app.getHttpServer())
        .put(`/users/1234`)
        .send({});
      expect(response.status).toEqual(200);
      expect(userService.updateUser).toHaveBeenCalledWith('1234', {});
    });
  });

  describe('enableUser', () => {
    it('should enable user', async () => {
      const response = await request(app.getHttpServer()).put(
        `/users/enable/1234`
      );
      expect(response.status).toEqual(200);
      expect(userService.enableUser).toHaveBeenCalledWith('1234');
    });
  });

  describe('updateEmail', () => {
    it('should forward the request to user userService', async () => {
      getUserByEmail.mockImplementationOnce(() => Promise.resolve(user));

      const response = await request(app.getHttpServer())
        .put(`/users/email/update`)
        .set('Accept-Language', 'es')
        .set('x-app-name', 'flexdrive')
        .send({});

      expect(response.status).toEqual(200);
      expect(updateEmailService.updateEmail).toHaveBeenCalledWith(
        {},
        'es',
        'flexdrive'
      );
    });

    it('should throw a 404 if the user is disabled', async () => {
      getUserByEmail.mockImplementationOnce(() =>
        Promise.resolve({ ...user, disabled: true })
      );

      const response = await request(app.getHttpServer())
        .put(`/users/email/update`)
        .set('Accept-Language', 'es')
        .send({});

      expect(response.status).toBe(404);
    });
  });

  describe('getChargers', () => {
    it('should forward call to user userChargerService and return 200', async () => {
      const response = await request(app.getHttpServer()).get(
        `/users/123/chargers`
      );
      expect(response.status).toEqual(200);
      expect(userChargerService.getChargersForUser).toHaveBeenCalledWith('123');
    });
  });

  describe('unLinkChargerFromUser', () => {
    it('should forward call to userChargerService and return 204', async () => {
      const response = await request(app.getHttpServer()).delete(
        `/users/123/chargers/456`
      );
      expect(response.status).toEqual(204);
      expect(userChargerService.unLinkChargerFromUser).toHaveBeenCalledWith(
        '123',
        '456'
      );
    });
  });

  describe('getSuppressedStatus', () => {
    it('should forward call to userService and return 200', async () => {
      const response = await request(app.getHttpServer()).get(
        `/users/123/suppressedStatus`
      );
      expect(response.status).toEqual(200);
      expect(userService.getSuppressedStatus).toHaveBeenCalledWith('123');
    });
  });

  describe('updateSuppressedStatus', () => {
    it('should return a 400 if status is missing', async () => {
      const response = await request(app.getHttpServer())
        .put(`/users/123/suppressedStatus`)
        .send({});
      expect(response.status).toEqual(400);
      expect(userService.removeSuppressionStatus).not.toHaveBeenCalled();
    });

    it('should forward call to userService and return 200', async () => {
      const response = await request(app.getHttpServer())
        .put(`/users/123/suppressedStatus`)
        .send({
          status: null,
        });
      expect(response.status).toEqual(200);
      expect(userService.removeSuppressionStatus).toHaveBeenCalledWith('123');
    });
  });

  describe('softDelete', () => {
    it('should forward call to user userService and return 200', async () => {
      const response = await request(app.getHttpServer()).delete(
        `/users/123?force=true`
      );
      expect(response.status).toEqual(200);
      expect(userService.deleteUser).toHaveBeenCalledWith('123', true);
    });
  });

  describe('trackLogin', () => {
    const mockedtrackLoginRequest: TrackLoginRequest = {
      ipAddress: '2a00:23ee:1320:109f:98e0:dcc6:b917:f126',
      userAgent:
        'FirebaseAuth.iOS/8.15.0 com.podpoint.podpoint/3.26.0 iPhone/17.5.1 hw/iPhone14_5,gzip(gfe),gzip(gfe',
      timestamp: '2020-03-14 00:00:00',
      authId: '12345',
    };
    it('should trackLogin a user and return 200', async () => {
      const response = await request(app.getHttpServer())
        .post(`/users/login/123/alert`)
        .send(mockedtrackLoginRequest);
      expect(response.status).toEqual(200);
      expect(userService.trackLogin).toHaveBeenCalledWith(
        '123',
        mockedtrackLoginRequest
      );
    });
  });
});
