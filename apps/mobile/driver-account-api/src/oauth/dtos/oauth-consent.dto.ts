import { ApiProperty } from '@nestjs/swagger';

export class OAuthConsentRequestDto {
  @ApiProperty({
    description: 'The client ID for the application requesting access',
    type: 'string',
    example: 'client_id',
    required: true,
  })
  clientId: string;

  @ApiProperty({
    description: 'The redirect URI used in the authorize endpoint flow',
    type: 'string',
    format: 'uri',
    example: 'redirect_uri',
    required: true,
  })
  redirectUri: string;

  @ApiProperty({
    description: 'Response type (must be "code")',
    type: 'string',
    example: 'code',
    required: true,
  })
  responseType: string;

  @ApiProperty({
    description: 'PKCE code challenge method (only S256 is supported)',
    type: 'string',
    example: 'S256',
    required: true,
  })
  codeChallengeMethod: string;

  @ApiProperty({
    description:
      'Code challenge generated by the client using the code verifier',
    type: 'string',
    example: 'code_challenge',
    required: true,
  })
  codeChallenge: string;

  @ApiProperty({
    description: 'The scopes requested for access',
    type: 'string',
    example: 'data:read',
    required: false,
  })
  scope?: string;

  @ApiProperty({
    description:
      'Optional string to be repeated in the response to prevent CSRF attacks',
    type: 'string',
    required: false,
  })
  state?: string;

  @ApiProperty({
    description: 'The user id of the user who is granting access to the client',
    type: 'string',
    example: 'user-id',
  })
  userId: string;
}
