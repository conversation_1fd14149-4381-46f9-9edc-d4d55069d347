import {
  ApiBadRequestResponse,
  ApiBody,
  ApiExtraModels,
  ApiFoundResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpRedirectResponse,
  NotFoundException,
  Param,
  Post,
  Query,
  Redirect,
} from '@nestjs/common';
import { OAuthAuthorizeService } from './oauth.authorize.service';
import {
  OAuthClientResponseDto,
  presentOAuthClient,
} from './dtos/oauth-client.dto';
import { OAuthClientService } from './oauth.client.service';
import { OAuthConsentRequestDto } from './dtos/oauth-consent.dto';
import {
  OAuthTokenAuthorizationCodeRequestDto,
  OAuthTokenAuthorizationCodeResponseDto,
} from './dtos/oauth-token-authorization-code.dto';
import {
  OAuthTokenClientCredentialsRequestDto,
  OAuthTokenClientCredentialsResponseDto,
} from './dtos/oauth-token-client-credentials.dto';
import {
  OAuthTokenRefreshTokenRequestDto,
  OAuthTokenRefreshTokenResponseDto,
} from './dtos/oauth-token-refresh-token.dto';
import { OAuthTokenService } from './oauth.token.service';

@Controller('oauth')
@ApiTags('oauth')
export class OAuthController {
  constructor(
    readonly oAuthAuthorizeService: OAuthAuthorizeService,
    readonly oAuthTokenService: OAuthTokenService,
    readonly oAuthClientService: OAuthClientService
  ) {}

  @Get('authorize')
  @ApiOperation({
    summary: 'OAuth 2.0 authorization endpoint',
    description: [
      'Triggers an OAuth 2.0 flow for users to authorize access to their charger',
      'The only supported flow is Authorization Code Flow with PKCE.',
    ].join('\n'),
  })
  @ApiQuery({
    name: 'scope',
    required: false,
    type: 'string',
    description: [
      'The scope(s) requested for access',
      'Note that this is an OAuth endpoint only.',
      'Therefore openid scopes and /userinfo are not supported.',
    ].join('\n'),
  })
  @ApiQuery({
    name: 'response_type',
    required: true,
    type: 'string',
    description: 'Response type (must be "code")',
    example: 'code',
  })
  @ApiQuery({
    name: 'client_id',
    required: true,
    type: 'string',
    description: 'Client ID for the application requesting access',
  })
  @ApiQuery({
    name: 'state',
    required: false,
    type: 'string',
    description:
      'Optional string to be repeated in the response to prevent CSRF attacks',
  })
  @ApiQuery({
    name: 'redirect_uri',
    required: true,
    type: 'string',
    description: [
      'URI to redirect the user to after authorization',
      'This must be provided and match one of the URIs registered against the client ID.',
      'On successful authorization this will be called with `code` (and `state` if provided) as query parameters.',
      'The `code` can then be exchanged for an access token using the `/token` endpoint.',
      'On error, the user will be redirected to this URI with an `error` (as defined in RFC 6749 *******)',
      'As well as an `error_description` to be displayed to the user (and `state` if provided).',
    ].join('\n'),
  })
  @ApiQuery({
    name: 'code_challenge_method',
    required: true,
    type: 'string',
    description: 'PKCE code challenge method (only S256 is supported)',
  })
  @ApiQuery({
    name: 'code_challenge',
    required: true,
    type: 'string',
    description:
      'Code challenge generated by the client using the code verifier',
  })
  @ApiBadRequestResponse({
    description: 'Invalid request (see error message for details)',
  })
  @ApiFoundResponse({
    description:
      'See Location header for URI to redirect the user to for authorization',
  })
  @Redirect()
  async authorize(
    @Query('scope') scope: string,
    @Query('response_type') responseType: string,
    @Query('client_id') clientId: string,
    @Query('state') state: string,
    @Query('redirect_uri') redirectUri: string,
    @Query('code_challenge_method') codeChallengeMethod: string,
    @Query('code_challenge') codeChallenge: string
  ): Promise<HttpRedirectResponse> {
    const uri = await this.oAuthAuthorizeService.authorize({
      clientId,
      redirectUri,
      responseType,
      codeChallengeMethod,
      codeChallenge,
      scope: scope,
      state,
    });
    return { statusCode: 302, url: uri };
  }

  @Get('client_info/:client_id')
  @ApiOperation({
    summary: 'Get information on an OAuth 2.0 client',
    description:
      'Get information on an OAuth 2.0 client, including name, image and website URL',
  })
  @ApiNotFoundResponse({
    description: 'Client not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Only an authenticated user can call this endpoint',
  })
  @ApiOkResponse({
    description: 'Client information',
    type: OAuthClientResponseDto,
  })
  async getOAuthClient(
    @Param('client_id') clientId: string
  ): Promise<OAuthClientResponseDto> {
    const client = await this.oAuthClientService.getClient({ clientId });
    if (client == null) {
      throw new NotFoundException();
    }
    return presentOAuthClient(client);
  }

  @Post('consent')
  @ApiOperation({
    summary:
      'Consent to an OAuth 2.0 application accessing the authorized account',
    description: [
      'The internal stage of the OAuth 2.0 flow where the user, after signing-in,',
      'consents to the application accessing their account. (as limited by the scopes requested).',
    ].join('\n'),
  })
  @ApiBadRequestResponse({
    description: 'Invalid request (see error message for details)',
  })
  @ApiUnauthorizedResponse({
    description: 'Only an authenticated user can call this endpoint',
  })
  @ApiFoundResponse({
    description: [
      'See Location header for URI to redirect the user to for authorization',
      'On successful authorization this will be called with `code` (and `state` if provided) as query parameters.',
      'The `code` can then be exchanged for an access token using the `/token` endpoint.',
      'On error, the user will be redirected to this URI with an `error` (as defined in RFC 6749 *******)',
      'As well as an `error_description` to be displayed to the user (and `state` if provided).',
    ].join('\n'),
  })
  @Redirect()
  async consent(@Body() consentRequest: OAuthConsentRequestDto) {
    const uri = await this.oAuthAuthorizeService.consent(consentRequest);
    return { statusCode: 302, url: uri };
  }

  @Post('token')
  @ApiOperation({
    summary: 'OAuth 2.0 token endpoint',
    description: [
      'Exchanges an authorization/refresh code or client credentials for an access token.',
      'The supported grant types are `authorization_code`,`client_credentials` and `refresh_token`.',
      'Use of `authorization_code` requires PKCE extensions, and is used against charger specific endpoints.',
      '`refresh_token` can then be used to refresh an access token provided via the authorization code flow.',
      '`client_credentials` can only be used for non-charger specific endpoints (e.g. webhooks),',
      'and the resulting access token does not support refresh.',
      'All access tokens expire after 1 hour.',
      'Refresh tokens (where provided) expire as defined by the OAuth application.',
    ].join('\n'),
  })
  @ApiExtraModels(
    OAuthTokenAuthorizationCodeRequestDto,
    OAuthTokenClientCredentialsRequestDto,
    OAuthTokenRefreshTokenRequestDto
  )
  @ApiBody({
    description: 'OAuth 2.0 token exchange request',
    schema: {
      oneOf: [
        { $ref: getSchemaPath(OAuthTokenAuthorizationCodeRequestDto) },
        { $ref: getSchemaPath(OAuthTokenClientCredentialsRequestDto) },
        { $ref: getSchemaPath(OAuthTokenRefreshTokenRequestDto) },
      ],
    },
    examples: {
      authorization_code: {
        value: {
          grant_type: 'authorization_code',
          client_id: 'client_id',
          code: 'code',
          code_verifier: 'code_verifier',
          redirect_uri: 'redirect_uri',
        },
      },
      client_credentials: {
        value: {
          grant_type: 'client_credentials',
          client_id: 'client_id',
          client_secret: 'client_secret',
        },
      },
      refresh_token: {
        value: {
          grant_type: 'refresh_token',
          client_id: 'client_id',
          refresh_token: 'refresh_token',
        },
      },
    },
  })
  @ApiExtraModels(
    OAuthTokenAuthorizationCodeResponseDto,
    OAuthTokenClientCredentialsResponseDto,
    OAuthTokenRefreshTokenResponseDto
  )
  @ApiBadRequestResponse({
    description: 'Invalid request (see error message for details)',
  })
  @ApiOkResponse({
    description: 'OAuth 2.0 token exchange response',
    content: {
      'application/json': {
        schema: {
          oneOf: [
            { $ref: getSchemaPath(OAuthTokenAuthorizationCodeResponseDto) },
            { $ref: getSchemaPath(OAuthTokenClientCredentialsResponseDto) },
            { $ref: getSchemaPath(OAuthTokenRefreshTokenResponseDto) },
          ],
        },
        examples: {
          authorization_code: {
            value: {
              access_token: 'access_token',
              token_type: 'Bearer',
              expires_in: 3600,
              refresh_token: 'refresh_token',
              scope: 'data:read',
            },
          },
          client_credentials: {
            value: {
              access_token: 'access_token',
              token_type: 'Bearer',
              expires_in: 3600,
              scope: 'data:read',
            },
          },
          refresh_token: {
            value: {
              access_token: 'access_token',
              token_type: 'Bearer',
              expires_in: 3600,
            },
          },
        },
      },
    },
  })
  @HttpCode(200)
  async token(
    @Body()
    tokenRequest:
      | OAuthTokenAuthorizationCodeRequestDto
      | OAuthTokenClientCredentialsRequestDto
      | OAuthTokenRefreshTokenRequestDto
  ): Promise<
    | OAuthTokenAuthorizationCodeResponseDto
    | OAuthTokenClientCredentialsResponseDto
    | OAuthTokenRefreshTokenResponseDto
  > {
    switch (tokenRequest.grant_type) {
      case 'authorization_code': {
        const token = await this.oAuthTokenService.getToken({
          grantType: tokenRequest.grant_type,
          clientId: tokenRequest.client_id,
          code: tokenRequest.code,
          codeVerifier: tokenRequest.code_verifier,
          redirectUri: tokenRequest.redirect_uri,
        });
        return {
          access_token: token.accessToken,
          token_type: token.tokenType,
          expires_in: token.expiresIn,
          refresh_token: token.refreshToken,
          scope: token.scope,
        } as OAuthTokenAuthorizationCodeResponseDto;
      }
      case 'refresh_token': {
        const token = await this.oAuthTokenService.getToken({
          grantType: tokenRequest.grant_type,
          clientId: tokenRequest.client_id,
          refreshToken: tokenRequest.refresh_token,
        });
        return {
          access_token: token.accessToken,
          token_type: token.tokenType,
          expires_in: token.expiresIn,
        } as OAuthTokenRefreshTokenResponseDto;
      }
      case 'client_credentials': {
        const token = await this.oAuthTokenService.getToken({
          grantType: tokenRequest.grant_type,
          clientId: tokenRequest.client_id,
          clientSecret: tokenRequest.client_secret,
        });
        return {
          access_token: token.accessToken,
          token_type: token.tokenType,
          expires_in: token.expiresIn,
          scope: token.scope,
        } as OAuthTokenClientCredentialsResponseDto;
      }
      default:
        throw new BadRequestException('unsupported_grant_type');
    }
  }
}
