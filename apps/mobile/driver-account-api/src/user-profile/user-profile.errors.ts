export enum UserError {
  FAILED_TO_CREATE_USER_PROFILE = 'FAILED_TO_CREATE_USER_PROFILE',
  FAILED_TO_FIND_USER_PROFILE = 'FAILED_TO_FIND_USER_PROFILE',
  FAILED_TO_UPDATE_USER_PROFILE = 'FAILED_TO_UPDATE_USER_PROFILE',
  FAILED_TO_DELETE_USER_PROFILE = 'FAILED_TO_DELETE_USER_PROFILE',
  FAILED_TO_RESTORE_USER_PROFILE = 'FAILED_TO_RESTORE_USER_PROFILE',
}

export class FailedToCreateUserProfileError extends Error {
  constructor(uuid: string) {
    super(`Failed to create user profile for user '${uuid}'`);
  }
}

export class FailedToFindUserProfileError extends Error {
  constructor(uuid: string) {
    super(`Failed to find user profile for user '${uuid}'`);
  }
}

export class FailedToUpdateUserProfileError extends Error {
  constructor(uuid: string) {
    super(`Failed to update user profile for user '${uuid}'`);
  }
}

export class FailedToDeleteUserProfileError extends Error {
  constructor(uuid: string) {
    super(`Failed to delete user profile for user '${uuid}'`);
  }
}

export class FailedToRestoreUserProfileError extends Error {
  constructor(uuid: string) {
    super(`Failed to restore user profile for user '${uuid}'`);
  }
}
