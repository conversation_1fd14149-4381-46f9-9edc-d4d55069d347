import { Client } from 'pg';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import {
  buildUserCreatedEvent,
  buildUserDeletedEvent,
  buildUserRestoredEvent,
  buildUserUpdatedEvent,
} from '@experience/mobile/events';
import { setTimeout as delay } from 'timers/promises';

export const getUserProfile = async (db: Client, uuid: string) => {
  const result = await db.query(
    `SELECT * FROM profile.users WHERE uuid = '${uuid}';`
  );

  return result.rows[0];
};

const sendMessage = async (message: string, queueUrl: string) => {
  const sqsClient = new SQSClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://127.0.0.1:4566',
    region: 'eu-west-1',
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });

  const command = new SendMessageCommand({
    QueueUrl: queueUrl,
    MessageBody: message,
  });
  await sqsClient.send(command);
};

export const testSendCreateMessage = async (
  queueUrl: string,
  uid: string,
  db: Client
) => {
  const message = buildUserCreatedEvent(
    'Driver',
    '/experience/mobile/driver-account-api',
    {
      user: {
        id: uid,
        email: '<EMAIL>',
        firstName: 'Mobiler',
        lastName: 'Tester',
        locale: 'en',
        consent: {
          marketing: {
            isConsentGiven: false,
            type: null,
            copy: null,
            origin: null,
          },
        },
        preferences: {
          unitOfDistance: 'mi',
        },
      },
    },
    {
      createdAt: new Date(),
    }
  );
  await sendMessage(JSON.stringify(message), queueUrl);
  await delay(1000);
  const profile = await getUserProfile(db, uid);
  expect(profile).toBeDefined();
  expect(profile.created_at).toBeDefined();
};

export const testUpdateUserEvent = async (
  db: Client,
  uid: string,
  email: string,
  queueUrl: string
) => {
  const firstName = 'John';
  const lastName = 'Doe';
  const profileBefore = await getUserProfile(db, uid);

  expect(profileBefore).toBeUndefined();
  const message = buildUserUpdatedEvent(
    'Driver',
    '/experience/mobile/driver-account-api',
    {
      user: {
        id: uid,
        email: email,
        firstName: firstName,
        lastName: lastName,
      },
    }
  );
  await sendMessage(JSON.stringify(message), queueUrl);
  await delay(1000);
  const profileAfter = await getUserProfile(db, uid);

  expect(profileAfter.first_name).toEqual(firstName);
  expect(profileAfter.last_name).toEqual(lastName);
  expect(profileAfter.updated_at).toBeDefined();
};

export const testRestoreEvent = async (
  db: Client,
  uid: string,
  email: string,
  queueUrl: string
) => {
  const profileBefore = await getUserProfile(db, uid);

  expect(profileBefore).toBeUndefined();
  const message = buildUserRestoredEvent(
    'Driver',
    '/experience/mobile/driver-account-api',
    {
      user: {
        id: uid,
        email: email,
      },
    }
  );
  await sendMessage(JSON.stringify(message), queueUrl);
  await delay(1000);
  const profileAfter = await getUserProfile(db, uid);

  expect(profileAfter.deleted_at).toBeNull();
};

export const testDeleteEvent = async (
  db: Client,
  uid: string,
  email: string,
  queueUrl: string
) => {
  const profileBefore = await getUserProfile(db, uid);

  expect(profileBefore.deleted_at).toBeNull();
  const message = buildUserDeletedEvent(
    'Driver',
    '/experience/mobile/driver-account-api',
    {
      user: {
        id: uid,
        email: email,
      },
    }
  );
  await sendMessage(JSON.stringify(message), queueUrl);
  await delay(1000);
  const profileAfter = await getUserProfile(db, uid);

  expect(profileAfter.deleted_at).toBeDefined();
};
