import {
  CapturePaymentIntentResponse,
  CreateIntentResponse,
  CreatePaymentRequest,
  CreateRegisteredUserPaymentResponse,
  StripeCustomerService,
  StripePaymentIntentService,
  StripeSetupIntentService,
  StripeWebhookService,
} from '@experience/shared/nest/stripe';
import { CreateCustomerErrors } from './payment.errors';
import { INestApplication } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentIntentErrors } from './payment.errors';
import { PaymentService } from './payment.service';
import { PodadminSequelizeModule } from '@experience/shared/sequelize/podadmin';
import { Test, TestingModule } from '@nestjs/testing';
import { UsersApi } from '@experience/driver-account-api/api-client';
import { createMockRepository } from '../test.utils';
import request from 'supertest';

jest.mock('@experience/shared/nest/stripe');

describe('PaymentController', () => {
  let app: INestApplication;
  let module: TestingModule;
  let service: PaymentService;
  const customerId = '1s133423r5221';
  const ephemeralKey = '12345';
  const setupIntent = 'xxxxx';
  const name = 'Joe';
  const userId = 1233;
  const paymentIntent = 'sdfdsfdsf';
  const PAYMENT_REQUEST: CreatePaymentRequest = {
    amount: 80,
    currency: 'gbp',
  };
  const uid = 'uuid';
  const email = '<EMAIL>';
  const billingEventId = 123456;
  const amountToCapture = 3000;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [PodadminSequelizeModule],
      controllers: [PaymentController],
      providers: [
        PaymentService,
        StripeSetupIntentService,
        StripeCustomerService,
        StripePaymentIntentService,
        StripeWebhookService,
        {
          provide: UsersApi,
          useValue: new UsersApi(),
        },
        {
          provide: 'BILLING_ACCOUNTS_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: 'USERS_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: 'BILLING_EVENTS_REPOSITORY',
          useValue: createMockRepository(),
        },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);

    app = module.createNestApplication();

    await app.init();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('setupIntent', () => {
    it('should setup an intent', async () => {
      const response: CreateIntentResponse = {
        customer: customerId,
        ephemeralKey,
        setupIntent,
      };
      const mockCreateIntent = jest
        .spyOn(service, 'setupIntent')
        .mockResolvedValueOnce(response);
      await request(app.getHttpServer())
        .post(`/payments/setup-intent/${uid}`)
        .send({ email, name, userId })
        .expect(201, response);
      expect(mockCreateIntent).toHaveBeenCalledWith(uid);
    });

    it('should throw a 400 when setup intent fails', async () => {
      const mockCreateIntent = jest
        .spyOn(service, 'setupIntent')
        .mockRejectedValue(new CreateCustomerErrors());
      await request(app.getHttpServer())
        .post(`/payments/setup-intent/${uid}`)
        .send({ email, name, userId })
        .expect(400);
      expect(mockCreateIntent).toHaveBeenCalledWith(uid);
    });
  });

  describe('createGuestPaymentIntent', () => {
    it('should create a payment intent', async () => {
      const mockInitializeGuestCharge = jest
        .spyOn(service, 'initializeGuestCharge')
        .mockResolvedValue({ paymentIntent });

      await request(app.getHttpServer())
        .post('/payments/create-payment-intent/')
        .send(PAYMENT_REQUEST)
        .expect(201, { paymentIntent });

      expect(mockInitializeGuestCharge).toHaveBeenCalledTimes(1);
      expect(mockInitializeGuestCharge).toHaveBeenCalledWith(PAYMENT_REQUEST);
    });

    it('should throw a 400 when initialise guest charge fails', async () => {
      const mockInitializeGuestCharge = jest
        .spyOn(service, 'initializeGuestCharge')
        .mockImplementation(() => {
          throw new PaymentIntentErrors();
        });

      await request(app.getHttpServer())
        .post('/payments/create-payment-intent/')
        .send(PAYMENT_REQUEST)
        .expect(400);

      expect(mockInitializeGuestCharge).toHaveBeenCalledTimes(1);
      expect(mockInitializeGuestCharge).toHaveBeenCalledWith(PAYMENT_REQUEST);
    });
  });

  describe('createRegisteredUserPaymentIntent', () => {
    it('should create a payment intent', async () => {
      const response: CreateRegisteredUserPaymentResponse = {
        customer: customerId,
        ephemeralKey,
        paymentIntent: paymentIntent,
      };
      const mockInitializePaymentSheet = jest
        .spyOn(service, 'initializePaymentSheet')
        .mockResolvedValueOnce(response);
      await request(app.getHttpServer())
        .post(`/payments/create-payment-intent/${uid}`)
        .send(PAYMENT_REQUEST)
        .expect(201, response);
      expect(mockInitializePaymentSheet).toHaveBeenCalledWith(
        PAYMENT_REQUEST,
        uid
      );
    });

    it('should throw a 400 when initialise payment sheet fails', async () => {
      const mockCreateIntent = jest
        .spyOn(service, 'initializePaymentSheet')
        .mockRejectedValue(new PaymentIntentErrors());
      await request(app.getHttpServer())
        .post(`/payments/create-payment-intent/${uid}`)
        .send(PAYMENT_REQUEST)
        .expect(400);
      expect(mockCreateIntent).toHaveBeenCalledWith(PAYMENT_REQUEST, uid);
    });
  });

  describe('captureGuestPayment', () => {
    it('should capture a guest payment', async () => {
      const response: CapturePaymentIntentResponse = {
        paymentIntentStatus: 'succeeded',
        amountReceived: 3000,
        currency: 'gbp',
      };
      const mockCaptureIntent = jest
        .spyOn(service, 'capturePaymentIntent')
        .mockResolvedValue(response);
      await request(app.getHttpServer())
        .post('/payments/capture-guest-payment')
        .send({ billingEventId, amountToCapture })
        .expect(201, response);
      expect(mockCaptureIntent).toHaveBeenCalledWith({
        billingEventId,
        amountToCapture,
      });
    });

    it('should validate request body', async () => {
      await request(app.getHttpServer())
        .post('/payments/capture-guest-payment')
        .send({})
        .expect(400);
    });

    it('should respond with bad request when capture intent fails', async () => {
      const mockCaptureIntent = jest
        .spyOn(service, 'capturePaymentIntent')
        .mockRejectedValue(new PaymentIntentErrors());
      await request(app.getHttpServer())
        .post('/payments/capture-guest-payment')
        .send({ billingEventId, amountToCapture })
        .expect(400);
      expect(mockCaptureIntent).toHaveBeenCalledWith({
        billingEventId,
        amountToCapture,
      });
    });
  });
});
