# Payments API

## Getting started

### Build the application

Use the command

```bash
npx nx build payments-api
```

### Run the application (locally)

Use the command

```bash
npx nx run payments-api-e2e:e2e:start-mocks
npx nx serve payments-api
```

### Run e2e tests

Use the command

```bash
npx nx run payments-api-e2e:e2e
```

### Update e2e snapshots

Use the command

```bash
npx nx run payments-api-e2e:e2e -c update-snapshots
```

### Generate Openapi file

Use the command

```bash
npx nx generate-swagger payments-api
```

### Auto generate client sources

Run the following commands keep in mind that it might generate files with different indentation, so you might have a lot of files being updated but the auto linting on commit will fix it.

```bash
npx nx generate-sources payments-api-axios
```

If you don't have a JRE installed, you can run the container inside Docker.

```bash
npx nx generate-sources payments-api-axios -c docker
```

### Auto generate mocks

Use the command

```bash
npx nx generate-mocks payments-api-msw
```

---

<img src="https://d3h256n3bzippp.cloudfront.net/pod-point-logo.svg" alt="Pod Point logo" style="float: right;" />

Driving shouldn’t cost the earth 🌍

Made with ❤️&nbsp;&nbsp;at [Pod Point](https://pod-point.com)
