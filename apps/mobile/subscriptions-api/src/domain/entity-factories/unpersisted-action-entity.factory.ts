import {
  ActionEntity,
  ActionOwner,
  ActionStatus,
  ActionType,
  UnpersistedActionEntity,
} from '../entities/action.entity';

export class UnpersistedActionEntityFactory {
  static createPayupFrontFee(params: {
    subscriptionId: ActionEntity['subscriptionId'];
    dependsOn?: Pick<ActionEntity, 'id'>[];
  }) {
    return new UnpersistedActionEntity({
      status: ActionStatus.SUCCESS,
      owner: ActionOwner.USER,
      data: {
        type: ActionType.PAY_UPFRONT_FEE_V1,
      },
      subscriptionId: params.subscriptionId,
      dependsOn: (params.dependsOn ?? []).map(({ id }) => ({ id })),
    });
  }

  static createCompleteSurveyAction(params: {
    surveyUrl: string;
    subscriptionId: ActionEntity['subscriptionId'];
    dependsOn?: Pick<ActionEntity, 'id'>[];
  }): UnpersistedActionEntity {
    return new UnpersistedActionEntity({
      status: ActionStatus.PENDING,
      owner: ActionOwner.USER,
      data: {
        type: ActionType.COMPLETE_HOME_SURVEY_V1,
        surveyUrl: params.surveyUrl,
      },
      subscriptionId: params.subscriptionId,
      // incase duck typed
      dependsOn: (params.dependsOn ?? []).map(({ id }) => ({ id })),
    });
  }

  static createCreateInstallChargingStationAction(params: {
    subscriptionId: ActionEntity['subscriptionId'];
    dependsOn?: Pick<ActionEntity, 'id'>[];
  }): UnpersistedActionEntity {
    return new UnpersistedActionEntity({
      owner: ActionOwner.SYSTEM,
      status: ActionStatus.PENDING,
      data: {
        type: ActionType.INSTALL_CHARGING_STATION_V1,
        ppid: null,
      },
      subscriptionId: params.subscriptionId,
      // incase duck typed
      dependsOn: (params.dependsOn ?? []).map(({ id }) => ({ id })),
    });
  }

  static createAffordabilityCheckAction(params: {
    subscriptionId: ActionEntity['subscriptionId'];
    dependsOn?: Pick<ActionEntity, 'id'>[];
  }): UnpersistedActionEntity {
    return new UnpersistedActionEntity({
      owner: ActionOwner.USER,
      status: ActionStatus.PENDING,
      data: {
        type: ActionType.CHECK_AFFORDABILITY_V1,
        applicationId: null,
        loanId: null,
      },
      subscriptionId: params.subscriptionId,
      dependsOn: (params.dependsOn ?? []).map(({ id }) => ({ id })),
    });
  }

  static createSignDocumentsAction(params: {
    subscriptionId: ActionEntity['subscriptionId'];
    dependsOn?: Pick<ActionEntity, 'id'>[];
  }) {
    return new UnpersistedActionEntity({
      owner: ActionOwner.USER,
      status: ActionStatus.PENDING,
      data: {
        type: ActionType.SIGN_DOCUMENTS_V1,
        documents: [],
      },
      subscriptionId: params.subscriptionId,
      dependsOn: (params.dependsOn ?? []).map(({ id }) => ({ id })),
    });
  }

  static createSetupDirectDebitAction(params: {
    subscriptionId: ActionEntity['subscriptionId'];
    dependsOn?: Pick<ActionEntity, 'id'>[];
  }) {
    const unpersisted = new UnpersistedActionEntity({
      owner: ActionOwner.USER,
      status: ActionStatus.PENDING,
      data: {
        type: ActionType.SETUP_DIRECT_DEBIT_V1,
      },
      subscriptionId: params.subscriptionId,
      dependsOn: (params.dependsOn ?? []).map(({ id }) => ({ id })),
    });

    return unpersisted;
  }
}
