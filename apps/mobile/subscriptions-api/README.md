# Subscriptions API

## Getting started

This app is architected using CLEAN architecture principles.

- [Introduction to clean architecture](https://www.freecodecamp.org/news/a-quick-introduction-to-clean-architecture-990c014448d2/)
- [Guide to clean architecture](https://www.geeksforgeeks.org/complete-guide-to-clean-architecture/)
- [Clean architecture (Original Article)](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)

**Note:** there is one key difference in this implementation, the use of the `use-cases` layer. **Instead of _`use cases`_ we use _`service`_ classes** located in the `application` layer, their function and purpose are the same as use cases, BUT they group the business logic in a more logical way, instead of having a single use case per file, we can have multiple related use cases grouped in a single class.

## Actions

### Build application

Use the command

```bash
npx nx build subscriptions-api
```

### Run the application (locally)

Use the command

```bash
npx nx run subscriptions-api-e2e:e2e:start-mocks
npx nx serve subscriptions-api
```

### Run e2e tests

Use the command

```bash
npx nx run subscriptions-api-e2e:e2e
```

### Update e2e snapshots

Use the command

```bash
npx nx run subscriptions-api-e2e:e2e -c update-snapshots
```

### Generate Openapi file

Use the command

```bash
npx nx generate-swagger subscriptions-api
```

### Auto generate client sources

Run the following commands keep in mind that it might generate files with different indentation, so you might have a lot of files being updated but the auto linting on commit will fix it.

```bash
npx nx generate-sources subscriptions-api-axios
```

If you don't have a JRE installed, you can run the container inside Docker.

```bash
npx nx generate-sources subscriptions-api-axios -c docker
```

### Auto generate mocks

Use the command

```bash
npx nx generate-mocks subscriptions-api-msw
```

---

<img src="https://d3h256n3bzippp.cloudfront.net/pod-point-logo.svg" alt="Pod Point logo" style="float: right;" />

Driving shouldn’t cost the earth 🌍

Made with 💚&nbsp;&nbsp;at [Pod Point](https://pod-point.com)
