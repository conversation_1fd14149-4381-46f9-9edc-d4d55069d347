import { MigrationInterface, QueryRunner } from 'typeorm';

export class GrafanaRoUser1727963201179 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `GRANT SELECT ON ALL TABLES IN SCHEMA installer TO grafana_ro;`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `REVOKE SELECT ON ALL TABLES IN SCHEMA installer TO grafana_ro;`
    );
  }
}
