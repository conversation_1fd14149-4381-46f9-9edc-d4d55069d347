process.env.USER_QUEUE_NAME = 'migrate-users-events';
process.env.AWS_ENDPOINT_URL = 'http://127.0.0.1:4566';
process.env.USER_QUEUE_URL =
  'http://sqs.eu-west-1.localhost.localstack.cloud:4566/************/migrate-users-events';
process.env.SQS_REGION = 'eu-west-1';
process.env.AUTH_DB_HOST = 'localhost';
process.env.AUTH_DB_HOST_RO = 'localhost';
process.env.AUTH_DB_USERNAME = 'user';
process.env.AUTH_DB_PASSWORD = 'password';
process.env.AUTH_DB_DATABASE = 'auth_service';
process.env.AUTH_DB_PORT = '3309';
process.env.DB_USERNAME = 'user';
process.env.DB_PASSWORD = 'password';
process.env.DB_DATABASE = 'podpoint';
process.env.DB_HOST = 'localhost';
process.env.DB_HOST_RO = 'localhost';
process.env.DB_PORT = '3307';
process.env.DB_LOGGING = 'false';
process.env.FIREBASE_AUTH_EMULATOR_HOST = '127.0.0.1:9099';
process.env.FIREBASE_API_KEY = 'test';
process.env.FIREBASE_APP_ID = 'test';
process.env.FIREBASE_AUTH_DOMAIN = 'test';
process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>';
process.env.FIREBASE_MESSAGING_SENDER_ID = '10000000000';
process.env.FIREBASE_PRIVATE_KEY =
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
process.env.FIREBASE_PROJECT_ID = 'demo-experience';
process.env.AWS_ACCESS_KEY_ID = 'ACCESSKEY1234';
process.env.AWS_SECRET_ACCESS_KEY = 'SomeRandomStringThatMeansNothing';
process.env.AWS_REGION = 'eu-west-1';
process.env.AWS_PROFILE = 'localstack';
process.env.IDENTITY_BASE_URL_CLASSIC = 'localhost:5105';
process.env.IDENTITY_BASE_URL_POD_ENERGY = 'localhost:5105';
process.env.API3_BASE_URL = 'https://test.api-staging.pod-point.com';
process.env.DRIVER_ACCOUNT_DB_CONFIG =
  '{"migrate":{"port":5432,"host":"localhost","database":"driver_account","username":"postgres","password":"password"},"read":{"port":5432,"host":"localhost","database":"driver_account","username":"postgres","password":"password"}, "write":{"port":5432,"host":"localhost","database":"driver_account","username":"postgres","password":"password"}}';
process.env.SEGMENT_WRITE_KEY = 'abcdef';
process.env.AUTH_SERVICE_USER_URL =
  'https://auth-staging.pod-point.com/api/v1/user';
process.env.USER_EVENTS_QUEUE_URL =
  'http://sqs.eu-west-1.localhost.localstack.cloud:4566/************/driver-account-api-user-profile-events';
process.env.BILLING_API_BASE_URL = 'http://localhost:5108';
