import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  Configuration,
  NotificationsApi,
  UsersApi,
} from '@experience/driver-account-api/api-client';
import { Module } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { SqsConsumerWrapperModule } from '@experience/shared/nest/aws/sqs-module';

@Module({
  imports: [
    ConfigModule,
    SqsConsumerWrapperModule.register(
      'NOTIFICATIONS_EVENTS_QUEUE_URL',
      'NOTIFICATION_QUEUE_CONSUMER'
    ),
    SqsConsumerWrapperModule.register(
      'USER_CREDENTIALS_INVALIDATED_EVENTS_QUEUE_URL',
      'USER_CREDENTIALS_INVALIDATED_QUEUE_CONSUMER'
    ),
    SqsConsumerWrapperModule.register(
      'VEHICLE_INTERVENTION_EVENTS_QUEUE_URL',
      'VEHICLE_INTERVENTION_QUEUE_CONSUMER'
    ),
  ],
  providers: [
    NotificationsService,
    {
      provide: NotificationsApi,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        new NotificationsApi(
          new Configuration({
            basePath: configService.get('DRIVER_ACCOUNT_API_BASE_URL'),
          })
        ),
    },
    {
      provide: UsersApi,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        new UsersApi(
          new Configuration({
            basePath: configService.get('DRIVER_ACCOUNT_API_BASE_URL'),
          })
        ),
    },
  ],
})
export class NotificationsModule {}
