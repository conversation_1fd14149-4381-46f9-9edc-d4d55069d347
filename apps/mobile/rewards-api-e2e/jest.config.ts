/* eslint-disable */
export default {
  displayName: 'rewards-api-e2e',
  preset: '../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
      },
    ],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  setupFiles: ['<rootDir>/jest.env.ts'],
  coverageDirectory: '../../../coverage/rewards-api-e2e',
};
