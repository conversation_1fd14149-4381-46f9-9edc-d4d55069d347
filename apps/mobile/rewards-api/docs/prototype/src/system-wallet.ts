import { BankAccount } from './bank-account';
import { Currency } from './types';
import { MILES_TO_GBP_EXCHANGE_RATE, SYSTEM_USER_ID } from './constants';
import { RewardsSystem } from './rewards-system';
import { SystemMilesAccount } from './system-miles-account';
import { UserWallet } from './user-wallet';
import { Wallet } from './wallet';

export class SystemWallet extends Wallet {
  milesAccount: SystemMilesAccount;
  bankAccount: BankAccount;

  constructor(rewardsSystem: RewardsSystem) {
    super(SYSTEM_USER_ID, rewardsSystem);
    this.milesAccount = new SystemMilesAccount(this);
    this.bankAccount = new BankAccount(this);
  }

  addMilesToAllowance(userAccount: UserWallet, amount: number) {
    this.milesAccount.transfer(
      userAccount.allowanceAccount,
      amount,
      Currency.MILES,
      'Add miles to allowance'
    );
  }

  addRewardMiles(userAccount: UserWallet, amount: number) {
    this.milesAccount.transfer(
      userAccount.rewardsAccount,
      amount,
      Currency.MILES,
      'Add reward miles'
    );
  }

  withdraw(userAccount: UserWallet, amount: number) {
    userAccount.rewardsAccount.transfer(
      this.milesAccount,
      amount,
      Currency.MILES,
      'Rewards withdrawal'
    );
    // convert miles to GBP and transfer to the user's bank account
    const gbpAmount =
      Math.round((amount * MILES_TO_GBP_EXCHANGE_RATE + Number.EPSILON) * 100) /
      100;
    this.bankAccount.transfer(
      userAccount.bankAccount,
      gbpAmount,
      Currency.GBP,
      'Rewards withdrawal'
    );
  }

  getBalances() {
    return {
      bank: this.bankAccount.getBalance(),
      miles: this.milesAccount.getBalance(),
    };
  }
}
