import {
  UnpersistedWalletEntity,
  WalletEntity,
  WalletEntityType,
} from '../../domain/entities/wallet.entity';
import { WalletRepositoryInterface } from '../../domain/repositories/wallet.repository.interface';
import { v4 } from 'uuid';

export class NoOpWalletRepository implements WalletRepositoryInterface {
  async read(): Promise<WalletEntity | null> {
    return null;
  }

  async create(entity: UnpersistedWalletEntity): Promise<WalletEntity> {
    return new WalletEntity({
      id: v4(),
      userId: v4(),
      subscriptionId: entity.subscriptionId,
      type: entity.type,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });
  }

  async getByUserId(userId: string): Promise<WalletEntity | null> {
    return new WalletEntity({
      id: v4(),
      userId,
      subscriptionId: v4(),
      type: WalletEntityType.POD_DRIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });
  }

  async update(
    entity: WalletEntity,
    fields: Partial<WalletEntity>
  ): Promise<WalletEntity> {
    return new WalletEntity({
      ...entity,
      ...fields,
    });
  }

  async getByType(): Promise<WalletEntity[]> {
    return [];
  }
}
