import {
  AccountEntity,
  AccountEntityType,
} from '../../domain/entities/account.entity';
import { Injectable } from '@nestjs/common';
import {
  InvalidEntityError,
  Transformer,
} from '@experience/mobile/clean-architecture';
import { RewardsAccount } from '@experience/mobile/driver-account/database';

@Injectable()
export class AccountTransformer extends Transformer<
  RewardsAccount,
  AccountEntity
> {
  /**
   * @throws InvalidEntityError
   */
  transform(input: RewardsAccount): AccountEntity {
    function assertTypeIsValid(
      type: unknown
    ): asserts type is AccountEntityType {
      // eslint-disable-next-line unicorn/prefer-includes
      if (Object.values(AccountEntityType).some((val) => val === type)) {
        return;
      }

      throw new InvalidEntityError({ id: input.id, type }, 'type is invalid');
    }

    assertTypeIsValid(input.type);

    return new AccountEntity({
      id: input.id,
      type: input.type,
      rewardWalletId: input.rewardWalletId,
      createdAt: input.createdAt,
      updatedAt: input.updatedAt,
      deletedAt: input.deletedAt ?? null,
    });
  }
}
