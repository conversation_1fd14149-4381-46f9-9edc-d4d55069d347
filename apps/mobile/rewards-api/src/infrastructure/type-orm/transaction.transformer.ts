import {
  InvalidEntityError,
  Transformer,
} from '@experience/mobile/clean-architecture';
import { RewardsTransaction } from '@experience/mobile/driver-account/database';
import {
  TransactionEntity,
  TransactionEntityCurrency,
  TransactionEntityMetadata,
  TransactionEntityStatus,
} from '../../domain/entities/transaction.entity';
import { ZodOptional, ZodString, z } from 'zod';

const TransactionMetadataValidator = z.object({
  chargeId: z.string().optional(),
  paymentTransactionId: z.string().optional(),
  rewardsTransactionId: z.string().optional(),
} satisfies {
  [Key in keyof TransactionEntityMetadata]: ZodOptional<ZodString>;
});

export class TransactionTransformer extends Transformer<
  RewardsTransaction,
  TransactionEntity
> {
  transform(input: RewardsTransaction): TransactionEntity {
    if (!this.isValidCurrency(input.currency)) {
      throw new InvalidEntityError(
        { id: input.id, currency: input.currency },
        'currency is invalid'
      );
    }

    if (!this.isValidStatus(input.status)) {
      throw new InvalidEntityError(
        { id: input.id, status: input.status },
        'status is invalid'
      );
    }

    if (input.metadata && !this.isValidMetadata(input.metadata)) {
      throw new InvalidEntityError(
        { id: input.id, metadata: input.metadata },
        'metadata is invalid'
      );
    }

    return {
      id: input.id,
      accountId: input.accountId,
      amount: input.amount,
      currency: input.currency,
      status: input.status,
      reference: input.reference ?? null,
      transactionHash: input.transactionHash,
      transactionDate: input.transactionDate,
      idempotencyKey: input.idempotencyKey,
      metadata: input.metadata ?? null,
      createdAt: input.createdAt,
      updatedAt: input.updatedAt,
      deletedAt: input.deletedAt ?? null,
    };
  }

  private isValidCurrency(
    currency: unknown
  ): currency is TransactionEntityCurrency {
    return (
      typeof currency === 'string' &&
      Object.values(TransactionEntityCurrency).includes(currency as never)
    );
  }

  private isValidStatus(status: unknown): status is TransactionEntityStatus {
    return (
      typeof status === 'string' &&
      Object.values(TransactionEntityStatus).includes(status as never)
    );
  }

  private isValidMetadata(
    metadata: unknown
  ): metadata is TransactionEntityMetadata {
    return TransactionMetadataValidator.safeParse(metadata).success;
  }
}
