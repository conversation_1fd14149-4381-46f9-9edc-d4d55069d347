import { ConfigModule } from '@nestjs/config';
import { Module } from '@nestjs/common';
import { PaymentsService } from '../application/services/payments.service';
import { SqsClientModule } from '@experience/shared/nest/aws/sqs-module';

@Module({
  imports: [
    ConfigModule,
    SqsClientModule.register(process.env.AWS_ENDPOINT_URL),
  ],
  providers: [PaymentsService],
  exports: [PaymentsService],
})
export class PaymentsModule {}
