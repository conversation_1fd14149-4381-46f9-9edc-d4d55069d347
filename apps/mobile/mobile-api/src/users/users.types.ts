import { ApiProperty } from '@nestjs/swagger';
import { LinkUserResponse } from '@experience/shared/axios/enode-api-client';

export class GetLinkSessionRequest {
  @ApiProperty({
    example: 'VOLVO',
    description:
      'By specifying a vendor, the brand selection step in Link UI will be skipped. Instead, your user will go directly to the service selection view (if applicable for the specified vendor), or to the review data access step.',
    required: false,
  })
  vendor?: string;

  @ApiProperty({
    example: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    description:
      'A unique identifier identifying the user to be used with the link session generated at the capture vehicle stage',
    required: false,
  })
  enodeUserId?: string;
}

export class GetLinkSessionResponse implements LinkUserResponse {
  @ApiProperty({
    example: 'WFiMjNjZDRANThlZmIyMzMtYTNjNS00Njk...',
  })
  linkToken: string;

  @ApiProperty({
    example: 'https://example.com',
  })
  linkUrl: string;
}
