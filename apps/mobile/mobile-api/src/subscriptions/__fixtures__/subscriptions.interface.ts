import {
  OrderDTOOriginEnum,
  PersistedSubscriptionDTO,
} from '@experience/mobile/subscriptions-api/axios';
import {
  SubscriptionCheckAffordabilityActionDTO,
  SubscriptionConfirmationOfPayeeDTO,
  SubscriptionDirectDebitDTO,
  SubscriptionDocumentsDTO,
} from '../subscriptions.types';

export const MOCK_AUTH_ID = '43a4a23e-e385-458f-80ce-287d77dc59e2';

export const MOCK_SUBSCRIPTION: PersistedSubscriptionDTO = {
  id: 'f4fe18a2-5283-47f8-9cfb-976d4fa4ee0b',
  status: 'ACTIVE',
  userId: MOCK_AUTH_ID,
  actions: [],
  order: {
    id: 'f658c722-528b-4161-b31e-22f5cd423adc',
    orderedAt: '2025-01-01T00:00:00.000Z',
    origin: OrderDTOOriginEnum.Salesforce,
    address: {
      line1: "222 Gray's Inn Road",
      line2: null,
      line3: null,
      postcode: 'WC1X 8HB',
    },
    email: '<EMAIL>',
    firstName: 'Mobile',
    lastName: 'Tester',
    mpan: 'S 01 801 101 22 6130 5588 165',
    phoneNumber: '02072474114',
    eCommerceId: 'dr3l2e8dhu::2415',
  },
  plan: {
    id: 'bdd9e0e1-c4c7-4c7a-9737-a431dabd49fb',
    type: 'POD_DRIVE',
    productCode: 'example',
    allowanceMiles: 10_000,
    allowancePeriod: 'ANNUAL',
    upfrontFeePounds: 99,
    monthlyFeePounds: 30,
    contractDurationMonths: 18,
    ratePencePerMile: 2.8,
    rateMilesPerKwh: 3.5,
    milesRenewalDate: '2026-02-01T00:00:00.000Z',
  },
  deletedAt: null,
  createdAt: '2025-02-01T00:00:00.000Z',
  updatedAt: '2025-02-01T00:00:00.000Z',
  activatedAt: '2025-02-01T00:00:00.000Z',
};

export const MOCK_ACTION: SubscriptionCheckAffordabilityActionDTO = {
  id: '2cd5781c-9d4b-408f-bce5-9b830f10109a',
  subscriptionId: MOCK_SUBSCRIPTION.id,
  owner: 'USER',
  status: 'PENDING',
  type: 'CHECK_AFFORDABILITY_V1',
  dependsOn: [],
  data: {
    applicationId: null,
    loanId: null,
  },
};

export const MOCK_COP_REQUEST: SubscriptionConfirmationOfPayeeDTO = {
  accountName: 'Mr Pod Point',
  accountNumber: '********',
  sortCode: '010203',
};

export const MOCK_SUBSCRIPTION_DIRECT_DEBIT: SubscriptionDirectDebitDTO = {
  accountNumberLastDigits: '1234',
  sortCodeLastDigits: '12',
  nameOnAccount: 'Mr Tom Wallace',
  monthlyPaymentDay: 13,
};

export const MOCK_SUBSCRIPTION_DOCUMENTS: SubscriptionDocumentsDTO = {
  documents: [
    {
      issued: '2025-05-06T16:30:00.000Z',
      active: true,
      format: 'PDF',
      type: 'ha',
      link: '/subscriptions/8f331f13-d7e9-4487-b194-1889532e984e/documents/LMS-A0838D19-ED4H-1HD0-6F21-976J0ABCC627',
    },
  ],
};
