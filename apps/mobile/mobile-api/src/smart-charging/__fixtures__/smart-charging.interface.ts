import {
  CreateVehicleLinkRequestDto,
  DelegatedControlChargingStationResponseDto,
  ExtendedVehicleLinksResponseDto,
  SetDelegatedControlIntentsResponseDto,
  UpdateVehicleByPpidAndVehicleId200Response,
  UpdateVehicleLinkRequestDto,
  VehicleIntentsRequestDto,
  VehicleLinkResponseDto,
} from '@experience/shared/axios/smart-charging-service-client';
import { SmartChargingPreferencesDTO } from '../smart-charging.types';

export const TEST_GET_VEHICLES_RESPONSE: ExtendedVehicleLinksResponseDto = {
  data: [
    {
      id: 'string',
      isPluggedInToThisCharger: true,
      vehicle: {
        id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
        enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
        enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
        vehicleInformation: {
          brand: 'Polestar',
          model: '2',
          modelVariant: 'Long range',
          vehicleRegistrationPlate: 'ABC123',
          displayName: 'My car',
        },
        chargeState: {
          batteryCapacity: 78,
          batteryLevelPercent: 78,
          chargeLimitPercent: 78,
          chargeRate: 78,
          chargeTimeRemaining: 78,
          isCharging: true,
          isFullyCharged: true,
          isPluggedIn: true,
          lastUpdated: '2021-08-12T09:00:00Z',
          maxCurrent: 32,
          powerDeliveryState: 'UNPLUGGED',
          range: 300,
        },
      },
      intents: {
        id: 'string',
        details: [
          {
            chargeByTime: '07:00:00',
            chargeKWh: 28,
            dayOfWeek: 'MONDAY',
          },
        ],
        maxPrice: 0.23,
      },
      currentIntent: {
        canMeetTarget: true,
        cannotMeetTargetReason: 'PRICE',
        chargeDetail: {
          expectedChargeByTarget_kWh: 0,
          expectedChargeByTargetPercent: 0,
          fullChargeByTime: '2024-10-07T10:51:10.415Z',
        },
        chargingStation: {
          ppid: 'PSL-123456',
        },
        tariffType: 'FLAT',
        needsPeakToMeetTarget: false,
        readyByTime: '',
      },
    },
  ],
};

export const TEST_GET_INTENTS_RESPONSE: DelegatedControlChargingStationResponseDto =
  {
    id: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    ppid: '1234567890',
    createdAt: '2021-01-01T00:00:00Z',
    status: 'ACTIVE',
    vehicleLinks: [
      {
        id: 'string',
        isPluggedInToThisCharger: true,
        vehicle: {
          id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
          enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
          enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
          vehicleInformation: {
            brand: 'Polestar',
            model: '2',
            modelVariant: 'Long range',
            vehicleRegistrationPlate: 'ABC123',
            displayName: 'My car',
          },
          chargeState: {
            batteryCapacity: 78,
            batteryLevelPercent: 78,
            chargeLimitPercent: 78,
            chargeRate: 78,
            chargeTimeRemaining: 78,
            isCharging: true,
            isFullyCharged: true,
            isPluggedIn: true,
            lastUpdated: '2021-08-12T09:00:00Z',
            maxCurrent: 32,
            powerDeliveryState: 'UNPLUGGED',
            range: 300,
          },
        },
        intents: {
          id: 'string',
          details: [
            {
              chargeByTime: '07:00:00',
              chargeKWh: 28,
              dayOfWeek: 'MONDAY',
            },
          ],
          maxPrice: 0.25,
        },
      },
    ],
  };

export const TEST_CAPTURE_VEHICLE_REQUEST: CreateVehicleLinkRequestDto = {
  vehicle: {
    vehicleInformation: {
      brand: 'Polestar',
      model: '2',
      modelVariant: 'Long range',
      vehicleRegistrationPlate: 'ABC123',
      displayName: 'My car',
    },
    chargeState: {
      batteryCapacity: 78,
    },
    enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
  },
  intents: [
    {
      chargeByTime: '07:00:00',
      chargeKWh: 28,
      dayOfWeek: 'MONDAY',
    },
  ],
};

export const TEST_CAPTURE_VEHICLE_REQUEST_NO_MODEL_VARIANT: CreateVehicleLinkRequestDto =
  {
    vehicle: {
      vehicleInformation: {
        brand: 'Polestar',
        model: '2',
        vehicleRegistrationPlate: 'ABC123',
        displayName: 'My car',
      },
      chargeState: {
        batteryCapacity: 78,
      },
      enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    },
    intents: [
      {
        chargeByTime: '07:00:00',
        chargeKWh: 28,
        dayOfWeek: 'MONDAY',
      },
    ],
  };

export const TEST_CAPTURE_VEHICLE_RESPONSE: VehicleLinkResponseDto = {
  id: 'string',
  isPluggedInToThisCharger: true,
  vehicle: {
    id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    vehicleInformation: {
      brand: 'Polestar',
      model: '2',
      modelVariant: 'Long range',
      vehicleRegistrationPlate: 'ABC123',
      displayName: 'My car',
    },
    chargeState: {
      batteryCapacity: 78,
      batteryLevelPercent: 78,
      chargeLimitPercent: 78,
      chargeRate: 78,
      chargeTimeRemaining: 78,
      isCharging: true,
      isFullyCharged: true,
      isPluggedIn: true,
      lastUpdated: '2021-08-12T09:00:00Z',
      maxCurrent: 32,
      powerDeliveryState: 'UNPLUGGED',
      range: 300,
    },
  },
  intents: {
    id: 'string',
    details: [
      {
        chargeByTime: '07:00:00',
        chargeKWh: 28,
        dayOfWeek: 'MONDAY',
      },
    ],
    maxPrice: 0.25,
  },
};

export const TEST_UPDATE_VEHICLE_REQUEST: UpdateVehicleLinkRequestDto = {};

export const TEST_UPDATE_VEHICLE_RESPONSE: UpdateVehicleByPpidAndVehicleId200Response =
  {
    id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    vehicleInformation: {
      brand: 'Polestar',
      model: '2',
      modelVariant: 'Long range',
      vehicleRegistrationPlate: 'ABC123',
      displayName: 'My car',
    },
    chargeState: {
      batteryCapacity: 78,
    },
  };

export const TEST_SET_VEHICLE_INTENT_REQUEST: VehicleIntentsRequestDto = {
  intentDetails: [
    {
      chargeByTime: '07:00:00',
      chargeKWh: 28,
      dayOfWeek: 'MONDAY',
    },
  ],
};

export const TEST_SET_VEHICLE_INTENT_RESPONSE: SetDelegatedControlIntentsResponseDto =
  {
    createdAt: '2024-07-31T12:34:56.789Z',
    updatedAt: '2024-07-31T12:34:56.789Z',
    delegatedControlChargingStationVehicleId:
      '69aed230-4f2b-4249-95b2-b69cb2ee65b0',
    id: 'c2ddbfd6-6bf6-48e5-b248-c38416f0b090',
    intentDetails: [
      {
        chargeByTime: '07:00:00',
        chargeKWh: 28,
        dayOfWeek: 'MONDAY',
      },
    ],
    maxPrice: 0.25,
  };

export const MOCK_PREFERENCES: SmartChargingPreferencesDTO = {
  maxPrice: 10,
};
