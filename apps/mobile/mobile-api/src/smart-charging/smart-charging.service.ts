import { Cache } from '@nestjs/cache-manager';
import {
  ChargersAndVehicles,
  DelegatedControlChargingStationResponseDtoImpl,
  SmartChargingPreferencesDTO,
  VehicleData,
  VehicleLinkResponseDtoImpl,
} from './smart-charging.types';
import { ConfigService } from '@nestjs/config';
import {
  CreateVehicleLinkRequestDto,
  DelegatedControlChargingStationResponseDtoStatusEnum,
  DelegatedControlChargingStationsApi,
  DelegatedControlIntentsApi,
  DelegatedControlVehiclesApi,
  SetDelegatedControlIntentsResponseDto,
  UpdateVehicleByPpidAndVehicleId200Response,
  UpdateVehicleLinkRequestDto,
  VehicleIntentsRequestDto,
} from '@experience/shared/axios/smart-charging-service-client';
import { DelegatedControlStatus } from '../chargers/chargers.types';
import { ITokenAuthUser } from '@experience/mobile/nest/authorisation';
import { Injectable, Logger } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { v4 } from 'uuid';
import axios, { AxiosError } from 'axios';

export const CACHE_TTL = 10 * 60 * 1000; // 10 minutes
export const VEHICLES_CACHE_KEY = 'vehicles';

@Injectable()
export class SmartChargingService {
  private readonly logger = new Logger(SmartChargingService.name);
  constructor(
    private readonly delegatedControlChargingStationsApi: DelegatedControlChargingStationsApi,
    private readonly delegatedControlVehiclesApi: DelegatedControlVehiclesApi,
    private readonly delegatedControlsIntentsApi: DelegatedControlIntentsApi,
    private readonly usersService: UsersService,
    private readonly config: ConfigService,
    private readonly cacheManager: Cache
  ) {}

  async getChargersAndVehicles(
    user: ITokenAuthUser
  ): Promise<ChargersAndVehicles[]> {
    this.logger.log(
      { userUuid: user.uuid },
      'retrieving chargers and vehicles'
    );

    try {
      const ppids =
        (await this.usersService.getUserChargers(user.uuid))?.map(
          (wrapper) => wrapper.ppid
        ) ?? [];

      this.logger.log(
        {
          userUuid: user.uuid,
          ppids,
        },
        'got users chargers'
      );

      const res = await Promise.all(
        ppids.map(async (ppid) => {
          const {
            data: { data: vehicles },
          } =
            await this.delegatedControlVehiclesApi.getDelegatedControlChargingStationVehicles(
              ppid
            );
          return {
            ppid,
            vehicles: [...vehicles].map((veh, index) => ({
              ...veh,
              // SCSA no longer returns isPrimary, this is a stub to prevent the app breaking in the meantime
              isPrimary: index === 0,
            })),
          };
        })
      );

      this.logger.log(
        {
          userUuid: user.uuid,
          vehicles: res.map(({ ppid, vehicles }) => ({
            ppid,
            vehicles: vehicles.map(({ id }) => ({ id })),
          })),
        },
        'got charger vehicles'
      );

      return res;
    } catch (error) {
      this.logger.error(
        { user, error },
        'error retrieving chargers and vehicles'
      );

      throw error;
    }
  }

  async getDelegatedControlChargerStatus(
    ppid: string
  ): Promise<DelegatedControlStatus> {
    this.logger.log({ ppid }, 'retrieving charger delegated control status');

    try {
      const result =
        await this.delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid(
          ppid
        );

      this.logger.log(
        { ppid, httpStatus: result.status },
        'retrieved charger delegated control status'
      );

      let { status } = result.data;

      if (
        status ===
        DelegatedControlChargingStationResponseDtoStatusEnum.Candidate
      ) {
        status = DelegatedControlChargingStationResponseDtoStatusEnum.Inactive;
      }

      return status;
    } catch (error) {
      if (error instanceof AxiosError && error.response) {
        if (error.response.status === 404) {
          this.logger.log(
            { ppid },
            'charger delegated control status returned 404, returning unknown'
          );

          return DelegatedControlChargingStationResponseDtoStatusEnum.Unknown;
        }
      }

      this.logger.error(
        { ppid, error },
        'error retrieving charger delegated control status'
      );

      throw error;
    }
  }

  async captureVehicle(
    ppid: string,
    request: CreateVehicleLinkRequestDto
  ): Promise<VehicleLinkResponseDtoImpl> {
    this.logger.log({ ppid, request }, 'capturing vehicle');

    try {
      const enodeCompatible = await this.isVehicleEnodeCompatible(request);

      request.vehicle.enodeUserId = enodeCompatible
        ? v4()
        : request.vehicle.enodeUserId;

      this.logger.log(
        { enodeCompatible, request },
        'calling smart charging service API to add vehicle'
      );

      const response =
        await this.delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation(
          ppid,
          request
        );

      // SCSA no longer returns isPrimary, this is a stub to prevent the app breaking in the meantime
      return { ...response.data, isPrimary: true };
    } catch (error) {
      this.logger.error({ ppid, request, error }, 'error capturing vehicle');

      throw error;
    }
  }

  async isVehicleEnodeCompatible(request: CreateVehicleLinkRequestDto) {
    try {
      const modelId = [
        request.vehicle.vehicleInformation.brand,
        request.vehicle.vehicleInformation.model,
      ]
        .join('-')
        .replace(/\s/g, '-')
        .normalize('NFD')
        .replace(/\p{Diacritic}/gu, '')
        .toLowerCase();

      const variantName =
        request.vehicle.vehicleInformation.modelVariant ?? null;

      let vehicles: VehicleData = await this.cacheManager.get<VehicleData>(
        VEHICLES_CACHE_KEY
      );

      console.log('DEBUG: Cache lookup result:', JSON.stringify(vehicles, null, 2));

      if (!vehicles) {
        console.log('DEBUG: No cache data found, making HTTP request');
        const result = await axios.get(
          this.config.get('ENODE_VEHICLE_JSON_URL')
        );

        vehicles = result.data;

        await this.cacheManager.set(VEHICLES_CACHE_KEY, vehicles, CACHE_TTL);
      } else {
        console.log('DEBUG: Using cached data');
      }

      this.logger.log(
        { modelId, variantName },
        'searching vehicle JSON for enode compatability'
      );

      return (
        vehicles.modelVariants.find(
          (value) => value.modelId === modelId && value.name === variantName
        )?.enodeCompatible || false
      );
    } catch (error) {
      this.logger.error(
        { error },
        'error identifying vehicle from vehicles data'
      );

      throw error;
    }
  }

  async getIntents(
    ppid: string
  ): Promise<DelegatedControlChargingStationResponseDtoImpl> {
    try {
      this.logger.log({ ppid }, 'retrieving intents');

      const response =
        await this.delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid(
          ppid
        );

      const { data } = response;

      if (
        data.status ===
        DelegatedControlChargingStationResponseDtoStatusEnum.Candidate
      ) {
        data.status =
          DelegatedControlChargingStationResponseDtoStatusEnum.Inactive;
      }

      return {
        ...data,
        vehicleLinks: data.vehicleLinks.map((vl) => ({
          ...vl,
          isPrimary: true,
        })),
      };
    } catch (error) {
      this.logger.error({ ppid, error }, 'error retrieving intents');

      throw error;
    }
  }

  async createDelegatedControlStation(ppid: string) {
    try {
      this.logger.log({ ppid }, 'calling create delegated control station');

      const response =
        await this.delegatedControlChargingStationsApi.createDelegatedControlChargingStation(
          ppid,
          {
            providerName: 'axle',
          }
        );

      return response.data;
    } catch (error) {
      this.logger.error(
        { ppid, error },
        'error calling create delegated control station'
      );

      throw error;
    }
  }

  async updateVehicle(
    ppid: string,
    vehicleId: string,
    update: UpdateVehicleLinkRequestDto
  ): Promise<UpdateVehicleByPpidAndVehicleId200Response> {
    this.logger.log(
      { ppid, vehicleId, update },
      'attempting to update vehicle'
    );

    try {
      const res =
        await this.delegatedControlVehiclesApi.updateVehicleByPpidAndVehicleId(
          ppid,
          vehicleId,
          update
        );

      this.logger.log(
        { ppid, vehicleId, update, updateRes: res.data },
        'successfully updated vehicle'
      );

      return res.data;
    } catch (error) {
      this.logger.error(
        {
          ppid,
          vehicleId,
          update,
          error,
        },
        'failed to update vehicle'
      );

      throw error;
    }
  }

  async setVehicleIntents(
    ppid: string,
    vehicleId: string,
    intentDetails: VehicleIntentsRequestDto
  ): Promise<SetDelegatedControlIntentsResponseDto> {
    this.logger.log(
      {
        ppid,
        vehicleId,
        intentDetails,
      },
      'attempting to set intent for vehicle'
    );

    try {
      const res =
        await this.delegatedControlsIntentsApi.setDelegatedControlIntents(
          ppid,
          vehicleId,
          intentDetails
        );

      this.logger.log(
        {
          ppid,
          vehicleId,
          intentDetails,
          setIntent: res.data,
        },
        'successfully set intent for vehicle'
      );

      return res.data;
    } catch (error) {
      this.logger.error(
        {
          ppid,
          vehicleId,
          intentDetails,
          error,
        },
        'failed to set intent for vehicle'
      );

      throw error;
    }
  }

  async deleteVehicle(ppid: string, vehicleId: string) {
    this.logger.log(
      {
        ppid,
        vehicleId,
      },
      'attempting to delete vehicle'
    );

    return await this.delegatedControlVehiclesApi
      .unlinkVehicleFromDelegatedControlChargingStation(ppid, vehicleId)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error(
          {
            ppid,
            vehicleId,
            error,
          },
          'failed to delete vehicle'
        );
        throw error;
      });
  }

  async removeChargerFromDelegatedControl(ppid: string) {
    this.logger.log(
      {
        ppid,
      },
      'attempting to remove charger from delegated control'
    );

    try {
      await this.delegatedControlChargingStationsApi.deleteDelegatedControlChargingStationByPpid(
        ppid
      );

      this.logger.log(
        {
          ppid,
        },
        'successfully removed charger from delegated control'
      );
    } catch (error) {
      this.logger.error(
        { ppid, error },
        'failed to remove charger from delegated control'
      );

      throw error;
    }
  }

  async getPreferences(ppid: string): Promise<SmartChargingPreferencesDTO> {
    this.logger.log({ ppid }, 'getting charger delegated controls preferences');

    try {
      const {
        data: { preferences },
      } =
        await this.delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid(
          ppid
        );

      if (!preferences) {
        this.logger.log(
          { ppid },
          'charger has no delegated controls preferences'
        );

        return {
          maxPrice: null,
        };
      }

      this.logger.log(
        { ppid, preferences },
        'got charger delegated controls preferences'
      );

      return preferences;
    } catch (error) {
      this.logger.error(
        { ppid, error },
        'unable to get charger delegated controls preferences'
      );

      throw error;
    }
  }

  async updatePreferences(
    ppid: string,
    updatedPreferences: SmartChargingPreferencesDTO
  ): Promise<void> {
    this.logger.log(
      { ppid, updatedPreferences },
      'attempting to update charger delegated controls preferences'
    );

    try {
      await this.delegatedControlChargingStationsApi.updateDelegatedControlChargingStationPreferences(
        ppid,
        updatedPreferences
      );

      this.logger.log(
        { ppid, updatedPreferences },
        'updated charger delegated controls preferences'
      );
    } catch (error) {
      this.logger.error(
        { ppid, error },
        'unable to update charger delegated controls preferences'
      );

      throw error;
    }
  }
}
