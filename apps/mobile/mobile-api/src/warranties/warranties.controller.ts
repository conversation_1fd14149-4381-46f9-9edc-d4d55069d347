import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import {
  ApiInternalServerException,
  ApiUnauthorizedException,
} from '@experience/mobile/nest/swagger';
import { AuthGuardToken } from '../authentication/authguard.token';
import {
  Controller,
  Get,
  Param,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { WarrantiesInterceptor } from './warranties.interceptor';
import { WarrantiesService } from './warranties.service';
import { WarrantyDTO } from './warranties.types';

@ApiBearerAuth()
@ApiTags('Warranties')
@Controller('warranties')
@UseGuards(AuthGuardToken)
@UseInterceptors(WarrantiesInterceptor)
export class WarrantiesController {
  constructor(private readonly warrantiesService: WarrantiesService) {}

  @ApiOperation({
    description: 'Retrieves the warranty associated with a given PPID',
  })
  @ApiOkResponse({
    type: WarrantyDTO,
  })
  @ApiParam({
    name: 'ppid',
    description: 'The PPID to retrieve the warranty for',
  })
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  @Get(':ppid')
  async getWarranty(@Param('ppid') ppid: string): Promise<WarrantyDTO> {
    return await this.warrantiesService.getWarranty(ppid);
  }
}
