import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiNotImplementedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { AuthGuardToken } from '../authentication/authguard.token';
import { AxiosHttpExceptionFilter } from '@experience/mobile/nest/exception';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  Injectable,
  Param,
  Post,
  UseFilters,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ChargersInterceptor } from '../chargers/chargers.interceptor';
import { LinkyDTO, SetLinkyDTO } from './linky.types';
import { LinkyService } from './linky.service';
import { UserUnitParamGuard } from '../authentication/user-unit-param.guard';

@ApiTags('Linky')
@Controller('linky')
@Injectable()
@ApiBearerAuth()
@UseInterceptors(ChargersInterceptor)
@UseFilters(AxiosHttpExceptionFilter)
export class LinkyController {
  constructor(private readonly linkyService: LinkyService) {}

  @Get(':ppid')
  @UseGuards(AuthGuardToken, UserUnitParamGuard)
  @ApiParam({
    name: 'ppid',
    description: 'PPID of a given charger',
    example: 'PSL-123456',
  })
  @ApiOkResponse({
    description: 'Returns the status of Linky for a given charger',
    type: LinkyDTO,
  })
  @ApiUnauthorizedResponse({
    description: 'Returned when the request is not authenticated',
  })
  @ApiForbiddenResponse({
    description: 'Returned when the user does not own the given charger',
  })
  @ApiNotFoundResponse({
    description: 'Returned when the given charger is not found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an unknown error occurs',
  })
  async getLinkyStatusForCharger(
    @Param('ppid') ppid: string
  ): Promise<LinkyDTO> {
    return this.linkyService.getLinkyStatusForCharger(ppid);
  }

  @Post(':ppid')
  @HttpCode(200)
  @UseGuards(AuthGuardToken, UserUnitParamGuard)
  @ApiParam({
    name: 'ppid',
    description: 'PPID of a given charger',
  })
  @ApiOkResponse({
    description: 'Returns the status of Linky for a given charger',
    type: LinkyDTO,
  })
  @ApiUnauthorizedResponse({
    description: 'Returned when the request is not authenticated',
  })
  @ApiForbiddenResponse({
    description: 'Returned when the user does not own the given charger',
  })
  @ApiNotFoundResponse({
    description: 'Returned when the given charger is not found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an unknown error occurs',
  })
  @ApiNotImplementedResponse({
    description: 'Returned when a charger is not Linky capable',
  })
  async setLinkyStatusForCharger(
    @Param('ppid') ppid: string,
    @Body() { scheduleEnabled }: SetLinkyDTO
  ): Promise<LinkyDTO> {
    return this.linkyService.setLinkyStatusForCharger(ppid, scheduleEnabled);
  }
}
