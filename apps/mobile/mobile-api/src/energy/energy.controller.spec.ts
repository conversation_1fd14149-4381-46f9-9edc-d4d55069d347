import { AuthenticationModule } from '../authentication/authentication.module';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { EnergyController } from './energy.controller';
import { EnergyService } from './energy.service';
import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { UsersService } from '../users/users.service';
import { mockAuthenticatedUser } from '../test.utils';
import request from 'supertest';

describe('EnergyController', () => {
  let app: INestApplication;
  let energyService: DeepMocked<EnergyService>;
  let token: string;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AuthenticationModule],
      controllers: [EnergyController],
      providers: [
        {
          provide: UsersService,
          useValue: createMock<UsersService>(),
        },
        {
          provide: EnergyService,
          useValue: createMock<EnergyService>(),
        },
      ],
    }).compile();

    app = module.createNestApplication();

    energyService = module.get(EnergyService);

    await app.init();
  });

  beforeEach(() => {
    jest.resetAllMocks();

    token = mockAuthenticatedUser();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('getSuppliers()', () => {
    it('returns a 401 if unauthorised', async () => {
      const res = await request(app.getHttpServer()).get('/energy/suppliers');

      expect(res.status).toEqual(401);

      expect(energyService.getSuppliers).not.toHaveBeenCalled();
    });

    it('should get the energy suppliers', async () => {
      energyService.getSuppliers.mockResolvedValue([
        {
          id: 'aad06700-9a28-4433-814a-68c1a3516c0a',
          name: 'EDF',
          timeZone: 'Europe/London',
          icon: 'https://cdn.pod-point.com/tariff-api/supplier-images/ep-edf.svg',
          defaultMaxChargePrice: 0.2,
          defaultTariffInfo: [],
        },
      ]);

      const res = await request(app.getHttpServer())
        .get('/energy/suppliers')
        .set('Authorization', `Bearer ${token}`);

      expect(res.status).toEqual(200);
      expect(res.body).toMatchSnapshot();

      expect(energyService.getSuppliers).toHaveBeenCalledTimes(1);
    });
  });
});
