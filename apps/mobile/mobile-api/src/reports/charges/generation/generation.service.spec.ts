import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { CSV_HEADERS } from '../reports.constants';
import { ChargesCsvService } from './charges.csv.service';
import { ChargesService } from '../../../charges/charges.service';
import { ConfigModule } from '@nestjs/config';
import { DriversChargesResponse } from '@experience/shared/axios/data-platform-api-client';
import {
  InternalServerErrorException,
  Logger,
  StreamableFile,
} from '@nestjs/common';
import { QueueConsumerService } from '@experience/shared/nest/aws/sqs-module';
import { ReportsGenerationService } from './generation.service';
import { ReportsMessage } from '../reports.types';
import {
  SimpleEmailService,
  getTemplateStrings,
} from '@experience/shared/nest/aws/ses-module';
import {
  TEST_DRIVER_CHARGES_RESPONSE,
  TEST_EMPTY_DRIVER_CHARGES_RESPONSE,
  TEST_MBE_51_BUG_CHARGES_RESPONSE,
} from '@experience/shared/axios/data-platform-api-client/fixtures';
import { TEST_USER_INFO_RESPONSE } from '@experience/mobile/driver-account/domain/user';
import { Test, TestingModule } from '@nestjs/testing';
import { createMock } from '@golevelup/ts-jest';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';
import { jest } from '@jest/globals';
import { mockDeep } from 'jest-mock-extended';
import { readFileContents } from './utils';
// eslint-disable-next-line @nx/enforce-module-boundaries
import es from '../../../../../../../assets/mobile-api/i18n/es/main.json';

describe('ReportsGenerationService', () => {
  let reportsGenerationService: ReportsGenerationService;
  let queueConsumerService: QueueConsumerService;
  let simpleEmailService: SimpleEmailService;
  let chargesCsvService: ChargesCsvService;
  let chargesService: ChargesService;
  const logger = mockDeep<Logger>();

  const defaultReportsMessage: ReportsMessage = {
    uid: '123',
    from: '2020-03-12',
    to: '2020-03-22',
    distances: { total: 10, business: 5 },
    organisationOnly: false,
    unitOfDistance: 'mi',
    language: 'en',
  };

  beforeAll(() => {
    TEST_DRIVER_CHARGES_RESPONSE.meta.params.userProfile =
      TEST_USER_INFO_RESPONSE;
    TEST_EMPTY_DRIVER_CHARGES_RESPONSE.meta.params.userProfile =
      TEST_USER_INFO_RESPONSE;
    TEST_MBE_51_BUG_CHARGES_RESPONSE.meta.params.userProfile =
      TEST_USER_INFO_RESPONSE;
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule,
        I18nModule.forRoot({
          fallbackLanguage: 'en',
          loaderOptions: {
            path: './assets/mobile-api/i18n/',
            watch: true,
          },
          resolvers: [
            { use: QueryResolver, options: ['lang'] },
            AcceptLanguageResolver,
          ],
        }),
      ],
      providers: [
        {
          provide: QueueConsumerService,
          useValue: { start: jest.fn(), setMessageHandler: jest.fn() },
        },
        ChargesCsvService,
        {
          provide: ChargesService,
          useValue: { getCharges: jest.fn() },
        },
        ReportsGenerationService,
        {
          provide: SimpleEmailService,
          useValue: createMock<SimpleEmailService>(),
        },
      ],
    }).compile();

    const app = module.createNestApplication();
    await app.init();

    reportsGenerationService = module.get<ReportsGenerationService>(
      ReportsGenerationService
    );
    chargesCsvService = module.get<ChargesCsvService>(ChargesCsvService);
    chargesService = module.get<ChargesService>(ChargesService);
    simpleEmailService = module.get<SimpleEmailService>(SimpleEmailService);
    queueConsumerService = module.get(QueueConsumerService);
    module.useLogger(logger);
  });

  it('Should define services', () => {
    expect(chargesCsvService).toBeDefined();
    expect(chargesService).toBeDefined();
    expect(simpleEmailService).toBeDefined();
    expect(queueConsumerService).toBeDefined();
  });

  const assertEmailHasBeenSentWithReport = async (
    chargesData: DriversChargesResponse,
    language = 'en',
    subject = 'Your report is ready'
  ) => {
    const url = process.env.IMAGES_BASE_URL;
    const filename = 'stats.csv';
    const file = new StreamableFile(Buffer.from(CSV_HEADERS.join(',')));
    const fileContent = await readFileContents(
      new StreamableFile(Buffer.from(CSV_HEADERS.join(','))),
      ''
    );
    const mockSimpleEmailService = jest
      .spyOn(simpleEmailService, 'sendEmail')
      .mockResolvedValue(true);
    jest.spyOn(chargesService, 'getCharges').mockResolvedValue(chargesData);
    jest.spyOn(chargesCsvService, 'generateCSV').mockReturnValue({
      file,
      filename,
    });
    defaultReportsMessage.language = language;
    const mockGenerateReport = {
      Body: JSON.stringify(defaultReportsMessage),
    };
    await reportsGenerationService.handleMessageAndSendEmail(
      mockGenerateReport
    );

    const { htmlTemplateString, plainTextTemplateString } = getTemplateStrings(
      `./assets/mobile-api/email-templates/${language}/send-report`
    );

    const firstName = TEST_USER_INFO_RESPONSE.first_name;
    const emailAddress = TEST_USER_INFO_RESPONSE.email;
    const params = {
      emailAddress,
      subject,
      firstName,
      imageUrl: url,
    };

    expect(mockSimpleEmailService).toHaveBeenCalledWith({
      bodyHtml: injectParametersIntoTemplateString(htmlTemplateString, {
        ...params,
      }),
      bodyText: injectParametersIntoTemplateString(plainTextTemplateString, {
        ...params,
      }),
      subject,
      to: emailAddress,
      attachment: [
        {
          filename,
          contentType: 'application/vnd',
          data: Buffer.from(fileContent).toString('base64'),
        },
      ],
    });
  };

  it('should send an email with the CSV attached to it', async () => {
    await assertEmailHasBeenSentWithReport(TEST_DRIVER_CHARGES_RESPONSE);
  });

  it('should send an email with the CSV attached to it and translate subject when the language is es', async () => {
    await assertEmailHasBeenSentWithReport(
      TEST_DRIVER_CHARGES_RESPONSE,
      'es',
      es.reportSubject
    );
  });

  it('should not send an email with the CSV attached to it for MBE-51 bug', async () => {
    await assertEmailHasBeenSentWithReport(TEST_MBE_51_BUG_CHARGES_RESPONSE);
  });

  it('should not send the email and throw internal server error', async () => {
    const mockGenerateReport = {
      Body: 'message',
    };
    await expect(
      reportsGenerationService.handleMessageAndSendEmail(mockGenerateReport)
    ).rejects.toThrow(
      new InternalServerErrorException(
        `Error in message processing SyntaxError: Unexpected token 'm', "message" is not valid JSON`
      )
    );
  });

  it('should not send an email when there is no csv generated', async () => {
    const mockSimpleEmailService = jest
      .spyOn(simpleEmailService, 'sendEmail')
      .mockResolvedValue(true);
    jest
      .spyOn(chargesService, 'getCharges')
      .mockResolvedValue(TEST_EMPTY_DRIVER_CHARGES_RESPONSE);
    jest.spyOn(chargesCsvService, 'generateCSV').mockReturnValue(undefined);
    const mockGenerateReport = {
      Body: JSON.stringify(defaultReportsMessage),
    };
    await reportsGenerationService.handleMessageAndSendEmail(
      mockGenerateReport
    );
    expect(logger.log.mock.calls[0][0]).toEqual({
      message: mockGenerateReport,
    });
    expect(logger.log.mock.calls[0][1]).toEqual(`Message about to be handled`);
    expect(logger.log.mock.calls[1][0]).toEqual({
      uid: defaultReportsMessage.uid,
      from: defaultReportsMessage.from,
      to: defaultReportsMessage.to,
    });
    expect(logger.log.mock.calls[1][1]).toEqual(
      `About to start the process of generating and sending a CSV`
    );
    expect(logger.log.mock.calls[2][0]).toEqual({
      uid: defaultReportsMessage.uid,
    });
    expect(logger.log.mock.calls[2][1]).toEqual(
      `Got about ${TEST_EMPTY_DRIVER_CHARGES_RESPONSE.data.count} number of charges for period ${defaultReportsMessage.from} - ${defaultReportsMessage.to}`
    );
    expect(logger.log.mock.calls[3][0]).toEqual({
      uid: TEST_USER_INFO_RESPONSE.uid,
    });
    expect(logger.log.mock.calls[3][1]).toEqual(
      `csv for user ${TEST_USER_INFO_RESPONSE.email} is to be generated`
    );
    expect(logger.log.mock.calls[4][0]).toEqual({
      uid: TEST_USER_INFO_RESPONSE.uid,
    });
    expect(logger.log.mock.calls[4][1]).toEqual(
      `csv <NAME_EMAIL> has been generated`
    );
    expect(logger.log.mock.calls[5][0]).toEqual({
      uid: defaultReportsMessage.uid,
    });
    expect(logger.log.mock.calls[5][1]).toEqual(
      `There are no stats for dates: ${defaultReportsMessage.from} - ${defaultReportsMessage.to} for user ${defaultReportsMessage.uid}`
    );
    expect(mockSimpleEmailService).not.toHaveBeenCalled();
  });

  it('should start listening to queue on bootstrap', () => {
    expect(queueConsumerService.setMessageHandler).toHaveBeenCalledTimes(1);
    expect(queueConsumerService.start).toHaveBeenCalledTimes(1);
  });

  it.each([
    ['override email', 'local', '<EMAIL>', '<EMAIL>'],
    ['override email', 'dev', '<EMAIL>', '<EMAIL>'],
    ['override email', 'stage', '<EMAIL>', '<EMAIL>'],
    [
      'user email',
      'prod',
      '<EMAIL>',
      TEST_USER_INFO_RESPONSE.email,
    ],
    ['user email', 'local', null, TEST_USER_INFO_RESPONSE.email],
    ['user email', 'dev', null, TEST_USER_INFO_RESPONSE.email],
    ['user email', 'stage', null, TEST_USER_INFO_RESPONSE.email],
    ['user email', 'prod', null, TEST_USER_INFO_RESPONSE.email],
  ])(
    `email is sent to %s if environment is %s and override email is %s`,
    async (_scenario, environment, overrideEmail, expectedEmail) => {
      const initialEnvironment = process.env.ENVIRONMENT;

      process.env.ENVIRONMENT = environment;

      const mockSimpleEmailService = jest
        .spyOn(simpleEmailService, 'sendEmail')
        .mockResolvedValue(true);
      jest
        .spyOn(chargesService, 'getCharges')
        .mockResolvedValue(TEST_DRIVER_CHARGES_RESPONSE);
      const mockGenerateReport = {
        Body: JSON.stringify({
          ...defaultReportsMessage,
          overrideEmailAddress: overrideEmail,
        }),
      };
      await reportsGenerationService.handleMessageAndSendEmail(
        mockGenerateReport
      );

      expect(mockSimpleEmailService).toHaveBeenCalledWith({
        bodyHtml: expect.anything(),
        bodyText: expect.anything(),
        subject: expect.anything(),
        to: expectedEmail,
        attachment: expect.anything(),
      });

      process.env.ENVIRONMENT = initialEnvironment;
    }
  );
});
