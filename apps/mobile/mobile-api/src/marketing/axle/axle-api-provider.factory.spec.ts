import { CACHE_MANAGER, CacheModule } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MetersApi } from '@experience/shared/axios/axle-api-client';
import { Test } from '@nestjs/testing';
import { createMock } from '@golevelup/ts-jest';
import { provideAxleApi } from './axle-api-provider.factory';
import axios, { InternalAxiosRequestConfig } from 'axios';

describe('provideAxleApi()', () => {
  it('returns the given Axle API as a Nest.js provider', async () => {
    const result = provideAxleApi(MetersApi);

    expect(result).toStrictEqual({
      provide: MetersApi,
      inject: [CACHE_MANAGER, ConfigService],
      useFactory: expect.any(Function),
    });

    const module = await Test.createTestingModule({
      imports: [CacheModule.register(), ConfigModule],
      providers: [result],
    }).compile();

    expect(module.get(MetersApi)).toBeInstanceOf(MetersApi);
  });

  it('uses the interceptor to refresh the access token', async () => {
    const module = await Test.createTestingModule({
      imports: [CacheModule.register(), ConfigModule],
      providers: [provideAxleApi(MetersApi)],
    }).compile();

    const axiosPostMock = jest.spyOn(axios, 'post').mockResolvedValue({
      data: {
        access_token: 'token',
      },
    });

    const interceptor =
      module.get(MetersApi)['axios'].interceptors.request['handlers'][0][
        'fulfilled'
      ];

    const config = await interceptor(createMock<InternalAxiosRequestConfig>());

    expect(axiosPostMock).toHaveBeenCalledTimes(1);
    expect(axiosPostMock).toHaveBeenCalledWith(
      'https://api.axle.energy/auth/token-form',
      {
        grant_type: 'password',
        username: 'podpoint',
        password: 'password',
      },
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    expect(config.headers['Authorization']).toEqual('Bearer token');
  });
});
