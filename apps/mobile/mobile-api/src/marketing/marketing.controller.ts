import {
  ApiForbiddenException,
  ApiUnauthorizedException,
} from '@experience/mobile/nest/swagger';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuardToken } from '../authentication/authguard.token';
import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { MarketingOpportunitiesDTO } from './marketing.types';
import { MarketingService } from './marketing.service';
import { UserUnitParamGuard } from '../authentication/user-unit-param.guard';

@ApiTags('Marketing')
@Controller('marketing')
export class MarketingController {
  constructor(private readonly marketingService: MarketingService) {}

  @Get('opportunities/:ppid')
  @UseGuards(AuthGuardToken, UserUnitParamGuard)
  @ApiOperation({
    summary: 'gets marketing opportunities for a given charger',
    description: 'For a given charger, get its marketing opportunties',
  })
  @ApiOkResponse({
    description: 'OK response',
    type: MarketingOpportunitiesDTO,
  })
  @ApiUnauthorizedException()
  @ApiForbiddenException('Charger is not linked to this account')
  async getOpportunitiesForCharger(
    @Param('ppid') ppid: string
  ): Promise<MarketingOpportunitiesDTO> {
    return this.marketingService.getOpportunitiesForCharger(ppid);
  }
}
