import { AxiosError, AxiosResponse } from 'axios';
import {
  BankAccountsApi,
  TransactionsApi,
} from '@experience/mobile/payments-api/axios';
import {
  CanNotPayoutToAnotherUsersBankAccountError,
  FailedToPayoutRewardWalletError,
  FailedToRetrieveRewardWalletTransactionsError,
  FailedToRetrieveWalletBalanceSummaryError,
  WalletBalanceNotMetPayoutThresholdError,
  WalletBalanceSummaryNotFoundError,
} from './reward-wallet.errors';
import { MOCK_BANK_ACCOUNT_API_RESPONSE_DTO } from '../payouts/__fixtures__/payouts.interface';
import {
  MOCK_HANDLE_ACTION_REWARD_PAYOUT,
  MOCK_PAYMENTS_TRANSACTION_DTO,
  MOCK_REWARDS_ACCRUAL_ACCOUNT_TRANSACTION_DTO,
  MOCK_REWARDS_PAYOUT_ACCOUNT_TRANSACTION_DTO,
  MOCK_REWARDS_REFUND_ACCOUNT_TRANSACTION_DTO,
  MOCK_REWARDS_TRANSACTIONS_HTTP_DTO,
  MOCK_REWARD_WALLET,
} from './__fixtures__/reward-wallet.interface';
import { REWARD_WALLET_PAYMENT_THRESHOLD_GBP } from './reward-wallet.constants';
import { RewardWalletService } from './reward-wallet.service';
import {
  RewardWalletsApi,
  RewardsApi,
} from '@experience/mobile/rewards-api/axios';
import { Test } from '@nestjs/testing';
import { v4 } from 'uuid';

describe('RewardWalletService', () => {
  let service: RewardWalletService;
  let rewardWalletApi: RewardWalletsApi;
  let rewardsApi: RewardsApi;
  let bankAccountsApi: BankAccountsApi;
  let transactionsApi: TransactionsApi;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        RewardWalletService,
        {
          provide: RewardWalletsApi,
          useValue: new RewardWalletsApi(),
        },
        {
          provide: RewardsApi,
          useValue: new RewardsApi(),
        },
        {
          provide: BankAccountsApi,
          useValue: new BankAccountsApi(),
        },
        {
          provide: TransactionsApi,
          useValue: new TransactionsApi(),
        },
      ],
    }).compile();

    module.useLogger(false);

    service = module.get(RewardWalletService);
    rewardWalletApi = module.get(RewardWalletsApi);
    rewardsApi = module.get(RewardsApi);
    bankAccountsApi = module.get(BankAccountsApi);
    transactionsApi = module.get(TransactionsApi);
  });

  afterEach(() => jest.resetAllMocks());

  describe(RewardWalletService.prototype.getWalletBalanceSummary, () => {
    it('throws WalletBalanceSummaryNotFound if Rewards API returns 404', async () => {
      const error = new AxiosError();
      error.status = 404;

      const getWalletBalanceSummary = jest
        .spyOn(rewardWalletApi, 'walletControllerGetWalletBalanceSummary')
        .mockRejectedValue(error);

      const userId = v4();

      await expect(service.getWalletBalanceSummary(userId)).rejects.toThrow(
        WalletBalanceSummaryNotFoundError
      );

      expect(getWalletBalanceSummary).toHaveBeenCalledTimes(1);
      expect(getWalletBalanceSummary).toHaveBeenCalledWith(userId);
    });

    it('throws FailedToRetrieveWalletBalanceSummary if Rewards API call fails', async () => {
      const error = new AxiosError();
      error.status = 500;

      const getWalletBalanceSummary = jest
        .spyOn(rewardWalletApi, 'walletControllerGetWalletBalanceSummary')
        .mockRejectedValueOnce(error);

      const userId = v4();

      await expect(service.getWalletBalanceSummary(userId)).rejects.toThrow(
        FailedToRetrieveWalletBalanceSummaryError
      );

      expect(getWalletBalanceSummary).toHaveBeenCalledTimes(1);
      expect(getWalletBalanceSummary).toHaveBeenCalledWith(userId);
    });

    it('falls back the total withdrawn amount to 0 if no results in GBP', async () => {
      const getWalletBalanceSummary = jest
        .spyOn(rewardWalletApi, 'walletControllerGetWalletBalanceSummary')
        .mockResolvedValueOnce({
          data: MOCK_REWARD_WALLET,
        } as AxiosResponse);

      const getBankAccounts = jest
        .spyOn(bankAccountsApi, 'bankAccountControllerSearchBankAccounts')
        .mockResolvedValue({
          data: [MOCK_BANK_ACCOUNT_API_RESPONSE_DTO],
        } as AxiosResponse);

      const getTransactionSummary = jest
        .spyOn(
          bankAccountsApi,
          'bankAccountControllerGetBankAccountTransactionSummary'
        )
        .mockResolvedValue({
          data: {
            totalWithdrawn: [{ currency: 'EUR', amount: 10 }],
          },
        } as AxiosResponse);

      const res = await service.getWalletBalanceSummary(
        '9de50be4-6bfd-40a4-b107-1491227c6e80'
      );

      expect(res.payments.totalWithdrawnGbp).toEqual(0);

      expect(getWalletBalanceSummary).toHaveBeenCalledTimes(1);
      expect(getWalletBalanceSummary).toHaveBeenCalledWith(
        '9de50be4-6bfd-40a4-b107-1491227c6e80'
      );

      expect(getBankAccounts).toHaveBeenCalledTimes(1);
      expect(getBankAccounts).toHaveBeenCalledWith(
        '9de50be4-6bfd-40a4-b107-1491227c6e80'
      );

      expect(getTransactionSummary).toHaveBeenCalledTimes(1);
      expect(getTransactionSummary).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.id,
        'REWARDS'
      );
    });

    it('retrieves wallet balance summary', async () => {
      const getWalletBalanceSummary = jest
        .spyOn(rewardWalletApi, 'walletControllerGetWalletBalanceSummary')
        .mockResolvedValueOnce({
          data: MOCK_REWARD_WALLET,
        } as AxiosResponse);

      const getBankAccounts = jest
        .spyOn(bankAccountsApi, 'bankAccountControllerSearchBankAccounts')
        .mockResolvedValue({
          data: [
            { ...MOCK_BANK_ACCOUNT_API_RESPONSE_DTO, id: '1' },
            { ...MOCK_BANK_ACCOUNT_API_RESPONSE_DTO, id: '2' },
            { ...MOCK_BANK_ACCOUNT_API_RESPONSE_DTO, id: '3' },
            { ...MOCK_BANK_ACCOUNT_API_RESPONSE_DTO, id: '4' },
          ],
        } as AxiosResponse);

      const getTransactionSummary = jest
        .spyOn(
          bankAccountsApi,
          'bankAccountControllerGetBankAccountTransactionSummary'
        )
        .mockResolvedValue({
          data: {
            totalWithdrawn: [{ currency: 'GBP', amount: 2.5 }],
          },
        } as AxiosResponse);

      const userId = v4();

      expect(await service.getWalletBalanceSummary(userId)).toEqual(
        MOCK_REWARD_WALLET
      );

      expect(getWalletBalanceSummary).toHaveBeenCalledTimes(1);
      expect(getWalletBalanceSummary).toHaveBeenCalledWith(userId);

      expect(getBankAccounts).toHaveBeenCalledTimes(1);
      expect(getBankAccounts).toHaveBeenCalledWith(userId);

      expect(getTransactionSummary).toHaveBeenCalledTimes(4);
      expect(getTransactionSummary).toHaveBeenCalledWith('1', 'REWARDS');
      expect(getTransactionSummary).toHaveBeenCalledWith('2', 'REWARDS');
      expect(getTransactionSummary).toHaveBeenCalledWith('3', 'REWARDS');
      expect(getTransactionSummary).toHaveBeenCalledWith('4', 'REWARDS');
    });
  });

  describe(RewardWalletService.prototype.payoutWalletBalance, () => {
    it("throws a WalletBalanceNotMetPayoutThresholdError if the user's balance is less than the threshold", async () => {
      const getWalletBalanceSummary = jest
        .spyOn(rewardWalletApi, 'walletControllerGetWalletBalanceSummary')
        .mockResolvedValue({
          data: {
            ...MOCK_REWARD_WALLET,
            rewards: {
              balanceGbp: REWARD_WALLET_PAYMENT_THRESHOLD_GBP - 1,
            },
          },
        } as AxiosResponse);

      const getBankAccountById = jest.spyOn(
        bankAccountsApi,
        'bankAccountControllerGetBankAccountById'
      );
      const handleAction = jest.spyOn(
        rewardWalletApi,
        'walletControllerHandleAction'
      );

      await expect(
        service.payoutWalletBalance(
          MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId,
          MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.id
        )
      ).rejects.toThrow(WalletBalanceNotMetPayoutThresholdError);

      expect(getWalletBalanceSummary).toHaveBeenCalledTimes(1);
      expect(getWalletBalanceSummary).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId
      );

      expect(getBankAccountById).not.toHaveBeenCalled();
      expect(handleAction).not.toHaveBeenCalled();
    });

    it('throws a CanNotPayoutToAnotherUsersBankAccountError if the user does not own the given bank account', async () => {
      const getWalletBalanceSummary = jest
        .spyOn(rewardWalletApi, 'walletControllerGetWalletBalanceSummary')
        .mockResolvedValue({
          data: MOCK_REWARD_WALLET,
        } as AxiosResponse);

      const getBankAccountById = jest
        .spyOn(bankAccountsApi, 'bankAccountControllerGetBankAccountById')
        .mockResolvedValue({
          data: {
            ...MOCK_BANK_ACCOUNT_API_RESPONSE_DTO,
            id: 'random-bank-account-id',
            userId: 'some-random-user-id',
          },
        } as AxiosResponse);

      const handleAction = jest.spyOn(
        rewardWalletApi,
        'walletControllerHandleAction'
      );

      await expect(
        service.payoutWalletBalance(
          MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId,
          'random-bank-account-id'
        )
      ).rejects.toThrow(CanNotPayoutToAnotherUsersBankAccountError);

      expect(getWalletBalanceSummary).toHaveBeenCalledTimes(1);
      expect(getWalletBalanceSummary).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId
      );

      expect(getBankAccountById).toHaveBeenCalledTimes(1);
      expect(getBankAccountById).toHaveBeenCalledWith(
        'random-bank-account-id',
        { validateStatus: expect.any(Function) }
      );

      expect(handleAction).not.toHaveBeenCalled();
    });

    it('throws a FailedToPayoutRewardWalletError if unable to trigger reward payout', async () => {
      const getWalletBalanceSummary = jest
        .spyOn(rewardWalletApi, 'walletControllerGetWalletBalanceSummary')
        .mockResolvedValue({
          data: MOCK_REWARD_WALLET,
        } as AxiosResponse);

      const getBankAccountById = jest
        .spyOn(bankAccountsApi, 'bankAccountControllerGetBankAccountById')
        .mockResolvedValue({
          data: MOCK_BANK_ACCOUNT_API_RESPONSE_DTO,
        } as AxiosResponse);

      const handleAction = jest
        .spyOn(rewardWalletApi, 'walletControllerHandleAction')
        .mockRejectedValue(new Error());

      await expect(
        service.payoutWalletBalance(
          MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId,
          MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.id
        )
      ).rejects.toThrow(FailedToPayoutRewardWalletError);

      expect(getWalletBalanceSummary).toHaveBeenCalledTimes(1);
      expect(getWalletBalanceSummary).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId
      );

      expect(getBankAccountById).toHaveBeenCalledTimes(1);
      expect(getBankAccountById).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.id,
        { validateStatus: expect.any(Function) }
      );

      expect(handleAction).toHaveBeenCalledTimes(1);
      expect(handleAction).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId,
        {
          action: 'REWARD_PAYOUT',
          bankAccountId: MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.id,
        }
      );
    });

    it('pays out wallet balance successfully', async () => {
      const getWalletBalanceSummary = jest
        .spyOn(rewardWalletApi, 'walletControllerGetWalletBalanceSummary')
        .mockResolvedValue({
          data: MOCK_REWARD_WALLET,
        } as AxiosResponse);

      const getBankAccountById = jest
        .spyOn(bankAccountsApi, 'bankAccountControllerGetBankAccountById')
        .mockResolvedValue({
          data: MOCK_BANK_ACCOUNT_API_RESPONSE_DTO,
        } as AxiosResponse);

      const handleAction = jest
        .spyOn(rewardWalletApi, 'walletControllerHandleAction')
        .mockResolvedValue({
          data: MOCK_HANDLE_ACTION_REWARD_PAYOUT,
        } as AxiosResponse);

      const res = await service.payoutWalletBalance(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId,
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.id
      );

      expect(res).toBeUndefined();

      expect(getWalletBalanceSummary).toHaveBeenCalledTimes(1);
      expect(getWalletBalanceSummary).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId
      );

      expect(getBankAccountById).toHaveBeenCalledTimes(1);
      expect(getBankAccountById).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.id,
        { validateStatus: expect.any(Function) }
      );

      expect(handleAction).toHaveBeenCalledTimes(1);
      expect(handleAction).toHaveBeenCalledWith(
        MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.userId,
        {
          action: 'REWARD_PAYOUT',
          bankAccountId: MOCK_BANK_ACCOUNT_API_RESPONSE_DTO.id,
        }
      );
    });
  });

  describe(RewardWalletService.prototype.getRewardWalletTransactions, () => {
    it('throws a FailedToRetrieveRewardWalletTransactionsError if reward transactions cannot be retrieved', async () => {
      jest
        .spyOn(rewardsApi, 'rewardsControllerGetRewardsTransactions')
        .mockRejectedValueOnce(new Error());

      await expect(
        service.getRewardWalletTransactions(v4(), 10)
      ).rejects.toThrow(FailedToRetrieveRewardWalletTransactionsError);
    });

    it('does not include PAYOUT or PAYOUT_REFUNDED transactions for which we are unable to obtain a payment', async () => {
      jest
        .spyOn(rewardsApi, 'rewardsControllerGetRewardsTransactions')
        .mockResolvedValue({
          data: MOCK_REWARDS_TRANSACTIONS_HTTP_DTO,
        } as AxiosResponse);

      jest
        .spyOn(transactionsApi, 'transactionsControllerSearchTransactions')
        .mockResolvedValue({
          data: [],
        } as AxiosResponse);

      jest
        .spyOn(transactionsApi, 'transactionsControllerGetTransactionsById')
        .mockRejectedValueOnce(new Error());

      const result = await service.getRewardWalletTransactions(v4(), 10);

      expect(result).toEqual({
        transactions: [
          {
            type: 'MILES_CHARGED',
            amount: MOCK_REWARDS_ACCRUAL_ACCOUNT_TRANSACTION_DTO.amount,
            chargeId:
              MOCK_REWARDS_ACCRUAL_ACCOUNT_TRANSACTION_DTO.metadata['chargeId'],
            timestamp: MOCK_REWARDS_ACCRUAL_ACCOUNT_TRANSACTION_DTO.date,
          },
        ],
        meta: MOCK_REWARDS_TRANSACTIONS_HTTP_DTO.meta,
      });
    });

    it('retrieve reward wallet transactions', async () => {
      jest
        .spyOn(rewardsApi, 'rewardsControllerGetRewardsTransactions')
        .mockResolvedValue({
          data: MOCK_REWARDS_TRANSACTIONS_HTTP_DTO,
        } as AxiosResponse);

      jest
        .spyOn(transactionsApi, 'transactionsControllerSearchTransactions')
        .mockResolvedValue({
          data: [MOCK_PAYMENTS_TRANSACTION_DTO],
        } as AxiosResponse);

      jest
        .spyOn(transactionsApi, 'transactionsControllerGetTransactionsById')
        .mockResolvedValue({
          data: MOCK_PAYMENTS_TRANSACTION_DTO,
        } as AxiosResponse);

      const result = await service.getRewardWalletTransactions(v4(), 10);

      expect(result).toEqual({
        transactions: [
          {
            type: 'MILES_CHARGED',
            amount: MOCK_REWARDS_ACCRUAL_ACCOUNT_TRANSACTION_DTO.amount,
            chargeId:
              MOCK_REWARDS_ACCRUAL_ACCOUNT_TRANSACTION_DTO.metadata['chargeId'],
            timestamp: MOCK_REWARDS_ACCRUAL_ACCOUNT_TRANSACTION_DTO.date,
          },
          {
            type: 'PAYOUT',
            amount: {
              miles: MOCK_REWARDS_PAYOUT_ACCOUNT_TRANSACTION_DTO.amount,
              gbp: MOCK_PAYMENTS_TRANSACTION_DTO.amount,
            },
            status: 'SENT',
            timestamp: MOCK_REWARDS_PAYOUT_ACCOUNT_TRANSACTION_DTO.date,
            bankAccountId: MOCK_PAYMENTS_TRANSACTION_DTO.bankAccountId,
            transactionId: MOCK_PAYMENTS_TRANSACTION_DTO.id,
          },
          {
            type: 'PAYOUT_REFUNDED',
            amount: {
              miles: MOCK_REWARDS_REFUND_ACCOUNT_TRANSACTION_DTO.amount,
              gbp: MOCK_PAYMENTS_TRANSACTION_DTO.amount,
            },
            timestamp: MOCK_REWARDS_REFUND_ACCOUNT_TRANSACTION_DTO.date,
            bankAccountId: MOCK_PAYMENTS_TRANSACTION_DTO.bankAccountId,
            transactionId: MOCK_PAYMENTS_TRANSACTION_DTO.id,
          },
        ],
        meta: MOCK_REWARDS_TRANSACTIONS_HTTP_DTO.meta,
      });
    });
  });
});
