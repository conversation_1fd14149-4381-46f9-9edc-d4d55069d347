import { HttpInterceptor } from '@experience/shared/nest/utils';
import { HttpStatus, Injectable } from '@nestjs/common';

import { AccountErrorCodes } from './installs.errors';
import {
  IncompleteProfilePayloadException,
  InvalidAuthUserException,
} from './installs.exception';

@Injectable()
export class InstallsInterceptor extends HttpInterceptor {
  constructor() {
    super([
      {
        name: IncompleteProfilePayloadException,
        code: AccountErrorCodes.INCOMPLETE_PROFILE_PAYLOAD,
        statusCode: HttpStatus.BAD_REQUEST,
      },
      {
        name: InvalidAuthUserException,
        code: AccountErrorCodes.INVALID_AUTH_USER_PAYLOAD,
        statusCode: HttpStatus.UNAUTHORIZED,
      },
    ]);
  }
}
