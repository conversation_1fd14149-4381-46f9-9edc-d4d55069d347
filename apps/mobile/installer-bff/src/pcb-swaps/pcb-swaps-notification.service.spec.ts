import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import {
  AccountApi,
  PCBSwapsApi,
  PcbSwapDto,
} from '@experience/installer/api/axios';
import { AxiosResponse } from 'axios';
import { ClsService } from 'nestjs-cls';
import { ConfigService } from '@nestjs/config';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import {
  ExtendedUserInfoResponseDto,
  UsersApi,
} from '@experience/driver-account-api/api-client';
import { PCBSwapsNotificationService } from './pcb-swaps-notification.service';
import { SimpleEmailService } from '@experience/shared/nest/aws/ses-module';
import { Test } from '@nestjs/testing';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';
import dayjs from 'dayjs';

jest.mock('@experience/shared/typescript/utils', () => {
  const originalModule = jest.requireActual(
    '@experience/shared/typescript/utils'
  );

  return {
    ...originalModule,
    injectParametersIntoTemplateString: jest
      .fn()
      .mockImplementation((template, params) =>
        originalModule.injectParametersIntoTemplateString(template, params)
      ),
  };
});

describe('PCBSwapsNotificationService', () => {
  let service: PCBSwapsNotificationService;
  let internalPcbSwapsApi: DeepMocked<PCBSwapsApi>;
  let simpleEmailService: DeepMocked<SimpleEmailService>;
  let driverAccountApi: DeepMocked<UsersApi>;
  let installerAccountApi: DeepMocked<AccountApi>;
  let clsService: DeepMocked<ClsService>;
  let configService: DeepMocked<ConfigService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        I18nModule.forRoot({
          fallbackLanguage: 'en',
          loaderOptions: {
            path: './assets/installer-bff/i18n/',
            watch: true,
          },
          resolvers: [
            { use: QueryResolver, options: ['lang'] },
            AcceptLanguageResolver,
          ],
        }),
      ],
      providers: [
        PCBSwapsNotificationService,
        {
          provide: PCBSwapsApi,
          useValue: createMock<PCBSwapsApi>(),
        },
        {
          provide: UsersApi,
          useValue: createMock<UsersApi>(),
        },
        {
          provide: AccountApi,
          useValue: createMock<AccountApi>(),
        },
        {
          provide: SimpleEmailService,
          useValue: createMock<SimpleEmailService>(),
        },
        {
          provide: ClsService,
          useValue: createMock<ClsService>(),
        },
        {
          provide: ConfigService,
          useValue: createMock<ConfigService>(),
        },
      ],
    }).compile();

    service = module.get(PCBSwapsNotificationService);
    internalPcbSwapsApi = module.get(PCBSwapsApi);
    driverAccountApi = module.get(UsersApi);
    installerAccountApi = module.get(AccountApi);
    simpleEmailService = module.get(SimpleEmailService);
    clsService = module.get(ClsService);
    configService = module.get(ConfigService);
  });

  afterEach(() => jest.clearAllMocks());

  describe('sendDomesticEmails()', () => {
    it('gets the PCB swaps which need emails sent for', async () => {
      driverAccountApi.userControllerGetByFilter.mockResolvedValue({
        data: [],
      } as AxiosResponse);

      internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps.mockResolvedValue({
        status: 200,
        data: new Array<PcbSwapDto>(10).fill({
          ppid: 'PSL-123456',
          serialNumber: '**********',
          status: 'success',
          changedAt: '2024-09-25T10:40:00.000Z',
          emailedAt: null,
        }),
      } as AxiosResponse);

      const subtractSpy = jest.spyOn(dayjs.prototype, 'subtract');
      const dateSpy = jest.spyOn(Date.prototype, 'toISOString');

      await service.sendDomesticEmails();

      expect(subtractSpy).toHaveBeenCalledWith(1, 'day');

      expect(
        internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps
      ).toHaveBeenCalledTimes(1);
      expect(
        internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps
      ).toHaveBeenCalledWith('null', dateSpy.mock.results[0].value);
    });

    it('does not send the email if a user can not be found', async () => {
      internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps.mockResolvedValueOnce(
        {
          status: 200,
          data: [
            {
              ppid: 'PSL-123456',
              serialNumber: '**********',
              status: 'success',
              changedAt: '2024-09-25T10:40:00.000Z',
              emailedAt: null,
            },
          ],
        } as AxiosResponse
      );

      driverAccountApi.userControllerGetByFilter.mockResolvedValue({
        data: [],
      } as AxiosResponse);

      await service.sendDomesticEmails();

      expect(driverAccountApi.userControllerGetByFilter).toHaveBeenCalledTimes(
        1
      );
      expect(driverAccountApi.userControllerGetByFilter).toHaveBeenCalledWith(
        'PSL-123456'
      );

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();
    });

    it('does not update if the email fails to send', async () => {
      driverAccountApi.userControllerGetByFilter.mockResolvedValue({
        data: <ExtendedUserInfoResponseDto[]>[
          {
            email: '<EMAIL>',
            first_name: 'Example',
            locale: 'en',
          },
        ],
      } as AxiosResponse);

      internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps.mockResolvedValueOnce(
        {
          status: 200,
          data: [
            {
              ppid: 'PSL-123456',
              serialNumber: '**********',
              status: 'success',
              changedAt: '2024-09-25T10:40:00.000Z',
              emailedAt: null,
            },
          ],
        } as AxiosResponse
      );

      simpleEmailService.sendEmail.mockImplementation(() => {
        throw new Error();
      });

      configService.get.mockReturnValue('installer-identity.pod-point.com');

      await service.sendDomesticEmails();

      expect(injectParametersIntoTemplateString).toHaveBeenCalledTimes(2);
      expect(injectParametersIntoTemplateString).toHaveBeenCalledWith(
        expect.any(String),
        {
          imageUrl: 'installer-identity.pod-point.com',
          emailAddress: '<EMAIL>',
          customerName: 'Example',
        }
      );

      expect(simpleEmailService.sendEmail).toHaveBeenCalledTimes(1);
      expect(simpleEmailService.sendEmail).toHaveBeenCalledWith({
        bodyHtml: (injectParametersIntoTemplateString as jest.Mock).mock
          .results[0].value,
        bodyText: (injectParametersIntoTemplateString as jest.Mock).mock
          .results[1].value,
        subject: 'Maintenance Complete',
        to: '<EMAIL>',
      });

      expect(
        internalPcbSwapsApi.pcbSwapsControllerUpdatePcbSwap
      ).not.toHaveBeenCalled();
    });

    it('sends an email and update', async () => {
      driverAccountApi.userControllerGetByFilter.mockResolvedValue({
        data: <ExtendedUserInfoResponseDto[]>[
          {
            email: '<EMAIL>',
            first_name: 'Example',
          },
        ],
      } as AxiosResponse);

      internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps.mockResolvedValueOnce(
        {
          status: 200,
          data: new Array<PcbSwapDto>(10).fill({
            ppid: 'PSL-123456',
            serialNumber: '**********',
            status: 'success',
            changedAt: '2024-09-25T10:40:00.000Z',
            emailedAt: null,
          }),
        } as AxiosResponse
      );

      configService.get.mockReturnValue('installer-identity.pod-point.com');

      await service.sendDomesticEmails();

      expect(
        internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps
      ).toHaveBeenCalledTimes(1);
      expect(
        internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps
      ).toHaveBeenCalledWith('null', expect.any(String));
      expect(
        internalPcbSwapsApi.pcbSwapsControllerGetAllPcbSwaps
      ).toHaveBeenCalledWith('null', expect.any(String));

      expect(
        internalPcbSwapsApi.pcbSwapsControllerUpdatePcbSwap
      ).toHaveBeenCalledTimes(10);
      expect(
        internalPcbSwapsApi.pcbSwapsControllerUpdatePcbSwap
      ).toHaveBeenCalledWith('PSL-123456', '**********', {
        emailedAt: expect.any(String),
      });
    });
  });

  describe('sendInstallerEmail', () => {
    it('does nothing if there is no installer found', async () => {
      clsService.get.mockReturnValue(undefined);

      installerAccountApi.accountControllerGetProfile.mockResolvedValue(null);

      await service.sendInstallerEmail('PSL-123456');

      expect(
        installerAccountApi.accountControllerGetProfile
      ).not.toHaveBeenCalled();
      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();
    });

    it('does not send an email if the installer is not returned from the users api', async () => {
      clsService.get.mockReturnValue({
        uid: 'd0fd73f5-3589-4605-915d-4480ef6d2092',
      });

      installerAccountApi.accountControllerGetProfile.mockRejectedValue({
        status: 404,
      });

      await service.sendInstallerEmail('PSL-123456');

      expect(
        installerAccountApi.accountControllerGetProfile
      ).toHaveBeenCalledTimes(1);
      expect(
        installerAccountApi.accountControllerGetProfile
      ).toHaveBeenCalledWith(clsService.get.mock.results[0].value.uid);

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();
    });

    it('sends the email', async () => {
      clsService.get.mockImplementation(
        (key) =>
          ({
            user: {
              uid: 'd0fd73f5-3589-4605-915d-4480ef6d2092',
              email: '<EMAIL>',
            },
            language: 'en-GB',
          }[key])
      );

      installerAccountApi.accountControllerGetProfile.mockResolvedValue({
        status: 200,
        data: {
          firstName: 'Example',
        },
      } as AxiosResponse);

      configService.get.mockReturnValue('installer-identity.pod-point.com');

      await service.sendInstallerEmail('PSL-123456');

      expect(configService.get).toHaveBeenCalledTimes(1);
      expect(configService.get).toHaveBeenCalledWith(
        'IDENTITY_BASE_URL_INSTALLER'
      );

      expect(
        installerAccountApi.accountControllerGetProfile
      ).toHaveBeenCalledTimes(1);
      expect(
        installerAccountApi.accountControllerGetProfile
      ).toHaveBeenCalledWith(clsService.get.mock.results[0].value.uid);

      expect(simpleEmailService.sendEmail).toHaveBeenCalledTimes(1);
      expect(simpleEmailService.sendEmail).toHaveBeenCalledWith({
        bodyHtml: (injectParametersIntoTemplateString as jest.Mock).mock
          .results[0].value,
        bodyText: (injectParametersIntoTemplateString as jest.Mock).mock
          .results[1].value,
        subject: 'PCB Swap Complete',
        to: clsService.get.mock.results[0].value.email,
      });
    });
  });
});
